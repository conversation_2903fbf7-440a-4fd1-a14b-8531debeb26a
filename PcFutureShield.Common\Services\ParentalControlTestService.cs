#nullable enable

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Test service to verify parental control blocking functionality
    /// </summary>
    public class ParentalControlTestService
    {
        private readonly ParentalControlService _parentalControl;
        private readonly DnsFilteringService _dnsFilter;
        private readonly HttpClient _httpClient;

        public ParentalControlTestService()
        {
            _parentalControl = new ParentalControlService();
            _dnsFilter = new DnsFilteringService();
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
        }

        /// <summary>
        /// Test if parental control blocking is working
        /// </summary>
        public async Task<ParentalControlTestResult> TestBlockingAsync()
        {
            var result = new ParentalControlTestResult();
            
            try
            {
                // Test 1: Enable network blocking
                result.NetworkBlockingEnabled = await _parentalControl.EnableNetworkBlockingAsync();
                
                // Test 2: Check if DNS filtering is active
                result.DnsFilteringActive = await _dnsFilter.IsFilteringActiveAsync();
                
                // Test 3: Test adult content blocking
                result.AdultContentBlocked = await TestCategoryBlockingAsync("adult");
                
                // Test 4: Test gambling site blocking
                result.GamblingBlocked = await TestCategoryBlockingAsync("gambling");
                
                // Test 5: Test custom domain blocking
                result.CustomDomainBlocked = await TestCustomDomainBlockingAsync();
                
                // Test 6: Verify hosts file modification
                result.HostsFileModified = await CheckHostsFileModificationAsync();
                
                // Calculate overall success
                result.OverallSuccess = result.NetworkBlockingEnabled && 
                                      result.DnsFilteringActive && 
                                      result.AdultContentBlocked && 
                                      result.GamblingBlocked &&
                                      result.HostsFileModified;
                
                result.TestCompleted = true;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.TestCompleted = false;
            }
            
            return result;
        }

        /// <summary>
        /// Test blocking for a specific category
        /// </summary>
        private async Task<bool> TestCategoryBlockingAsync(string category)
        {
            try
            {
                // Block the category
                var blockSuccess = await _parentalControl.BlockCategoryAsync(category);
                if (!blockSuccess) return false;
                
                // Get test domains for the category
                var testDomains = GetTestDomainsForCategory(category);
                
                // Test if domains are blocked (should fail to resolve or connect)
                foreach (var domain in testDomains)
                {
                    try
                    {
                        var response = await _httpClient.GetAsync($"http://{domain}");
                        // If we get here, the domain wasn't blocked
                        return false;
                    }
                    catch (HttpRequestException)
                    {
                        // Good! The request failed, indicating blocking is working
                        continue;
                    }
                    catch (TaskCanceledException)
                    {
                        // Timeout is also good - indicates blocking
                        continue;
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Category blocking test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test custom domain blocking
        /// </summary>
        private async Task<bool> TestCustomDomainBlockingAsync()
        {
            try
            {
                var testDomain = "test-blocked-site.com";
                
                // Block the domain
                var blockSuccess = await _parentalControl.BlockDomainAsync(testDomain);
                if (!blockSuccess) return false;
                
                // Try to access the domain (should fail)
                try
                {
                    var response = await _httpClient.GetAsync($"http://{testDomain}");
                    return false; // If we get here, blocking failed
                }
                catch (HttpRequestException)
                {
                    return true; // Good! Request failed
                }
                catch (TaskCanceledException)
                {
                    return true; // Timeout indicates blocking
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Custom domain blocking test failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if hosts file has been modified
        /// </summary>
        private async Task<bool> CheckHostsFileModificationAsync()
        {
            try
            {
                var hostsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers", "etc", "hosts");
                
                if (!File.Exists(hostsPath)) return false;
                
                var content = await File.ReadAllTextAsync(hostsPath);
                return content.Contains("# PcFutureShield DNS Filter");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Hosts file check failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get test domains for a category
        /// </summary>
        private List<string> GetTestDomainsForCategory(string category)
        {
            return category.ToLower() switch
            {
                "adult" => new List<string> { "pornhub.com", "xvideos.com" },
                "gambling" => new List<string> { "bet365.com", "pokerstars.com" },
                "social" => new List<string> { "tiktok.com", "snapchat.com" },
                _ => new List<string>()
            };
        }

        /// <summary>
        /// Generate a detailed test report
        /// </summary>
        public string GenerateTestReport(ParentalControlTestResult result)
        {
            var report = "=== PARENTAL CONTROL BLOCKING TEST REPORT ===\n\n";
            
            report += $"Test Completed: {(result.TestCompleted ? "✅ YES" : "❌ NO")}\n";
            report += $"Overall Success: {(result.OverallSuccess ? "✅ PASS" : "❌ FAIL")}\n\n";
            
            report += "Individual Test Results:\n";
            report += $"• Network Blocking Enabled: {(result.NetworkBlockingEnabled ? "✅ PASS" : "❌ FAIL")}\n";
            report += $"• DNS Filtering Active: {(result.DnsFilteringActive ? "✅ PASS" : "❌ FAIL")}\n";
            report += $"• Adult Content Blocked: {(result.AdultContentBlocked ? "✅ PASS" : "❌ FAIL")}\n";
            report += $"• Gambling Sites Blocked: {(result.GamblingBlocked ? "✅ PASS" : "❌ FAIL")}\n";
            report += $"• Custom Domain Blocked: {(result.CustomDomainBlocked ? "✅ PASS" : "❌ FAIL")}\n";
            report += $"• Hosts File Modified: {(result.HostsFileModified ? "✅ PASS" : "❌ FAIL")}\n\n";
            
            if (!string.IsNullOrEmpty(result.Error))
            {
                report += $"Error: {result.Error}\n\n";
            }
            
            if (result.OverallSuccess)
            {
                report += "🎉 PARENTAL CONTROL IS WORKING CORRECTLY!\n";
                report += "Adult sites, gambling sites, and other inappropriate content should now be blocked.\n";
            }
            else
            {
                report += "⚠️ PARENTAL CONTROL NEEDS ATTENTION!\n";
                report += "Some blocking features may not be working properly.\n";
                report += "Please run the application as Administrator for full functionality.\n";
            }
            
            return report;
        }

        /// <summary>
        /// Disable all blocking for testing cleanup
        /// </summary>
        public async Task<bool> DisableAllBlockingAsync()
        {
            try
            {
                await _parentalControl.DisableNetworkBlockingAsync();
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to disable blocking: {ex.Message}");
                return false;
            }
        }
    }

    /// <summary>
    /// Test result for parental control functionality
    /// </summary>
    public class ParentalControlTestResult
    {
        public bool TestCompleted { get; set; }
        public bool OverallSuccess { get; set; }
        public bool NetworkBlockingEnabled { get; set; }
        public bool DnsFilteringActive { get; set; }
        public bool AdultContentBlocked { get; set; }
        public bool GamblingBlocked { get; set; }
        public bool CustomDomainBlocked { get; set; }
        public bool HostsFileModified { get; set; }
        public string Error { get; set; } = string.Empty;
        public DateTime TestTime { get; set; } = DateTime.UtcNow;
    }
}
