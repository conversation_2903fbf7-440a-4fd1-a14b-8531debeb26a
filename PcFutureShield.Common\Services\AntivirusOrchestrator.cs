#nullable enable

using System;
using Microsoft.Extensions.Logging;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Text.Json;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Master antivirus orchestrator that coordinates all detection services
    /// </summary>
    public class AntivirusOrchestrator
    {
        private readonly ZeroDayDetectionService _zeroDayDetection;
        private readonly AdvancedAIDetectionService _aiDetection;
        private readonly BehavioralAnalysisService _behavioralAnalysis;
        private readonly ThreatIntelligenceService _threatIntelligence;
        private readonly string _scanResultsPath;
        private readonly Microsoft.Extensions.Logging.ILogger<AntivirusOrchestrator> _logger;
        private readonly Dictionary<string, ScanResult> _scanHistory;

        public AntivirusOrchestrator(
            ZeroDayDetectionService zeroDayDetection,
            AdvancedAIDetectionService aiDetection,
            BehavioralAnalysisService behavioralAnalysis,
            ThreatIntelligenceService threatIntelligence,
            Microsoft.Extensions.Logging.ILogger<AntivirusOrchestrator> logger)
        {
            _zeroDayDetection = zeroDayDetection;
            _aiDetection = aiDetection;
            _behavioralAnalysis = behavioralAnalysis;
            _threatIntelligence = threatIntelligence;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var resultsPath = Path.Combine(appData, "PcFutureShield", "ScanResults");
            Directory.CreateDirectory(resultsPath);
            _scanResultsPath = Path.Combine(resultsPath, "scan_history.json");

            _scanHistory = LoadScanHistory();
        }

        public async Task<ComprehensiveScanResult> PerformComprehensiveScanAsync(string filePath)
        {
            var result = new ComprehensiveScanResult
            {
                FilePath = filePath,
                ScanId = Guid.NewGuid().ToString(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Parallel execution of all detection services
                // Start async detection tasks directly and await them in parallel
                var zeroDayTask = _zeroDayDetection.AnalyzeFileAsync(filePath);
                var aiTask = _aiDetection.AnalyzeFileAsync(filePath);
                var intelTask = EnrichThreatIntelAsync(filePath);

                await Task.WhenAll(zeroDayTask, aiTask, intelTask).ConfigureAwait(false);

                // assign results after tasks complete
                result.ZeroDayResult = await zeroDayTask.ConfigureAwait(false);
                result.AIResult = await aiTask.ConfigureAwait(false);
                result.ThreatIntelResult = await intelTask.ConfigureAwait(false);

                // Calculate overall threat score
                result.OverallThreatScore = CalculateOverallThreatScore(result);

                // Determine final verdict
                result.IsThreat = DetermineFinalVerdict(result);
                result.Confidence = CalculateOverallConfidence(result);

                // Generate comprehensive report
                result.ScanReport = GenerateComprehensiveReport(result);

                // Generate remediation recommendations
                result.Remediations = GenerateRemediationRecommendations(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.IsThreat = false;
            }

            result.EndTime = DateTime.UtcNow;

            // Save to scan history
            _scanHistory[result.ScanId] = new ScanResult
            {
                ScanId = result.ScanId,
                FilePath = result.FilePath,
                IsThreat = result.IsThreat,
                ThreatScore = result.OverallThreatScore,
                ScanTime = result.StartTime,
                DetectionEngine = "Comprehensive"
            };
            SaveScanHistory();

            return result;
        }

        private async Task<ThreatIntelligenceResult?> EnrichThreatIntelAsync(string filePath)
        {
            try
            {
                // Compute hash synchronously (fast I/O) but call into async checker
                var sha256 = HashingService.ComputeSHA256(filePath);
                return await _threatIntelligence.CheckHashAsync(sha256).ConfigureAwait(false);
            }
            catch
            {
                return null;
            }
        }

        public async Task<ComprehensiveScanResult> PerformProcessScanAsync(Process process)
        {
            var result = new ComprehensiveScanResult
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                ScanId = Guid.NewGuid().ToString(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Parallel execution for process analysis
                // Run process-level detection tasks in parallel (start tasks and await)
                var zeroDayProcTask = _zeroDayDetection.AnalyzeProcessAsync(process).ContinueWith(t => result.ZeroDayResult = t.Result);
                var behavioralTask = _behavioralAnalysis.AnalyzeProcessAsync(process).ContinueWith(t => result.BehavioralResult = t.Result);
                await Task.WhenAll(zeroDayProcTask, behavioralTask).ConfigureAwait(false);

                // Calculate overall threat score
                result.OverallThreatScore = CalculateProcessThreatScore(result);

                // Determine final verdict
                result.IsThreat = DetermineProcessVerdict(result);
                result.Confidence = CalculateProcessConfidence(result);

                // Generate comprehensive report
                result.ScanReport = GenerateProcessReport(result);

                // Generate remediation recommendations
                result.Remediations = GenerateProcessRemediations(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.IsThreat = false;
            }

            result.EndTime = DateTime.UtcNow;

            // Save to scan history
            _scanHistory[result.ScanId] = new ScanResult
            {
                ScanId = result.ScanId,
                ProcessId = result.ProcessId,
                ProcessName = result.ProcessName,
                IsThreat = result.IsThreat,
                ThreatScore = result.OverallThreatScore,
                ScanTime = result.StartTime,
                DetectionEngine = "Process"
            };
            SaveScanHistory();

            return result;
        }

        public async Task<SystemScanResult> PerformSystemScanAsync()
        {
            var result = new SystemScanResult
            {
                ScanId = Guid.NewGuid().ToString(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Get all running processes
                var processes = Process.GetProcesses();
                result.TotalProcessesScanned = processes.Length;

                // Scan each process
                var processTasks = processes.Select(async proc =>
                {
                    try
                    {
                        var scanResult = await PerformProcessScanAsync(proc);
                        return scanResult;
                    }
                    catch
                    {
                        return null;
                    }
                });

                var processResults = await Task.WhenAll(processTasks);
                result.ProcessResults = processResults.Where(r => r != null).ToList()!;

                // Calculate system-wide statistics
                result.TotalThreatsDetected = result.ProcessResults.Count(r => r.IsThreat);
                result.HighRiskProcesses = result.ProcessResults.Count(r => r.OverallThreatScore > 0.7);
                result.MediumRiskProcesses = result.ProcessResults.Count(r => r.OverallThreatScore > 0.4 && r.OverallThreatScore <= 0.7);
                result.LowRiskProcesses = result.ProcessResults.Count(r => r.OverallThreatScore > 0.2 && r.OverallThreatScore <= 0.4);

                // Generate system health score
                result.SystemHealthScore = CalculateSystemHealthScore(result);

                // Generate system report
                result.SystemReport = GenerateSystemReport(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        public async Task<ComprehensiveScanResult> PerformURLScanAsync(string url)
        {
            var result = new ComprehensiveScanResult
            {
                URL = url,
                ScanId = Guid.NewGuid().ToString(),
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Check URL against threat intelligence
                result.ThreatIntelResult = await _threatIntelligence.CheckDomainAsync(url);

                // Calculate URL threat score
                result.OverallThreatScore = result.ThreatIntelResult?.IsMalicious == true ? 0.8 : 0.1;

                // Determine verdict
                result.IsThreat = result.ThreatIntelResult?.IsMalicious ?? false;
                result.Confidence = result.ThreatIntelResult?.IsMalicious == true ? 0.9 : 0.5;

                // Generate URL report
                result.ScanReport = GenerateURLReport(result);

                // Generate URL remediations
                result.Remediations = GenerateURLRemediations(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.IsThreat = false;
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        public ScanStatistics GetScanStatistics()
        {
            var stats = new ScanStatistics
            {
                TotalScans = _scanHistory.Count,
                ThreatsDetected = _scanHistory.Values.Count(s => s.IsThreat),
                LastScanTime = _scanHistory.Values.Max(s => s.ScanTime),
                DetectionRate = _scanHistory.Count > 0 ?
                    (double)_scanHistory.Values.Count(s => s.IsThreat) / _scanHistory.Count : 0,
                AverageThreatScore = _scanHistory.Values.Average(s => s.ThreatScore)
            };

            return stats;
        }

        public List<ScanResult> GetRecentScans(int count = 10)
        {
            return _scanHistory.Values
                .OrderByDescending(s => s.ScanTime)
                .Take(count)
                .ToList();
        }

        private double CalculateOverallThreatScore(ComprehensiveScanResult result)
        {
            var scores = new List<double>();

            if (result.ZeroDayResult != null)
                scores.Add(result.ZeroDayResult.ZeroDayScore);

            if (result.AIResult != null)
                scores.Add(result.AIResult.ThreatScore);

            if (result.ThreatIntelResult != null && result.ThreatIntelResult.IsMalicious)
                scores.Add(0.8);

            if (result.BehavioralResult != null)
                scores.Add(result.BehavioralResult.BehavioralScore);

            return scores.Count > 0 ? scores.Average() : 0.0;
        }

        private double CalculateProcessThreatScore(ComprehensiveScanResult result)
        {
            var scores = new List<double>();

            if (result.ZeroDayResult != null)
                scores.Add(result.ZeroDayResult.ZeroDayScore);

            if (result.AIResult != null)
                scores.Add(result.AIResult.ThreatScore);

            if (result.BehavioralResult != null)
                scores.Add(result.BehavioralResult.BehavioralScore);

            return scores.Count > 0 ? scores.Average() : 0.0;
        }

        private bool DetermineFinalVerdict(ComprehensiveScanResult result)
        {
            // Threat if any service detects with high confidence
            if (result.ZeroDayResult?.IsPotentialZeroDay == true) return true;
            if (result.AIResult?.IsMalicious == true && result.AIResult.Confidence > 0.7) return true;
            if (result.ThreatIntelResult?.IsMalicious == true) return true;
            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.Medium) return true;

            // Overall score threshold
            return result.OverallThreatScore > 0.6;
        }

        private bool DetermineProcessVerdict(ComprehensiveScanResult result)
        {
            if (result.ZeroDayResult?.IsPotentialZeroDay == true) return true;
            if (result.AIResult?.IsMalicious == true && result.AIResult.Confidence > 0.6) return true;
            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.Medium) return true;

            return result.OverallThreatScore > 0.5;
        }

        private double CalculateOverallConfidence(ComprehensiveScanResult result)
        {
            var confidences = new List<double>();

            if (result.ZeroDayResult != null)
                confidences.Add(result.ZeroDayResult.ZeroDayScore);

            if (result.AIResult != null)
                confidences.Add(result.AIResult.Confidence);

            if (result.ThreatIntelResult != null)
                confidences.Add(result.ThreatIntelResult.IsMalicious ? 0.9 : 0.1);

            return confidences.Count > 0 ? confidences.Average() : 0.5;
        }

        private double CalculateProcessConfidence(ComprehensiveScanResult result)
        {
            var confidences = new List<double>();

            if (result.ZeroDayResult != null)
                confidences.Add(result.ZeroDayResult.ZeroDayScore);

            if (result.AIResult != null)
                confidences.Add(result.AIResult.Confidence);

            if (result.BehavioralResult != null)
                confidences.Add(result.BehavioralResult.BehavioralScore);

            return confidences.Count > 0 ? confidences.Average() : 0.5;
        }

        private double CalculateSystemHealthScore(SystemScanResult result)
        {
            if (result.TotalProcessesScanned == 0) return 1.0;

            var threatRatio = (double)result.TotalThreatsDetected / result.TotalProcessesScanned;
            var highRiskRatio = (double)result.HighRiskProcesses / result.TotalProcessesScanned;

            // Health score decreases with threats and high-risk processes
            var healthScore = 1.0 - (threatRatio * 0.5) - (highRiskRatio * 0.3);

            return Math.Max(0.0, Math.Min(1.0, healthScore));
        }

        private string GenerateComprehensiveReport(ComprehensiveScanResult result)
        {
            var report = $"=== PcFutureShield Comprehensive Scan Report ===\n";
            report += $"Scan ID: {result.ScanId}\n";
            report += $"File: {result.FilePath}\n";
            report += $"Scan Time: {result.StartTime:G}\n";
            report += $"Duration: {result.Duration?.TotalSeconds:F2} seconds\n\n";

            report += $"OVERALL VERDICT: {(result.IsThreat ? "THREAT DETECTED" : "CLEAN")}\n";
            report += $"Threat Score: {(result.OverallThreatScore * 100):F1}%\n";
            report += $"Confidence: {(result.Confidence * 100):F1}%\n\n";

            // Zero-day detection results
            if (result.ZeroDayResult != null)
            {
                report += $"--- Zero-Day Detection ---\n";
                report += $"Zero-Day Score: {(result.ZeroDayResult.ZeroDayScore * 100):F1}%\n";
                report += $"Potential Zero-Day: {result.ZeroDayResult.IsPotentialZeroDay}\n";
                if (result.ZeroDayResult.DetectionReasons.Count > 0)
                {
                    report += $"Detection Reasons:\n";
                    foreach (var reason in result.ZeroDayResult.DetectionReasons)
                        report += $"  - {reason}\n";
                }
                report += "\n";
            }

            // AI detection results
            if (result.AIResult != null)
            {
                report += $"--- AI Detection ---\n";
                report += $"Malicious: {result.AIResult.IsMalicious}\n";
                report += $"Threat Score: {(result.AIResult.ThreatScore * 100):F1}%\n";
                report += $"Confidence: {(result.AIResult.Confidence * 100):F1}%\n";
                report += $"Anomaly Score: {(result.AIResult.AnomalyScore * 100):F1}%\n";
                if (result.AIResult.BehavioralIndicators.Count > 0)
                {
                    report += $"Behavioral Indicators:\n";
                    foreach (var indicator in result.AIResult.BehavioralIndicators)
                        report += $"  - {indicator}\n";
                }
                report += "\n";
            }

            // Threat intelligence results
            if (result.ThreatIntelResult != null)
            {
                report += $"--- Threat Intelligence ---\n";
                report += $"Malicious: {result.ThreatIntelResult.IsMalicious}\n";
                if (result.ThreatIntelResult.IsMalicious)
                {
                    report += $"Threat Name: {result.ThreatIntelResult.ThreatName}\n";
                    report += $"Threat Type: {result.ThreatIntelResult.ThreatType}\n";
                    report += $"Confidence: {(result.ThreatIntelResult.Confidence * 100):F1}%\n";
                    report += $"Source: {result.ThreatIntelResult.Source}\n";
                }
                report += "\n";
            }

            // Behavioral analysis results
            if (result.BehavioralResult != null)
            {
                report += $"--- Behavioral Analysis ---\n";
                report += $"Behavioral Score: {(result.BehavioralResult.BehavioralScore * 100):F1}%\n";
                report += $"Risk Level: {result.BehavioralResult.RiskLevel}\n";
                if (result.BehavioralResult.SuspiciousPatterns.Count > 0)
                {
                    report += $"Suspicious Patterns:\n";
                    foreach (var pattern in result.BehavioralResult.SuspiciousPatterns)
                        report += $"  - {pattern}\n";
                }
                report += "\n";
            }

            // Remediation recommendations
            if (result.Remediations.Count > 0)
            {
                report += $"--- Recommended Actions ---\n";
                foreach (var remediation in result.Remediations)
                    report += $"  • {remediation}\n";
                report += "\n";
            }

            if (!string.IsNullOrEmpty(result.Error))
            {
                report += $"ERROR: {result.Error}\n";
            }

            return report;
        }

        private string GenerateProcessReport(ComprehensiveScanResult result)
        {
            var report = $"=== PcFutureShield Process Scan Report ===\n";
            report += $"Scan ID: {result.ScanId}\n";
            report += $"Process: {result.ProcessName} (PID: {result.ProcessId})\n";
            report += $"Scan Time: {result.StartTime:G}\n\n";

            report += $"VERDICT: {(result.IsThreat ? "THREAT DETECTED" : "CLEAN")}\n";
            report += $"Threat Score: {(result.OverallThreatScore * 100):F1}%\n\n";

            // Similar detailed sections as file report...

            return report;
        }

        private string GenerateSystemReport(SystemScanResult result)
        {
            var report = $"=== PcFutureShield System Scan Report ===\n";
            report += $"Scan ID: {result.ScanId}\n";
            report += $"Scan Time: {result.StartTime:G}\n";
            report += $"Duration: {result.Duration?.TotalSeconds:F2} seconds\n\n";

            report += $"SYSTEM HEALTH: {(result.SystemHealthScore * 100):F1}%\n\n";

            report += $"--- Scan Summary ---\n";
            report += $"Processes Scanned: {result.TotalProcessesScanned}\n";
            report += $"Threats Detected: {result.TotalThreatsDetected}\n";
            report += $"High Risk: {result.HighRiskProcesses}\n";
            report += $"Medium Risk: {result.MediumRiskProcesses}\n";
            report += $"Low Risk: {result.LowRiskProcesses}\n\n";

            if (result.TotalThreatsDetected > 0)
            {
                report += $"--- Detected Threats ---\n";
                foreach (var threat in result.ProcessResults.Where(r => r.IsThreat))
                {
                    report += $"  • {threat.ProcessName} (PID: {threat.ProcessId}) - Score: {(threat.OverallThreatScore * 100):F1}%\n";
                }
                report += "\n";
            }

            return report;
        }

        private string GenerateURLReport(ComprehensiveScanResult result)
        {
            var report = $"=== PcFutureShield URL Scan Report ===\n";
            report += $"URL: {result.URL}\n";
            report += $"Verdict: {(result.IsThreat ? "MALICIOUS" : "SAFE")}\n\n";

            if (result.ThreatIntelResult != null)
            {
                report += $"Threat Intelligence: {result.ThreatIntelResult.IsMalicious}\n";
                if (result.ThreatIntelResult.IsMalicious)
                {
                    report += $"Threat: {result.ThreatIntelResult.ThreatName}\n";
                    report += $"Source: {result.ThreatIntelResult.Source}\n";
                }
            }

            return report;
        }

        private List<string> GenerateRemediationRecommendations(ComprehensiveScanResult result)
        {
            var recommendations = new List<string>();

            if (result.IsThreat)
            {
                recommendations.Add("Quarantine the file immediately");
                recommendations.Add("Scan the system for related threats");
                recommendations.Add("Update threat intelligence feeds");
            }

            if (result.OverallThreatScore > 0.8)
            {
                recommendations.Add("Consider system isolation");
                recommendations.Add("Contact security team for investigation");
            }

            if (result.ZeroDayResult?.IsPotentialZeroDay == true)
            {
                recommendations.Add("This appears to be a zero-day threat - handle with extreme caution");
                recommendations.Add("Submit sample to threat intelligence service");
            }

            return recommendations;
        }

        private List<string> GenerateProcessRemediations(ComprehensiveScanResult result)
        {
            var recommendations = new List<string>();

            if (result.IsThreat)
            {
                recommendations.Add($"Terminate process {result.ProcessName} (PID: {result.ProcessId})");
                recommendations.Add("Investigate process origin and parent processes");
                recommendations.Add("Check for persistence mechanisms");
            }

            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.High)
            {
                recommendations.Add("Isolate the system from network");
                recommendations.Add("Take memory dump for analysis");
            }

            return recommendations;
        }

        private List<string> GenerateURLRemediations(ComprehensiveScanResult result)
        {
            var recommendations = new List<string>();

            if (result.IsThreat)
            {
                recommendations.Add("Block access to this URL");
                recommendations.Add("Warn users about phishing attempts");
                recommendations.Add("Check for similar malicious URLs");
            }

            return recommendations;
        }

        private Dictionary<string, ScanResult> LoadScanHistory()
        {
            if (!File.Exists(_scanResultsPath))
            {
                return new Dictionary<string, ScanResult>();
            }

            try
            {
                var json = File.ReadAllText(_scanResultsPath);
                var history = JsonSerializer.Deserialize<List<ScanResult>>(json) ?? new List<ScanResult>();
                return history.ToDictionary(h => h.ScanId);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to load scan history");
                return new Dictionary<string, ScanResult>();
            }
        }

        private void SaveScanHistory()
        {
            try
            {
                var history = _scanHistory.Values.ToList();
                var json = JsonSerializer.Serialize(history, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_scanResultsPath, json);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error saving scan history");
            }
        }
    }

    // Supporting classes
    public class ComprehensiveScanResult
    {
        public string ScanId { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public string URL { get; set; } = string.Empty;
        public int? ProcessId { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool IsThreat { get; set; }
        public double OverallThreatScore { get; set; }
        public double Confidence { get; set; }
        public ZeroDayAnalysisResult? ZeroDayResult { get; set; }
        public AIAnalysisResult? AIResult { get; set; }
        public ThreatIntelligenceResult? ThreatIntelResult { get; set; }
        public BehavioralAnalysisResult? BehavioralResult { get; set; }
        public string ScanReport { get; set; } = string.Empty;
        public List<string> Remediations { get; set; } = new();
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class SystemScanResult
    {
        public string ScanId { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int TotalProcessesScanned { get; set; }
        public int TotalThreatsDetected { get; set; }
        public int HighRiskProcesses { get; set; }
        public int MediumRiskProcesses { get; set; }
        public int LowRiskProcesses { get; set; }
        public double SystemHealthScore { get; set; }
        public List<ComprehensiveScanResult> ProcessResults { get; set; } = new();
        public string SystemReport { get; set; } = string.Empty;
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class ScanResult
    {
        public string ScanId { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public int? ProcessId { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public bool IsThreat { get; set; }
        public double ThreatScore { get; set; }
        public DateTime ScanTime { get; set; }
        public string DetectionEngine { get; set; } = string.Empty;
    }

    public class ScanStatistics
    {
        public int TotalScans { get; set; }
        public int ThreatsDetected { get; set; }
        public DateTime LastScanTime { get; set; }
        public double DetectionRate { get; set; }
        public double AverageThreatScore { get; set; }
    }
}
