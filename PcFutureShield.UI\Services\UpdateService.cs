using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Security.Cryptography;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace PcFutureShield.UI.Services
{
    /// <summary>
    /// Production-grade update service: queries a JSON feed, validates checksums, and performs safe downloads.
    /// Designed to be resilient, cancellable and testable.
    /// </summary>
    public class UpdateService : IDisposable
    {
        private readonly HttpClient _http;
        private readonly SettingsService _settings;
        private readonly ILogger<UpdateService> _logger;
        private bool _disposed;

        public UpdateService(SettingsService settings, HttpClient? httpClient = null, ILogger<UpdateService>? logger = null)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _http = httpClient ?? new HttpClient() { Timeout = TimeSpan.FromSeconds(30) };
            _logger = logger ?? Microsoft.Extensions.Logging.Abstractions.NullLogger<UpdateService>.Instance;
        }

        public record UpdateFeedEntry(string Version, string Url, string Sha256, bool IsCritical, string? Notes);

        public async Task<IReadOnlyList<UpdateFeedEntry>> GetAvailableUpdatesAsync(CancellationToken cancellationToken = default)
        {
            var feedUrl = _settings.Get("UpdateFeedUrl", string.Empty);
            if (string.IsNullOrWhiteSpace(feedUrl))
            {
                _logger.LogWarning("No update feed URL configured.");
                return Array.Empty<UpdateFeedEntry>();
            }

            try
            {
                using var resp = await _http.GetAsync(feedUrl, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
                resp.EnsureSuccessStatusCode();
                await using var stream = await resp.Content.ReadAsStreamAsync(cancellationToken);
                var doc = await JsonDocument.ParseAsync(stream, cancellationToken: cancellationToken);
                var list = new List<UpdateFeedEntry>();
                if (doc.RootElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var item in doc.RootElement.EnumerateArray())
                    {
                        try
                        {
                            var version = item.GetProperty("version").GetString() ?? string.Empty;
                            var url = item.GetProperty("url").GetString() ?? string.Empty;
                            var sha = item.GetProperty("sha256").GetString() ?? string.Empty;
                            var critical = item.TryGetProperty("critical", out var crit) && crit.GetBoolean();
                            var notes = item.TryGetProperty("notes", out var n) ? n.GetString() : null;
                            if (!string.IsNullOrWhiteSpace(version) && !string.IsNullOrWhiteSpace(url))
                                list.Add(new UpdateFeedEntry(version, url, sha, critical, notes));
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Malformed update feed entry ignored");
                        }
                    }
                }

                return list;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying update feed");
                return Array.Empty<UpdateFeedEntry>();
            }
        }

        public async Task<(bool Success, string? LocalPath, string? Error)> DownloadUpdateAsync(UpdateFeedEntry entry, IProgress<double>? progress = null, CancellationToken cancellationToken = default)
        {
            if (entry == null) throw new ArgumentNullException(nameof(entry));
            try
            {
                using var resp = await _http.GetAsync(entry.Url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
                resp.EnsureSuccessStatusCode();

                var tempDir = Path.Combine(Path.GetTempPath(), "PcFutureShieldUpdates");
                Directory.CreateDirectory(tempDir);
                var fileName = Path.GetFileName(new Uri(entry.Url).LocalPath);
                var localPath = Path.Combine(tempDir, fileName);

                await using var contentStream = await resp.Content.ReadAsStreamAsync(cancellationToken);
                await using var fileStream = File.Create(localPath);

                var buffer = new byte[81920];
                long totalRead = 0;
                long? totalLength = resp.Content.Headers.ContentLength;
                int read;
                while ((read = await contentStream.ReadAsync(buffer.AsMemory(0, buffer.Length), cancellationToken)) > 0)
                {
                    await fileStream.WriteAsync(buffer.AsMemory(0, read), cancellationToken);
                    totalRead += read;
                    if (totalLength.HasValue && progress != null)
                    {
                        progress.Report((double)totalRead / totalLength.Value * 100.0);
                    }
                }

                // Verify sha256 if provided
                if (!string.IsNullOrWhiteSpace(entry.Sha256))
                {
                    var actual = await ComputeFileSha256Async(localPath, cancellationToken);
                    if (!string.Equals(actual, entry.Sha256, StringComparison.OrdinalIgnoreCase))
                    {
                        File.Delete(localPath);
                        return (false, null, "Checksum mismatch");
                    }
                }

                return (true, localPath, null);
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                return (false, null, "Cancelled");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading update {Url}", entry.Url);
                return (false, null, ex.Message);
            }
        }

        private static async Task<string> ComputeFileSha256Async(string path, CancellationToken cancellationToken)
        {
            await using var stream = File.OpenRead(path);
            using var sha = SHA256.Create();
            var hash = await sha.ComputeHashAsync(stream, cancellationToken);
            return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _http.Dispose();
                _disposed = true;
            }
        }
    }
}
