<UserControl x:Class="PcFutureShield.UI.Views.LicenseManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <TextBlock Text="License Manager" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- License Status -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="License Information" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="2">
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="License Type" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding LicenseType}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="License Status" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding LicenseStatus}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Expiration Date" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding ExpirationDate, StringFormat=d}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Days Remaining" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding DaysRemaining}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- License Activation -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="License Activation" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <TextBlock Text="Product Key:" FontSize="16" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBox Text="{Binding ProductKey, Mode=TwoWay}" Margin="0,0,0,15" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}"/>
                        <UniformGrid Columns="2" Rows="1">
                            <Button Content="Activate License" Style="{DynamicResource GlassButtonStyle}" Command="{Binding ActivateLicenseCommand}" Margin="5"/>
                            <Button Content="Deactivate License" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding DeactivateLicenseCommand}" Margin="5"/>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- License Features -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Licensed Features" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <ListBox ItemsSource="{Binding LicensedFeatures}" Height="150" Margin="0,0,0,15" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding FeatureName}" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,10,0"/>
                                        <TextBlock Text="{Binding Status}" Foreground="{DynamicResource SuccessBrush}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Refresh License" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding RefreshLicenseCommand}" Margin="10"/>
                    <Button Content="Transfer License" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding TransferLicenseCommand}" Margin="10"/>
                    <Button Content="Contact Support" Style="{DynamicResource MirrorButtonStyle}" Command="{Binding ContactSupportCommand}" Margin="10"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
