using System;

namespace PcFutureShield.Engine.Quarantine
{
    public sealed class QuarantineItem
    {
        public Guid Id { get; set; }
        public string OriginalPath { get; set; } = string.Empty;
        public string QuarantinePath { get; set; } = string.Empty; // encrypted blob
        public string OriginalSha256 { get; set; } = string.Empty;
        public DateTimeOffset Timestamp { get; set; }
        public string Reason { get; set; } = string.Empty;
        public long OriginalSize { get; set; }
    }

    public class QuarantineResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
