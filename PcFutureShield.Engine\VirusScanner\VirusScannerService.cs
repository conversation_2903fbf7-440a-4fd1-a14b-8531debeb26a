using Microsoft.Extensions.Logging;
using PcFutureShield.Common.Interfaces;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;

namespace PcFutureShield.Engine.VirusScanner
{
    public class VirusScannerService : IVirusScannerService, IDisposable
    {
        private readonly ILogger<VirusScannerService> _logger;
        private readonly SignatureDatabase? _signatureDb;
        private bool _disposed;
        private readonly object _scanLock = new();

        public event EventHandler<ThreatDetectedEventArgs>? ThreatDetected;
        public event EventHandler<ScanProgressEventArgs>? ScanProgress;

        public DateTime LastDefinitionUpdate { get; private set; }

        public VirusScannerService(ILogger<VirusScannerService> logger, SignatureDatabase? signatureDb = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _signatureDb = signatureDb;
        }
        public async Task<VirusScanResult> ScanAsync(IEnumerable<string> paths, CancellationToken cancellationToken = default)
        {
            // Delegate to the other ScanAsync method with default ScanType
            return await ScanAsync(paths, ScanType.Custom, cancellationToken);
        }

        public async Task<VirusScanResult> ScanAsync(IEnumerable<string> paths, ScanType scanType, CancellationToken cancellationToken = default)
        {
            var result = new VirusScanResult();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            foreach (var path in paths)
            {
                if (cancellationToken.IsCancellationRequested) break;
                if (File.Exists(path))
                {
                    result.FilesScanned++;
                    var threat = await ScanFileAsync(path, cancellationToken);
                    if (threat != null)
                        result.Threats.Add(threat);
                }
                else if (Directory.Exists(path))
                {
                    foreach (var file in SafeEnumerateFiles(path, "*", SearchOption.AllDirectories))
                    {
                        if (cancellationToken.IsCancellationRequested) break;
                        result.FilesScanned++;
                        try
                        {
                            var threat = await ScanFileAsync(file, cancellationToken);
                            if (threat != null)
                                result.Threats.Add(threat);
                        }
                        catch (UnauthorizedAccessException ex)
                        {
                            _logger.LogWarning(ex, "Access denied to file: {File}", file);
                        }
                        catch (IOException ex)
                        {
                            _logger.LogWarning(ex, "IO error on file: {File}", file);
                        }
                    }
                }
            }

            // Recursively enumerate files, skipping inaccessible directories
            static IEnumerable<string> SafeEnumerateFiles(string root, string searchPattern, SearchOption searchOption)
            {
                var dirs = new Stack<string>();
                dirs.Push(root);
                while (dirs.Count > 0)
                {
                    string currentDir = dirs.Pop();
                    string[] subDirs = Array.Empty<string>();
                    string[] files = Array.Empty<string>();
                    try
                    {
                        files = Directory.GetFiles(currentDir, searchPattern);
                    }
                    catch (UnauthorizedAccessException) { }
                    catch (IOException) { }
                    foreach (var file in files)
                        yield return file;
                    if (searchOption == SearchOption.AllDirectories)
                    {
                        try
                        {
                            subDirs = Directory.GetDirectories(currentDir);
                        }
                        catch (UnauthorizedAccessException) { }
                        catch (IOException) { }
                        foreach (var subDir in subDirs)
                            dirs.Push(subDir);
                    }
                }
            }
            stopwatch.Stop();
            result.Duration = stopwatch.Elapsed;
            return result;
        }

        public async Task<IReadOnlyList<QuarantineItem>> GetQuarantineAsync(CancellationToken cancellationToken = default)
        {
            var quarantineDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Quarantine", "quarantine.db");
            var items = new List<QuarantineItem>();
            if (!File.Exists(quarantineDbPath))
                return items;

            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={quarantineDbPath}");
            await conn.OpenAsync(cancellationToken);
            var cmd = conn.CreateCommand();
            cmd.CommandText = "SELECT Id, OriginalFilePath, QuarantinePath, ThreatName, DetectedAt, FileSize FROM Quarantine";
            using var reader = await cmd.ExecuteReaderAsync(cancellationToken);

            while (await reader.ReadAsync(cancellationToken))
            {
                items.Add(new QuarantineItem
                {
                    Id = Guid.Parse(reader.GetString(0)),
                    OriginalPath = reader.GetString(1),
                    QuarantinePath = reader.GetString(2),
                    ThreatName = reader.GetString(3),
                    DetectedAt = reader.GetDateTime(4),
                    OriginalSize = reader.GetInt64(5),
                    IsQuarantined = true
                });
            }
            return items;
        }

        public async Task<bool> RemoveFromQuarantineAsync(string quarantineId, CancellationToken cancellationToken = default)
        {
            var quarantineDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Quarantine", "quarantine.db");
            if (!File.Exists(quarantineDbPath))
                return false;

            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={quarantineDbPath}");
            await conn.OpenAsync(cancellationToken);

            // First get the file path before deleting the record
            var getCmd = conn.CreateCommand();
            getCmd.CommandText = "SELECT QuarantinePath FROM Quarantine WHERE Id = @id";
            getCmd.Parameters.AddWithValue("@id", quarantineId);

            string? filePath = await getCmd.ExecuteScalarAsync(cancellationToken) as string;

            // Delete the record from the database
            var deleteCmd = conn.CreateCommand();
            deleteCmd.CommandText = "DELETE FROM Quarantine WHERE Id = @id";
            deleteCmd.Parameters.AddWithValue("@id", quarantineId);

            int rows = await deleteCmd.ExecuteNonQueryAsync(cancellationToken);

            // If we have a file path and the file exists, delete it
            if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
            {
                try
                {
                    File.Delete(filePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting quarantined file: {FilePath}", filePath);
                    // Continue even if file deletion fails
                }
            }

            return rows > 0;
        }

        public async Task<bool> RestoreFromQuarantineAsync(string quarantineId, string? targetPath = null, CancellationToken cancellationToken = default)
        {
            var quarantineDbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Quarantine", "quarantine.db");
            if (!File.Exists(quarantineDbPath))
                return false;

            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={quarantineDbPath}");
            await conn.OpenAsync(cancellationToken);

            // Get the quarantined file info
            var cmd = conn.CreateCommand();
            cmd.CommandText = "SELECT OriginalFilePath, QuarantinePath FROM Quarantine WHERE Id = @id";
            cmd.Parameters.AddWithValue("@id", quarantineId);

            using var reader = await cmd.ExecuteReaderAsync(cancellationToken);
            if (!await reader.ReadAsync(cancellationToken))
                return false;

            var originalPath = reader.GetString(0);
            var quarantinePath = reader.GetString(1);
            var restorePath = targetPath ?? originalPath;

            // Ensure the target directory exists
            var targetDir = Path.GetDirectoryName(restorePath);
            if (!string.IsNullOrEmpty(targetDir) && !Directory.Exists(targetDir))
                Directory.CreateDirectory(targetDir);

            // Move the file back from quarantine
            if (File.Exists(quarantinePath))
            {
                if (File.Exists(restorePath))
                    File.Delete(restorePath);

                File.Move(quarantinePath, restorePath);
            }

            // Remove from quarantine database
            cmd = conn.CreateCommand();
            cmd.CommandText = "DELETE FROM Quarantine WHERE Id = @id";
            cmd.Parameters.AddWithValue("@id", quarantineId);

            return await cmd.ExecuteNonQueryAsync(cancellationToken) > 0;
        }

        public async Task<bool> UpdateDefinitionsAsync(CancellationToken cancellationToken = default)
        {
            if (_signatureDb == null)
            {
                _logger.LogWarning("Signature database not configured; cannot update definitions.");
                return false;
            }

            try
            {
                // Look for a definitions package in a standard folder under the application base directory
                var baseDir = AppDomain.CurrentDomain.BaseDirectory ?? ".";
                var defsDir = Path.Combine(baseDir, "Definitions");

                if (!Directory.Exists(defsDir))
                {
                    _logger.LogInformation("No definitions directory found at {DefsDir}", defsDir);
                    return false;
                }

                var jsonPackages = Directory.EnumerateFiles(defsDir, "*.json")
                    .OrderByDescending(p => File.GetLastWriteTimeUtc(p))
                    .ToList();

                if (jsonPackages.Count == 0)
                {
                    _logger.LogInformation("No definition packages found in {DefsDir}", defsDir);
                    return false;
                }

                // Pick the most recent package and import it
                var packagePath = jsonPackages.First();
                _logger.LogInformation("Applying signature package from {PackagePath}", packagePath);

                await _signatureDb.ImportFromJsonAsync(packagePath, cancellationToken).ConfigureAwait(false);

                LastDefinitionUpdate = DateTime.UtcNow;
                _logger.LogInformation("Virus definitions updated at {UpdateTime} from {Package}", LastDefinitionUpdate, packagePath);
                return true;
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("UpdateDefinitionsAsync was canceled");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating virus definitions");
                return false;
            }
        }

        public async Task<(bool Success, string? Error)> ApplySignaturePackageAsync(string packageJsonPath, CancellationToken cancellationToken = default)
        {
            if (_signatureDb == null) return (false, "Signature database not configured");
            try
            {
                await _signatureDb.ImportFromJsonAsync(packageJsonPath, cancellationToken);
                LastDefinitionUpdate = DateTime.UtcNow;
                return (true, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to apply signature package");
                return (false, ex.Message);
            }
        }


        protected virtual void OnThreatDetected(ThreatInfo threat)
        {
            ThreatDetected?.Invoke(this, new ThreatDetectedEventArgs(threat));
        }

        protected virtual void OnScanProgress(ScanProgressEventArgs e)
        {
            ScanProgress?.Invoke(this, e);
        }

        private async Task<ThreatInfo?> ScanFileAsync(string filePath, CancellationToken cancellationToken)
        {
            // Hash file and check against threat signature database
            using var stream = File.OpenRead(filePath);
            using var sha256 = SHA256.Create();
            var hash = await sha256.ComputeHashAsync(stream, cancellationToken);
            var hashString = BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            var threatName = await GetThreatNameFromSignatureDbAsync(hashString, cancellationToken);
            if (threatName != null)
            {
                return new ThreatInfo
                {
                    FilePath = filePath,
                    ThreatName = threatName,
                    DetectedAt = DateTime.UtcNow,
                    IsQuarantined = false
                };
            }
            return null;
        }

        private async Task<string?> GetThreatNameFromSignatureDbAsync(string hash, CancellationToken cancellationToken)
        {
            try
            {
                if (_signatureDb != null)
                {
                    return await _signatureDb.GetThreatNameAsync(hash, cancellationToken);
                }
                // As a fallback, return null and log
                _logger.LogWarning("Signature DB not configured; skipping signature lookup");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error querying threat signature database");
                return null;
            }
        }
        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources here
                }
                _disposed = true;
            }
        }

        ~VirusScannerService()
        {
            Dispose(false);
        }

        // Method implementations are provided in the main class body above

        #endregion
    }
}
