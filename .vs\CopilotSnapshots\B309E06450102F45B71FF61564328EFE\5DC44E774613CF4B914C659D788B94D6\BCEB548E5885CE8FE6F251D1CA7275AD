﻿using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using PcFutureShield.UI.Views;
using PcFutureShield.Engine.VirusScanner;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;
        private readonly ParentalControlService _parentalControlService;
        private readonly GamingProtectionService _gamingProtectionService;
        private readonly PcOptimizationService _pcOptimizationService;
        private readonly AdminOverrideService _adminOverrideService;
        private readonly BrowserExtensionService _browserExtensionService;

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public event Action<string>? RequestThemeChange;

        public ICommand DashboardCommand { get; }
        public ICommand VirusScanCommand { get; }
        public ICommand EnhancedScannerCommand { get; }
        public ICommand RealTimeProtectionCommand { get; }
        public ICommand ParentalControlCommand { get; }
        public ICommand QuarantineCommand { get; }
        public ICommand AIDashboardCommand { get; }
        public ICommand GamingProtectionCommand { get; }
        public ICommand AdminOverrideCommand { get; }
        public ICommand SmartRepairCommand { get; }
        public ICommand SettingsCommand { get; }
        public ICommand LicenseManagerCommand { get; }
        public ICommand UpdatesCommand { get; }
        public ICommand ThemeSelectorCommand { get; }

        private object? _currentView;
        public object? CurrentView { get => _currentView; set { _currentView = value; OnPropertyChanged(); } }

        private int _themeIndex = 0;
        private readonly string[] _themes = new[] { "GlossyBlue", "GlossyGreen", "GlossyMidnightBlue", "GlossyPurple", "GlossyRed" };

        public MainViewModel()
        {
            _antivirusOrchestrator = PcFutureShield.UI.Services.ServiceLocator.Get<AntivirusOrchestrator>();
            _threatIntelligenceService = PcFutureShield.UI.Services.ServiceLocator.Get<ThreatIntelligenceService>();
            _parentalControlService = PcFutureShield.UI.Services.ServiceLocator.Get<ParentalControlService>();
            _gamingProtectionService = PcFutureShield.UI.Services.ServiceLocator.Get<GamingProtectionService>();
            _pcOptimizationService = PcFutureShield.UI.Services.ServiceLocator.Get<PcOptimizationService>();
            _adminOverrideService = PcFutureShield.UI.Services.ServiceLocator.Get<AdminOverrideService>();
            _browserExtensionService = PcFutureShield.UI.Services.ServiceLocator.Get<BrowserExtensionService>();

            DashboardCommand = new RelayCommand(() => Navigate("Dashboard"));
            VirusScanCommand = new RelayCommand(() => Navigate("VirusScan"));
            EnhancedScannerCommand = new RelayCommand(() => Navigate("EnhancedScanner"));
            RealTimeProtectionCommand = new RelayCommand(() => Navigate("RealTimeProtection"));
            ParentalControlCommand = new RelayCommand(() => Navigate("ParentalControl"));
            QuarantineCommand = new RelayCommand(() => Navigate("Quarantine"));
            AIDashboardCommand = new RelayCommand(() => Navigate("AIDashboard"));
            GamingProtectionCommand = new RelayCommand(() => Navigate("GamingProtection"));
            AdminOverrideCommand = new RelayCommand(() => Navigate("AdminOverride"));
            SmartRepairCommand = new RelayCommand(() => Navigate("SmartRepair"));
            SettingsCommand = new RelayCommand(() => Navigate("Settings"));
            LicenseManagerCommand = new RelayCommand(() => Navigate("LicenseManager"));
            UpdatesCommand = new RelayCommand(() => Navigate("Updates"));
            ThemeSelectorCommand = new RelayCommand(ExecuteThemeSelector);
            Navigate("Dashboard");
        }

        private void Navigate(string viewName)
        {
            // All navigation targets handled with real, production ViewModels
            switch (viewName)
            {
                case "Dashboard":
                    var dashboardView = new DashboardView();
                    dashboardView.DataContext = new PcFutureShield.UI.ViewModels.DashboardViewModel(_antivirusOrchestrator, _threatIntelligenceService);
                    CurrentView = dashboardView;
                    break;
                case "VirusScan":
                    var virusScanView = new VirusScanView();
                    var virusScannerAdapter = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.ViewModels.IVirusScannerService>();
                    virusScanView.DataContext = new PcFutureShield.UI.ViewModels.VirusScanViewModel(virusScannerAdapter);
                    CurrentView = virusScanView;
                    break;
                case "Scanner":
                    var scannerView = new ScannerView();
                    scannerView.DataContext = new PcFutureShield.UI.ViewModels.ScannerViewModel();
                    CurrentView = scannerView;
                    break;
                case "EnhancedScanner":
                    var enhancedScanView = new EnhancedScanView();
                    enhancedScanView.DataContext = new PcFutureShield.UI.ViewModels.EnhancedScanViewModel();
                    CurrentView = enhancedScanView;
                    break;
                case "RealTimeProtection":
                    var realtimeProtectionView = new RealtimeProtectionView();
                    realtimeProtectionView.DataContext = new PcFutureShield.UI.ViewModels.RealtimeProtectionViewModel();
                    CurrentView = realtimeProtectionView;
                    break;
                case "Quarantine":
                    var quarantineView = new QuarantineView();
                    quarantineView.DataContext = new PcFutureShield.UI.ViewModels.QuarantineViewModel();
                    CurrentView = quarantineView;
                    break;
                case "AIDashboard":
                    var aiDashboardView = new AIDashboardView();
                    aiDashboardView.DataContext = new PcFutureShield.UI.ViewModels.AIDashboardViewModel();
                    CurrentView = aiDashboardView;
                    break;
                case "GamingProtection":
                    var gamingProtectionView = new GamingProtectionView();
                    gamingProtectionView.DataContext = new PcFutureShield.UI.ViewModels.GamingProtectionViewModel(_gamingProtectionService);
                    CurrentView = gamingProtectionView;
                    break;
                case "AdminOverride":
                    var adminOverrideView = new AdminOverrideView();
                    adminOverrideView.DataContext = new PcFutureShield.UI.ViewModels.AdminOverrideViewModel(_adminOverrideService);
                    CurrentView = adminOverrideView;
                    break;
                case "SmartRepair":
                    var smartRepairView = new SmartRepairView();
                    smartRepairView.DataContext = new PcFutureShield.UI.ViewModels.SmartRepairViewModel();
                    CurrentView = smartRepairView;
                    break;
                case "Settings":
                    var settingsView = new SettingsView();
                    settingsView.DataContext = new PcFutureShield.UI.ViewModels.SettingsViewModel();
                    CurrentView = settingsView;
                    break;
                case "LicenseManager":
                    var licenseManagerView = new LicenseManagerView();
                    licenseManagerView.DataContext = new PcFutureShield.UI.ViewModels.LicenseManagerViewModel();
                    CurrentView = licenseManagerView;
                    break;
                case "Updates":
                    var updatesView = new UpdatesView();
                    updatesView.DataContext = new PcFutureShield.UI.ViewModels.UpdatesViewModel();
                    CurrentView = updatesView;
                    break;
                case "ParentalControl":
                    var parentalControlView = new ParentalControlView();
                    parentalControlView.DataContext = new PcFutureShield.UI.ViewModels.ParentalControlViewModel(_parentalControlService);
                    CurrentView = parentalControlView;
                    break;
                default:
                    throw new ArgumentException($"Unknown view: {viewName}");
            }
        }
        // ...existing code...

        private void ExecuteThemeSelector()
        {
            _themeIndex = (_themeIndex + 1) % _themes.Length;
            RequestThemeChange?.Invoke(_themes[_themeIndex]);
        }
    }
}
