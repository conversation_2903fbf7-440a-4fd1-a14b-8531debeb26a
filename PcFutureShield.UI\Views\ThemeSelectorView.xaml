<UserControl x:Class="PcFutureShield.UI.Views.ThemeSelectorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             Background="{DynamicResource BackgroundBrush}">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- Header -->
            <TextBlock Text="Theme Selector" FontSize="32" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" HorizontalAlignment="Center" Margin="0,0,0,24"/>

            <!-- Current Theme Display -->
            <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="Current Theme:" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,10"/>
                    <TextBlock Text="{Binding SelectedTheme}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource AccentColorBrush}"/>
                </StackPanel>
            </Border>

            <!-- Available Themes -->
            <TextBlock Text="Available Themes" FontSize="24" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,16"/>

            <ItemsControl ItemsSource="{Binding AvailableThemes}" Margin="0,0,0,20">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,12" Padding="16">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="{Binding DisplayName}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,4"/>
                                    <TextBlock Text="{Binding Description}" FontSize="14" Foreground="{DynamicResource SecondaryFontBrush}" TextWrapping="Wrap"/>
                                    <TextBlock Text="{Binding Name, StringFormat='Theme: {0}'}" FontSize="12" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,4,0,0" Opacity="0.7"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                                    <Button Content="Preview" Command="{Binding DataContext.PreviewThemeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding Name}" Style="{DynamicResource GlassButtonStyle}" Width="100" Height="35" Margin="0,0,10,0"/>
                                    <Button Content="Apply" Command="{Binding DataContext.ApplyThemeCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding Name}" Style="{DynamicResource ChromeButtonStyle}" Width="100" Height="35"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>

            <!-- Theme Settings -->
            <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                <StackPanel>
                    <TextBlock Text="Theme Settings" FontSize="20" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,16"/>

                    <CheckBox Content="Enable Animations" IsChecked="{Binding EnableAnimations}" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                    <CheckBox Content="Enable Glass Effects" IsChecked="{Binding EnableGlassEffect}" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                    <CheckBox Content="Auto-switch Themes" IsChecked="{Binding AutoSwitchThemes}" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>

                    <StackPanel Orientation="Horizontal" Margin="0,12,0,0">
                        <TextBlock Text="Theme Opacity:" FontSize="14" Foreground="{DynamicResource PrimaryFontBrush}" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <Slider Value="{Binding ThemeOpacity}" Minimum="0.5" Maximum="1.0" Width="200" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ThemeOpacity, StringFormat={}{0:P0}}" FontSize="14" Foreground="{DynamicResource SecondaryFontBrush}" VerticalAlignment="Center" Margin="10,0,0,0"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Action Buttons -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button Content="Reset to Default" Command="{Binding ResetToDefaultCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="160" Height="48" Margin="0,0,20,0"/>
                <Button Content="Save Settings" Command="{Binding SaveThemeSettingsCommand}" Style="{DynamicResource ChromeButtonStyle}" Width="160" Height="48"/>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>
