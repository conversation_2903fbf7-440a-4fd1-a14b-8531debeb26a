using System;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using PcFutureShield.UI.Services;
using PcFutureShield.Common.Services;
// Add the correct namespace for MainViewModel if it exists, for example:
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : System.Windows.Window
    {
        private readonly LoggingService _logger = LoggingService.Instance;

        public MainWindow()
        {
            try
            {
                _logger.LogInfo("MainWindow", "Application starting up");
                InitializeComponent();

                // Apply DropShadowEffect color from theme Color resource if available
                try
                {
                    if (Application.Current.Resources.Contains("PrimaryColor"))
                    {
                        var colorObj = Application.Current.Resources["PrimaryColor"];
                        var sidebar = this.FindName("SidebarBorder") as System.Windows.Controls.Border;
                        if (sidebar != null)
                        {
                            if (colorObj is Color c)
                            {
                                sidebar.Effect = new System.Windows.Media.Effects.DropShadowEffect
                                {
                                    Color = c,
                                    Direction = 270,
                                    ShadowDepth = 6,
                                    BlurRadius = 16,
                                    Opacity = 0.25
                                };
                            }
                            else if (colorObj is SolidColorBrush brush)
                            {
                                sidebar.Effect = new System.Windows.Media.Effects.DropShadowEffect
                                {
                                    Color = brush.Color,
                                    Direction = 270,
                                    ShadowDepth = 6,
                                    BlurRadius = 16,
                                    Opacity = 0.25
                                };
                            }
                        }
                    }
                }
                catch (Exception fx)
                {
                    _logger.LogWarning("MainWindow", $"Failed to set sidebar shadow from resources: {fx.Message}");
                }

                // Use the MainViewModel from the ViewModels namespace so DataTemplates in XAML match
                PcFutureShield.UI.ViewModels.MainViewModel vm;
                try
                {
                    vm = new PcFutureShield.UI.ViewModels.MainViewModel();
                    _logger.LogInfo("MainWindow", "MainViewModel created successfully");
                }
                catch (Exception ex)
                {
                    _logger.LogFatal("MainWindow", $"Failed to create MainViewModel: {ex.Message}", ex);
                    var notifier = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                    if (notifier != null)
                        notifier.ShowError("Initialization Error", $"Failed to initialize application: {ex.Message}\n\nThe application may be partially functional.");
                    else
                        System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Failed to initialize application: {ex.Message}\n\nThe application may be partially functional.", "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error));

                    // Keep the window open with a fallback view to allow the user to inspect the app
                    this.DataContext = new PcFutureShield.UI.ViewModels.FallbackViewModel("Initialization Error", ex.Message);
                    // Do not shutdown here - allow App to decide or user to continue
                    return;
                }
                // Subscribe to the correct event name exposed by that ViewModel
                vm.RequestThemeChange += OnRequestThemeChange;
                // Log when CurrentView changes so we can diagnose DataTemplate rendering issues
                vm.PropertyChanged += (s, e) =>
                {
                    if (e.PropertyName == "CurrentView")
                    {
                        try
                        {
                            // Resolve the ContentControl by name at runtime in case the generated field is not present
                            var mainContentControl = this.FindName("MainContentControl") as System.Windows.Controls.ContentControl;
                            var content = mainContentControl?.Content;
                            _logger.LogDebug("MainWindow", $"MainContentControl.Content is now: {content?.GetType().Name ?? "null"}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainWindow", "Error inspecting MainContentControl content", ex);
                        }
                    }
                };
                DataContext = vm;
                _logger.LogInfo("MainWindow", "MainWindow initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogFatal("MainWindow", "Failed to initialize MainWindow", ex);
                var notifier = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                if (notifier != null)
                    notifier.ShowError("Startup Error", $"Application startup error: {ex.Message}\n\nStack Trace: {ex.StackTrace}");
                else
                    System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Application startup error: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error));

                // Keep app running; show fallback UI so user can see the error
                this.DataContext = new PcFutureShield.UI.ViewModels.FallbackViewModel("Startup Error", ex.Message + "\n" + ex.StackTrace);
            }
        }

        private void OnRequestThemeChange(string themeName)
        {
            _logger.LogInfo("ThemeSwitching", $"Attempting to switch to theme: {themeName}");
            try
            {
                string themePath = themeName switch
                {
                    "GlossyBlue" => "Themes/GlossyBlue.xaml",
                    "GlossyGreen" => "Themes/GlossyGreen.xaml",
                    "GlossyMidnightBlue" => "Themes/GlossyMidnightBlue.xaml",
                    "GlossyPurple" => "Themes/GlossyPurple.xaml",
                    "GlossyRed" => "Themes/GlossyRed.xaml",
                    _ => "Themes/GlossyBlue.xaml"
                };

                _logger.LogDebug("ThemeSwitching", $"Loading theme from path: {themePath}");

                var dict = new ResourceDictionary { Source = new Uri(themePath, UriKind.Relative) };
                var appResources = Application.Current.Resources;

                if (appResources.MergedDictionaries.Count > 0)
                {
                    appResources.MergedDictionaries[0] = dict;
                    _logger.LogInfo("ThemeSwitching", $"Successfully replaced theme with {themeName}");
                }
                else
                {
                    appResources.MergedDictionaries.Add(dict);
                    _logger.LogInfo("ThemeSwitching", $"Successfully added theme {themeName} as first dictionary");
                }

                // Update sidebar shadow color to new theme's PrimaryColor if available
                try
                {
                    if (Application.Current.Resources.Contains("PrimaryColor"))
                    {
                        var colorObj = Application.Current.Resources["PrimaryColor"];
                        Color c;
                        if (colorObj is Color cc) c = cc;
                        else if (colorObj is SolidColorBrush sb) c = sb.Color;
                        else c = Colors.Transparent;

                        var sidebar = this.FindName("SidebarBorder") as System.Windows.Controls.Border;
                        if (sidebar != null)
                        {
                            sidebar.Effect = new System.Windows.Media.Effects.DropShadowEffect
                            {
                                Color = c,
                                Direction = 270,
                                ShadowDepth = 6,
                                BlurRadius = 16,
                                Opacity = 0.25
                            };
                        }
                    }
                }
                catch (Exception fx)
                {
                    _logger.LogWarning("ThemeSwitching", $"Failed to update sidebar shadow color: {fx.Message}");
                }

                // Verify theme was loaded by checking for key resources
                if (dict.Contains("PrimaryColorBrush") && dict.Contains("PrimaryFontBrush"))
                {
                    _logger.LogInfo("ThemeSwitching", $"Theme {themeName} loaded successfully with required resources");
                }
                else
                {
                    _logger.LogWarning("ThemeSwitching", $"Theme {themeName} loaded but missing some expected resources");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ThemeSwitching", $"Failed to switch to theme {themeName}", ex);

                // Try to show a more user-friendly error message
                string errorMessage = $"Failed to load theme '{themeName}':\n{ex.Message}\n\nThe application will continue with the current theme.";
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Theme Loading Error", errorMessage); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show(errorMessage, "Theme Loading Error", MessageBoxButton.OK, MessageBoxImage.Warning)); }

                // Don't rethrow - let the app continue with current theme
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInfo("MainWindow", "Opening log viewer");
                var logViewer = new PcFutureShield.UI.Views.LogViewerWindow
                {
                    Owner = this
                };
                logViewer.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError("MainWindow", "Failed to open log viewer", ex);
                var notify = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                if (notify != null) notify.ShowError("Error", $"Failed to open log viewer: {ex.Message}");
                else System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Failed to open log viewer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error));
            }
        }

        // InitializeComponent is provided by generated code during normal builds.
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void LightYellow(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
