using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Logging;
using PcFutureShield.ExtensionHost.Hubs;

var builder = WebApplication.CreateBuilder(args);

// Logging & Kestrel defaults
builder.Logging.ClearProviders();
builder.Logging.AddConsole();

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyHeader().AllowAnyMethod().AllowCredentials().AllowAnyOrigin();
    });
});

builder.Services.AddSignalR();

// Optional: configure HTTPS & certificates in production
var app = builder.Build();

app.UseRouting();
app.UseCors();

app.MapGet("/", () => "PcFutureShield ExtensionHost (SignalR) is running.");
app.MapHub<DetectionHub>("/hubs/detections");

app.Run();
