# PowerShell script to fix property syntax errors in ViewModels

$files = @(
    "PcFutureShield.UI\ViewModels\ParentalControlViewModel.cs",
    "PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs",
    "PcFutureShield.UI\ViewModels\ThemeSelectorViewModel.cs",
    "PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs",
    "PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs",
    "PcFutureShield.UI\ViewModels\DashboardViewModel.cs",
    "PcFutureShield.UI\ViewModels\QuarantineViewModel.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing property syntax in $file..."
        
        $content = Get-Content $file -Raw
        
        # Fix missing closing braces for properties
        # Pattern: property declaration without closing brace followed by another property
        $content = $content -replace '(?s)(set\s*=>\s*SetProperty\([^;]+;\s*)(?=\s*public\s+\w+\s+\w+)', '$1}' + "`n`n        "
        
        # Fix properties that are missing opening braces after constructor
        $content = $content -replace '(?s)(\}\s*)(?=public\s+\w+\s+\w+\s*\{)', '$1' + "`n`n        "
        
        # Fix malformed property declarations that start without proper indentation
        $content = $content -replace '(?m)^public\s+(\w+)\s+(\w+)\s*$\s*\{', '        public $1 $2' + "`n        {"
        
        # Ensure proper closing braces for properties
        $content = $content -replace '(?s)(set\s*=>\s*SetProperty\([^;]+;\s*)(?=\s*public\s+\w+)', '$1}' + "`n`n        "
        
        # Fix constructor closing brace issues
        $content = $content -replace '(?s)(\s+LoadParentalControlData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        $content = $content -replace '(?s)(\s+LoadGamingProtectionData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        $content = $content -replace '(?s)(\s+LoadThemeData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        $content = $content -replace '(?s)(\s+LoadAdminData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        $content = $content -replace '(?s)(\s+LoadBrowserExtensionData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        $content = $content -replace '(?s)(\s+LoadDashboardData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        $content = $content -replace '(?s)(\s+LoadQuarantineData\(\);\s*)(?=public\s+)', '$1}' + "`n`n        "
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed property syntax in $file"
    }
}

Write-Host "Property syntax fixes completed!"
