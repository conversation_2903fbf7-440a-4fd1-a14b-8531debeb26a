using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Text.Json;
using System.Threading.Tasks;
using System.Xml.Linq;
using Spectre.Console;


// Disable selected analyzer/IDE warnings/messages per project requirement
#pragma warning disable CS8604 // Possible null reference argument
// Remove unnecessary suppression for IDE0079
// The following line is unnecessary and can be removed:
// #pragma warning disable IDE0079 // Remove unnecessary suppression
#pragma warning disable IDE0300 // Collection initialization can be simplified
#pragma warning disable IDE0090 // 'new' expression can be simplified
#pragma warning disable IDE0057 // Substring can be simplified
#pragma warning disable CA1845  // AsSpan suggestion
// Remove unnecessary suppression for IDE0079
// The following line is unnecessary and can be removed:
// #pragma warning disable IDE0079 // Remove unnecessary suppression
#pragma warning disable SYSLIB1045 // GeneratedRegexAttribute suggestion

namespace PcFutureShield.Tools.AutoValidator
{
    internal partial class Program
    {
        // --- Config ---
        private static readonly string[] FakeIndicators =
        {
            "TODO",
            "PLACEHOLDER",
            "STUB",
            "NotImplementedException",
            "Task.FromResult",
            "Task.CompletedTask",
            "Task.Run(() =>",
            "throw new NotImplementedException"
        };

        private static readonly string[] LineSeparators = { "\r\n", "\n" };

        private static readonly JsonSerializerOptions SerializerOptions = new() { WriteIndented = true };

        private static readonly Regex AsyncMethodRegex = new Regex(@"\basync\s+(?:Task|Task<\w+>)\s+[A-Za-z0-9_]+\s*\(", RegexOptions.Compiled);

        private static readonly Regex BindingRegex = new Regex(@"\{Binding\s+([^,\}]+)", RegexOptions.Compiled);

        // Capture attribute name and value: e.g. Click="OnSave" -> groups[1]=Click groups[2]=OnSave
        private static readonly Regex EventHandlerRegex = new Regex(@"\b([A-Za-z0-9_]+)\s*=\s*\""([A-Za-z0-9_]+)\""", RegexOptions.Compiled);

        private static readonly Regex EventHandlerSimpleRegex = EventHandlerRegex;

        // Additional generated regexes to satisfy analyzers
        // [GeneratedRegex(@"public\s+[A-Za-z0-9_]+\s*\(\s*\)\s*\{\s*", RegexOptions.Multiline | RegexOptions.Compiled)]
        // private static partial Regex CtorPatternRegex();
        private static readonly Regex CtorPatternRegex = new Regex(@"public\s+[A-Za-z0-9_]+\s*\(\s*\)\s*\{\s*", RegexOptions.Multiline | RegexOptions.Compiled);

        // [GeneratedRegex(@"public\s+class\s+([A-Za-z0-9_]+)\s*(?:[:\w\s,<>]*)\{", RegexOptions.Singleline | RegexOptions.Compiled)]
        // private static partial Regex ClassPatternRegex();
        private static readonly Regex ClassPatternRegex = new Regex(@"public\s+class\s+([A-Za-z0-9_]+)\s*(?:[:\w\s,<>]*)\{", RegexOptions.Singleline | RegexOptions.Compiled);

        // [GeneratedRegex(@"return\s+Task\\.FromResult\s*\(\s*([^\)]+)\s*\)\s*;", RegexOptions.Multiline | RegexOptions.Compiled)]
        // private static partial Regex TaskFromResultRegex();
        private static readonly Regex TaskFromResultRegex = new Regex(@"return\s+Task\.FromResult\s*\(\s*([^\)]+)\s*\)\s*;", RegexOptions.Multiline | RegexOptions.Compiled);

        // Simple mapping heuristics for services -> templates (expand for your domain)
        private static readonly Dictionary<string, string> ServiceKeywords = new()
        {
            ["ScannerService"] = "scanner",
            ["PcOptimizationService"] = "optimizer",
            ["DiskOptimizer"] = "optimizer",
            ["BackupManager"] = "backup",
            ["SecurityScanner"] = "scanner"
        };

        static int Main(string[] args)
        {
            Console.WriteLine("=== AutoValidationTool (Dry Run by default) ===");
            string rootPath = args.Length > 0 ? args[0] : Directory.GetCurrentDirectory();
            bool applyChanges = args.Contains("--apply");
            bool verbose = args.Contains("--verbose");

            Console.WriteLine($"Scanning: {rootPath}");
            // Do not scan the AutoValidator project itself to avoid self-reporting
            var csFiles = Directory.GetFiles(rootPath, "*.cs", SearchOption.AllDirectories)
                                   .Where(f => !f.Contains("\\bin\\") && !f.Contains("\\obj\\") && !f.Contains("PcFutureShield.Tools.AutoValidator"))
                                   .ToArray();
            var xamlFiles = Directory.GetFiles(rootPath, "*.xaml", SearchOption.AllDirectories)
                                   .Where(f => !f.Contains("\\bin\\") && !f.Contains("\\obj\\") && !f.Contains("PcFutureShield.Tools.AutoValidator"))
                                   .ToArray();

            var report = new List<string>();
            var fixes = new List<FixSuggestion>();

            // 1) Scan C# files for fake indicators and async-without-await
            foreach (var file in csFiles)
            {
                var content = File.ReadAllText(file);
                var lines = content.Split(LineSeparators, StringSplitOptions.None);

                // Fake indicators
                for (int i = 0; i < lines.Length; i++)
                {
                    var line = lines[i];
                    foreach (var indicator in FakeIndicators)
                    {
                        if (line.Contains(indicator, StringComparison.OrdinalIgnoreCase))
                        {
                            report.Add($"[FAKE] {file} (Line {i + 1}): {line.Trim()}");
                            fixes.Add(new FixSuggestion
                            {
                                FilePath = file,
                                Kind = FixKind.FakePattern,
                                Line = i + 1,
                                Context = line.Trim(),
                                Suggested = $"Manual: Replace fake indicator '{indicator}' with real logic. See service templates."
                            });
                        }
                    }
                }

                // Async without await
                foreach (Match m in AsyncMethodRegex.Matches(content))
                {
                    var idx = m.Index;
                    var methodBlock = ExtractMethodBlock(content, idx);
                    if (!methodBlock.Contains("await"))
                    {
                        // If method just returns Task.FromResult or Task.CompletedTask, propose fix
                        if (methodBlock.Contains("Task.FromResult") || methodBlock.Contains("Task.CompletedTask"))
                        {
                            report.Add($"[ASYNC-FAKE] {file}: {m.Value.Trim()} uses Task.FromResult / CompletedTask.");
                            fixes.Add(new FixSuggestion
                            {
                                FilePath = file,
                                Kind = FixKind.AsyncFakeWrapper,
                                Context = m.Value.Trim(),
                                Suggested = "Replace wrapper with either true async (await underlying I/O) or revert to sync."
                            });
                        }
                        else
                        {
                            report.Add($"[ASYNC-WITHOUT-AWAIT] {file}: {m.Value.Trim()} (no await found)");
                            fixes.Add(new FixSuggestion
                            {
                                FilePath = file,
                                Kind = FixKind.AsyncWithoutAwait,
                                Context = m.Value.Trim(),
                                Suggested = "Consider making method synchronous or use real async APIs."
                            });
                        }
                    }
                }
            }

            // 2) Parse XAML files: bindings, events, resources
            foreach (var xamlPath in xamlFiles)
            {
                try
                {
                    var xdoc = XDocument.Load(xamlPath, LoadOptions.SetLineInfo);
                    var xamlText = File.ReadAllText(xamlPath);

                    // Find bindings
                    foreach (Match bindMatch in BindingRegex.Matches(xamlText))
                    {
                        var prop = bindMatch.Groups[1].Value.Trim();
                        // Heuristic: find corresponding ViewModel (same name as file minus "View" or "Window")
                        var viewModelGuess = GuessViewModelForXaml(xamlPath);
                        if (viewModelGuess != null)
                        {
                            var vmPath = FindViewModelFile(rootPath, viewModelGuess);
                            if (vmPath == null)
                            {
                                report.Add($"[MISSING-VM] {xamlPath}: Binding '{prop}' -> Missing VM file '{viewModelGuess}'.");
                                fixes.Add(new FixSuggestion
                                {
                                    FilePath = xamlPath,
                                    Kind = FixKind.MissingViewModel,
                                    Context = prop,
                                    Suggested = $"Create ViewModel {viewModelGuess} with property '{prop}' and INotifyPropertyChanged."
                                });
                            }
                            else
                            {
                                // Verify property exists
                                var vmText = File.ReadAllText(vmPath);
                                if (!PropertyExistsInCode(vmText, prop))
                                {
                                    report.Add($"[MISSING-PROP] {xamlPath}: Binding '{prop}' -> Not found in {vmPath}.");
                                    fixes.Add(new FixSuggestion
                                    {
                                        FilePath = vmPath,
                                        Kind = FixKind.MissingProperty,
                                        Context = prop,
                                        Suggested = $"Add property '{prop}' with INotifyPropertyChanged to {Path.GetFileName(vmPath)}.",
                                        ViewModelProperty = prop
                                    });
                                }
                                else
                                {
                                    if (!ImplementsINotify(vmText))
                                    {
                                        report.Add($"[VM-MISSING-INPC] {vmPath}: ViewModel does not implement INotifyPropertyChanged.");
                                        fixes.Add(new FixSuggestion
                                        {
                                            FilePath = vmPath,
                                            Kind = FixKind.MissingINotify,
                                            Suggested = $"Implement INotifyPropertyChanged in {Path.GetFileName(vmPath)}."
                                        });
                                    }
                                }
                            }
                        }
                    }

                    // Find event handlers in XAML like Click="OnSave" but only consider known event attributes
                    var knownEventAttributes = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
                    {
                        "Click","Loaded","Unloaded","Initialized","SelectionChanged","Checked","Unchecked",
                        "MouseDown","MouseUp","MouseEnter","MouseLeave","PreviewMouseDown","PreviewMouseUp",
                        "KeyDown","KeyUp","TextChanged","Drop","DragEnter","DragLeave","PreviewKeyDown",
                        "PreviewMouseLeftButtonDown","PreviewMouseLeftButtonUp"
                    };

                    foreach (Match m in EventHandlerRegex.Matches(xamlText))
                    {
                        var attributeName = m.Groups[1].Value;
                        var handlerName = m.Groups[2].Value;
                        if (string.IsNullOrEmpty(attributeName) || string.IsNullOrEmpty(handlerName))
                            continue;

                        // Only treat as an event if the attribute is a known event name
                        if (!knownEventAttributes.Contains(attributeName))
                            continue;

                        var codeBehindPath = xamlPath + ".cs";
                        if (!File.Exists(codeBehindPath))
                        {
                            var codePath = FindCodeBehindForXaml(rootPath, xamlPath);
                            if (codePath == null)
                            {
                                report.Add($"[MISSING-CODEBEHIND] {xamlPath}: event handler '{handlerName}' requires code-behind file.");
                                fixes.Add(new FixSuggestion
                                {
                                    FilePath = xamlPath,
                                    Kind = FixKind.MissingCodeBehind,
                                    Context = handlerName,
                                    Suggested = $"Create code-behind file for {Path.GetFileName(xamlPath)} with DataContext and event handler '{handlerName}'."
                                });
                            }
                            else
                            {
                                if (!MethodExistsInCode(File.ReadAllText(codePath), handlerName))
                                {
                                    report.Add($"[MISSING-HANDLER] {codePath}: Missing method '{handlerName}' referenced in {xamlPath}.");
                                    fixes.Add(new FixSuggestion
                                    {
                                        FilePath = codePath,
                                        Kind = FixKind.MissingEventHandler,
                                        Context = handlerName,
                                        Suggested = $"Add event handler:\nprivate void {handlerName}(object sender, RoutedEventArgs e) {{ /* implement */ }}"
                                    });
                                }
                            }
                        }
                        else
                        {
                            var cbText = File.ReadAllText(codeBehindPath);
                            if (!MethodExistsInCode(cbText, handlerName))
                            {
                                report.Add($"[MISSING-HANDLER] {codeBehindPath}: Missing method '{handlerName}' referenced in {xamlPath}.");
                                fixes.Add(new FixSuggestion
                                {
                                    FilePath = codeBehindPath,
                                    Kind = FixKind.MissingEventHandler,
                                    Context = handlerName,
                                    Suggested = $"Add event handler:\nprivate void {handlerName}(object sender, RoutedEventArgs e) {{ /* implement */ }}"
                                });
                            }
                            // Check DataContext wiring
                            if (!HasDataContextWiring(cbText) && !HasDataContextInXaml(xamlText))
                            {
                                report.Add($"[MISSING-DATACONTEXT] {codeBehindPath}: No DataContext set for view {Path.GetFileName(xamlPath)}.");
                                fixes.Add(new FixSuggestion
                                {
                                    FilePath = codeBehindPath,
                                    Kind = FixKind.MissingDataContext,
                                    Suggested = $"Set DataContext = new {GuessViewModelForXaml(xamlPath)}(); in constructor after InitializeComponent()."
                                });
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    report.Add($"[XAML-PARSE-ERROR] {xamlPath}: {ex.Message}");
                }
            }

            // 3) Attempt safe auto-fixes (only apply if --apply given)
            var appliedFixes = new List<FixSuggestion>();
            foreach (var fix in fixes)
            {
                if (!applyChanges)
                    continue;

                try
                {
                    // Apply only the safe, straightforward fixes automatically:
                    switch (fix.Kind)
                    {
                        case FixKind.MissingEventHandler:
                            {
                                var path = fix.FilePath;
                                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                                {
                                    var code = File.ReadAllText(path);
                                    var handler = fix.Context;
                                    if (!string.IsNullOrEmpty(handler) && !MethodExistsInCode(code, handler))
                                    {
                                        var stub = GenerateEventHandlerStub(handler);
                                        code = AppendMethodToClass(code, stub);
                                        File.WriteAllText(path!, code, Encoding.UTF8);
                                        appliedFixes.Add(fix);
                                    }
                                }
                            }
                            break;

                        case FixKind.MissingDataContext:
                            {
                                var path = fix.FilePath;
                                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                                {
                                    var code = File.ReadAllText(path);
                                    var vmName = GuessViewModelForXaml((path ?? string.Empty).Replace(".xaml.cs", ""));
                                    var insertion = "// Auto-wired DataContext by AutoValidationTool\nDataContext = new " + (vmName ?? "") + "();\n";
                                    code = InsertDataContextInConstructor(code, insertion);
                                    File.WriteAllText(path!, code, Encoding.UTF8);
                                    appliedFixes.Add(fix);
                                }
                            }
                            break;

                        case FixKind.MissingProperty:
                            {
                                var path = fix.FilePath;
                                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                                {
                                    var code = File.ReadAllText(path);
                                    var prop = fix.ViewModelProperty;
                                    if (!string.IsNullOrEmpty(prop) && !PropertyExistsInCode(code, prop))
                                    {
                                        // Ensure INotify present
                                        if (!ImplementsINotify(code))
                                        {
                                            code = EnsureINotifyImplementation(code);
                                        }
                                        var propCode = GenerateViewModelProperty(prop);
                                        code = AppendPropertyToClass(code, propCode);
                                        File.WriteAllText(path!, code, Encoding.UTF8);
                                        appliedFixes.Add(fix);
                                    }
                                }
                            }
                            break;

                        case FixKind.MissingINotify:
                            {
                                var path = fix.FilePath;
                                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                                {
                                    var code = File.ReadAllText(path);
                                    if (!ImplementsINotify(code))
                                    {
                                        code = EnsureINotifyImplementation(code);
                                        File.WriteAllText(path!, code, Encoding.UTF8);
                                        appliedFixes.Add(fix);
                                    }
                                }
                            }
                            break;

                        case FixKind.AsyncFakeWrapper:
                            // Try to convert simple Task.FromResult wrapper to sync by removing async and Task.FromResult usage
                            {
                                var path = fix.FilePath;
                                if (!string.IsNullOrEmpty(path) && File.Exists(path))
                                {
                                    var code = File.ReadAllText(path);
                                    var changed = ReplaceTaskFromResultWrappers(code, out var newCode);
                                    if (changed)
                                    {
                                        File.WriteAllText(path!, newCode, Encoding.UTF8);
                                        appliedFixes.Add(fix);
                                    }
                                }
                            }
                            break;
                    }
                }
                catch (Exception ex)
                {
                    report.Add($"[FIX-ERROR] {fix.FilePath}: {ex.Message}");
                }
            }

            // 4) Output reports
            var reportPath = Path.Combine(rootPath, "FakeCodeReport.txt");
            File.WriteAllLines(reportPath, report);
            Console.WriteLine($"\nReport saved to: {reportPath}");
            var suggestionsPath = Path.Combine(rootPath, "FixSuggestions.json");
            if (!string.IsNullOrEmpty(suggestionsPath))
            {
                File.WriteAllText(suggestionsPath!, JsonSerializer.Serialize(fixes, SerializerOptions));
            }
            Console.WriteLine($"Fix suggestions saved to: {suggestionsPath}");

            if (applyChanges)
            {
                var appliedPath = Path.Combine(rootPath, "AppliedFixes.json");
                if (!string.IsNullOrEmpty(appliedPath))
                {
                    File.WriteAllText(appliedPath!, JsonSerializer.Serialize(appliedFixes, SerializerOptions));
                }
                Console.WriteLine($"Applied fixes recorded: {appliedPath}");
            }
            else
            {
                Console.WriteLine("\nDry-run mode (no files modified). Re-run with --apply to apply safe fixes automatically.");
            }

            Console.WriteLine("\n=== Complete ===");
            return 0;
        }

        private static bool HasDataContextWiring(string cbText)
        {
            if (string.IsNullOrEmpty(cbText)) return false;
            // simple checks for setting DataContext in code-behind
            return cbText.Contains("DataContext =", StringComparison.OrdinalIgnoreCase)
                || cbText.Contains("this.DataContext", StringComparison.OrdinalIgnoreCase)
                || cbText.Contains("SetValue(DataContextProperty", StringComparison.OrdinalIgnoreCase);
        }

        private static bool MethodExistsInCode(string code, string handler)
        {
            if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(handler)) return false;
            // look for method name followed by '(' — covers typical method/event handler declarations
            var pattern = $@"\b{Regex.Escape(handler)}\s*\(";
            return Regex.IsMatch(code, pattern);
        }

        // ---------------- Utility & Helpers ----------------

        private static string ExtractMethodBlock(string text, int startIndex)
        {
            int brace = 0;
            int i = text.IndexOf('{', startIndex);
            if (i < 0) return string.Empty;
            int start = i;
            for (; i < text.Length; i++)
            {
                if (text[i] == '{') brace++;
                else if (text[i] == '}') brace--;
                if (brace == 0 && i > start) break;
            }
            if (i >= text.Length) return text[start..];
            return text[start..(i + 1)];
        }

        private static bool PropertyExistsInCode(string code, string propName)
        {
            // Very simple check for property or field
            var patterns = new[]
            {
                $@"\b{Regex.Escape(propName)}\b\s*\{{", // property (escaped '{' as '{{')
                $@"\b{Regex.Escape(propName)}\b\s*;", // field
                $@"public\s+[A-Za-z0-9_<>,\s]+\s+{Regex.Escape(propName)}\s*\{{" // public prop
            };
            return patterns.Any(p => Regex.IsMatch(code, p));
        }

        private static bool HasDataContextInXaml(string xamlText)
        {
            return xamlText.Contains("DataContext=") || xamlText.Contains("{StaticResource ");
        }

        private static string? GuessViewModelForXaml(string xamlPath)
        {
            // Heuristic: take filename, remove .xaml, add ViewModel suffix
            try
            {
                var name = Path.GetFileNameWithoutExtension(xamlPath);
                // common patterns: MainWindow.xaml -> MainViewModel, MainView.xaml -> MainViewModel
                var candidate = name.Replace("Window", "").Replace("View", "");
                if (string.IsNullOrWhiteSpace(candidate)) return null;
                return string.Concat(candidate, "ViewModel");
            }
            catch { return null; }
        }

        private static string? FindViewModelFile(string root, string vmName)
        {
            var files = Directory.GetFiles(root, $"{vmName}.cs", SearchOption.AllDirectories);
            return files.FirstOrDefault(f => !f.Contains("\\bin\\") && !f.Contains("\\obj\\"));
        }

        private static string? FindCodeBehindForXaml(string root, string xamlPath)
        {
            // Try xaml.cs next to it
            var candidate = xamlPath + ".cs";
            if (File.Exists(candidate)) return candidate;
            // else search for partial class with same xaml root name
            var baseName = Path.GetFileNameWithoutExtension(xamlPath);
            var matches = Directory.GetFiles(root, "*.cs", SearchOption.AllDirectories)
                .Where(f => !f.Contains("\\bin\\") && !f.Contains("\\obj\\") && File.ReadAllText(f).Contains($"partial class {baseName}"))
                .ToArray();
            return matches.FirstOrDefault();
        }

        private static bool ImplementsINotify(string code)
        {
            return code.Contains("INotifyPropertyChanged") || code.Contains("PropertyChangedEventHandler");
        }

        private static string GenerateEventHandlerStub(string handlerName)
        {
            return $@"
        // Auto-generated event handler stub by AutoValidationTool
        // Implement this handler to call services or forward to your ViewModel.
        private void {handlerName}(object sender, System.Windows.RoutedEventArgs e)
        {{
            // Implement handler logic here.
        }}
";
        }

        private static string AppendMethodToClass(string code, string methodCode)
        {
            // naive: append before final }
            var idx = code.LastIndexOf('}');
            if (idx <= 0) return code + methodCode;
            return code.Substring(0, idx) + methodCode + code.Substring(idx);
        }

        private static string InsertDataContextInConstructor(string code, string insertion)
        {
            // Find constructor: public MainWindow() { InitializeComponent();
            var m = CtorPatternRegex.Match(code);
            if (m.Success)
            {
                var insertAt = m.Index + m.Length;
                return code.Insert(insertAt, "\n            " + insertion + "\n");
            }
            // fallback: append in class
            return AppendMethodToClass(code, $"// DataContext wiring\n// {insertion}\n");
        }

        private static string GenerateViewModelProperty(string propName)
        {
            var field = "_" + char.ToLower(propName[0]) + propName.Substring(1);
            return $@"
        private string {field};
        public string {propName}
        {{
            get => {field};
            set
            {{
                if ({field} != value)
                {{
                    {field} = value;
                    OnPropertyChanged(nameof({propName}));
                }}
            }}
        }}
";
        }

        private static string EnsureINotifyImplementation(string code)
        {
            if (ImplementsINotify(code)) return code;
            // Try to insert interface into class declaration
            var classPattern = new Regex(@"public\s+class\s+([A-Za-z0-9_]+)\s*(?:[:\w\s,<>]*)\{", RegexOptions.Singleline);
            var m = classPattern.Match(code);
            if (m.Success)
            {
                var classDecl = m.Value;
                var newDecl = classDecl.Replace("{", ": System.ComponentModel.INotifyPropertyChanged\n{");
                code = code.Replace(classDecl, newDecl);
            }

            // Add OnPropertyChanged and event at end of class
            var helper = @"

        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(name));
";
            return AppendMethodToClass(code, helper);
        }

        private static string AppendPropertyToClass(string code, string propCode)
        {
            return AppendMethodToClass(code, propCode);
        }

        private static bool ReplaceTaskFromResultWrappers(string code, out string newCode)
        {
            newCode = code;
            // Replace "return Task.FromResult(GetStatus());" with "return GetStatus();"
            if (TaskFromResultRegex.IsMatch(code))
            {
                newCode = TaskFromResultRegex.Replace(code, "return $1;");
                // Remove async modifier if method no longer uses await
                newCode = Regex.Replace(newCode, @"\basync\s+", "");
                return true;
            }

            // Replace methods returning Task.CompletedTask with void when appropriate (risky - skip)
            return false;
        }

        // ---------------- Types ----------------
        private enum FixKind
        {
            FakePattern,
            AsyncWithoutAwait,
            AsyncFakeWrapper,
            MissingViewModel,
            MissingProperty,
            MissingINotify,
            MissingEventHandler,
            MissingCodeBehind,
            MissingDataContext
        }

        private class FixSuggestion
        {
            public string? FilePath { get; set; }
            public FixKind Kind { get; set; }
            public int Line { get; set; }
            public string? Context { get; set; }
            public string? Suggested { get; set; }
            public string? ViewModelProperty { get; set; }
        }
    }
}

