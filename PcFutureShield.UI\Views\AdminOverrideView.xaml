<UserControl x:Class="PcFutureShield.UI.Views.AdminOverrideView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource PrimaryColorBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <ScrollViewer.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF0D7ED6" Offset="1"/>
                </LinearGradientBrush>
            </ScrollViewer.Background>
            <StackPanel Margin="20">
                <TextBlock Text="Admin Override Center" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- Main Controls -->
                <UniformGrid Columns="2" Rows="2" Margin="0,0,0,24">
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Request Admin Access" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Override Reason:" FontSize="14" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBox Text="{Binding OverrideReason, Mode=TwoWay}" Height="60" Margin="0,0,0,12" TextWrapping="Wrap" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}"/>
                            <TextBlock Text="Duration (minutes):" FontSize="14" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBox Text="{Binding MaxOverrideDurationMinutes, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,12" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                                <TextBox.InputScope>
                                    <InputScope>
                                        <InputScope.Names>
                                            <InputScopeName NameValue="Number" />
                                        </InputScope.Names>
                                    </InputScope>
                                </TextBox.InputScope>
                            </TextBox>
                            <Button Content="Request Override" Command="{Binding RequestOverrideCommand}" Style="{DynamicResource GlassButtonStyle}" Width="140" Height="36"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Active Sessions" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <ListBox ItemsSource="{Binding ActiveSessions}" Height="140" Margin="0,0,0,12" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding UserName}" FontSize="12" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                            <TextBlock Text="{Binding StartTime, StringFormat=Started: {0:g}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                            <TextBlock Text="{Binding Reason}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            <Button Content="Refresh Sessions" Command="{Binding ViewAdminLogsCommand}" Style="{DynamicResource ChromeButtonStyle}" Width="130" Height="32"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Pending Requests" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <ListBox ItemsSource="{Binding PendingRequests}" Height="140" Margin="0,0,0,12" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding UserName}" FontSize="12" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                            <TextBlock Text="{Binding RequestTime, StringFormat=Requested: {0:g}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                            <TextBlock Text="{Binding Reason}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                            <StackPanel Orientation="Horizontal" Margin="0,4,0,0">
                                                <Button Content="Approve" Command="{Binding DataContext.ApproveRequestCommand, RelativeSource={RelativeSource AncestorType=ListBox}}" CommandParameter="{Binding}" Style="{DynamicResource ChromeButtonStyle}" Width="60" Height="24" Margin="0,0,4,0"/>
                                                <Button Content="Deny" Command="{Binding DataContext.DenyRequestCommand, RelativeSource={RelativeSource AncestorType=ListBox}}" CommandParameter="{Binding}" Style="{DynamicResource ChromeButtonStyle}" Width="50" Height="24"/>
                                            </StackPanel>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Admin Statistics" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Active Sessions:" FontSize="16" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="{Binding ActiveSessions.Count}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,16"/>
                            <TextBlock Text="Pending Requests:" FontSize="16" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="{Binding PendingRequests.Count}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,24,0,0">
                    <Button Content="View Admin Logs" Command="{Binding ViewAdminLogsCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="160" Height="48" Margin="0,0,20,0"/>
                    <Button Content="End Override" Command="{Binding EndOverrideCommand}" Style="{DynamicResource GlassButtonStyle}" Width="160" Height="48"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
