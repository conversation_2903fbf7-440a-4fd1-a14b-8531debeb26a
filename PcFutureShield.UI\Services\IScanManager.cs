using System;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Services
{
    public interface IScanManager
    {
        event EventHandler<ScanResult> ScanCompleted;
        int GetThreatsBlocked();
        int GetFilesScanned();
        string GetLastScanTime();
        void RunFullSystemScan();
    }

    public class ScanResult
    {
        public DateTime EndTime { get; set; }
        // Add more properties as needed
    }
}
