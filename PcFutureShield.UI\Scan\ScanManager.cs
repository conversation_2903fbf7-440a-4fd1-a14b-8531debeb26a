using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using PcFutureShield.Engine.Scanning;
using PcFutureShield.Engine.VirusScanner;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.Scan
{
    public sealed class ScanManager : IScanManager
    {
        private readonly PcFutureShieldScanner _scanner;
        private readonly SettingsService _settings;
        private int _threatsBlocked;
        private int _filesScanned;
        private DateTime _lastScanTime;

        public event EventHandler<PcFutureShield.UI.Services.ScanResult>? ScanCompleted;

        public ScanManager()
        {
            try
            {
                // Try to reuse a registered signature DB if present
                SignatureDatabase sigDb = null;
                try { sigDb = Services.ServiceLocator.Get<SignatureDatabase>(); } catch { }
                if (sigDb == null)
                {
                    var sigPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Signatures", "signatures.db");
                    sigDb = new SignatureDatabase(sigPath);
                }
                _scanner = new PcFutureShieldScanner(sigDb);
                _settings = Services.ServiceLocator.Get<SettingsService>();
                _threatsBlocked = _settings.Get<int>("ThreatsBlocked", 0);
                _filesScanned = _settings.Get<int>("FilesScanned", 0);
                _lastScanTime = _settings.Get<DateTime>("LastScanTime", DateTime.MinValue);
            }
            catch (Exception ex)
            {
                try
                {
                    var notifier = Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>();
                    notifier.ShowError("PcFutureShield", $"ScanManager initialization failed: {ex.Message}\n{ex.StackTrace}");
                }
                catch
                {
                    System.Windows.MessageBox.Show($"ScanManager initialization failed: {ex.Message}\n{ex.StackTrace}", "PcFutureShield", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
                throw;
            }
        }

        public int GetThreatsBlocked() => _threatsBlocked;

        public int GetFilesScanned() => _filesScanned;

        public string GetLastScanTime() => _lastScanTime == DateTime.MinValue ? "Never" : _lastScanTime.ToString("g");

        public void RunFullSystemScan()
        {
            var folderPath = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
            int threatsFound = 0;
            int filesScanned = 0;
            foreach (var result in ScanFolder(folderPath))
            {
                filesScanned++;
                if (result.IsMalicious || (result.Threats != null && result.Threats.Count > 0))
                    threatsFound++;
            }
            _filesScanned += filesScanned;
            _threatsBlocked += threatsFound;
            _lastScanTime = DateTime.Now;
            PersistStats();
            ScanCompleted?.Invoke(this, new PcFutureShield.UI.Services.ScanResult { EndTime = _lastScanTime });
        }

        public IEnumerable<PcFutureShield.Engine.Scanning.ScanResult> ScanFolder(string folderPath)
        {
            if (!Directory.Exists(folderPath)) throw new DirectoryNotFoundException(folderPath);
            foreach (var file in EnumerateFilesSafe(folderPath))
            {
                PcFutureShield.Engine.Scanning.ScanResult r;
                try { r = _scanner.ScanFile(file); }
                catch { continue; }
                _filesScanned++;
                if (r.IsMalicious || (r.Threats != null && r.Threats.Count > 0))
                    _threatsBlocked++;
                yield return r;
            }
            _lastScanTime = DateTime.Now;
            PersistStats();
        }

        // Async variant that uses the scanner's async API (ScanFileAsync) so callers don't need to offload work themselves.
        public async Task<List<PcFutureShield.Engine.Scanning.ScanResult>> ScanFolderAsync(string folderPath)
        {
            var results = new List<PcFutureShield.Engine.Scanning.ScanResult>();
            if (!Directory.Exists(folderPath)) throw new DirectoryNotFoundException(folderPath);

            foreach (var file in EnumerateFilesSafe(folderPath))
            {
                try
                {
                    var r = await _scanner.ScanFileAsync(file).ConfigureAwait(false);
                    _filesScanned++;
                    if (r.IsMalicious || (r.Threats != null && r.Threats.Count > 0))
                        _threatsBlocked++;
                    results.Add(r);
                }
                catch
                {
                    // best-effort: skip files that fail scanning
                    continue;
                }
            }

            _lastScanTime = DateTime.Now;
            PersistStats();
            return results;
        }

        private void PersistStats()
        {
            _settings.Set("ThreatsBlocked", _threatsBlocked);
            _settings.Set("FilesScanned", _filesScanned);
            _settings.Set("LastScanTime", _lastScanTime);
        }

        private static IEnumerable<string> EnumerateFilesSafe(string root)
        {
            var stack = new Stack<string>();
            stack.Push(root);
            while (stack.Count > 0)
            {
                var dir = stack.Pop();
                string[] subdirs;
                try { subdirs = Directory.GetDirectories(dir); } catch { continue; }
                foreach (var s in subdirs) stack.Push(s);

                string[] files;
                try { files = Directory.GetFiles(dir); } catch { continue; }
                foreach (var f in files) yield return f;
            }
        }
    }
}
