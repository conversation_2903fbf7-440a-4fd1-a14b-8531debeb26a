<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/PcFutureShield.UI;component/Styles/SharedStyles.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Glossy 3D Theme Colors - Red backgrounds with Blue text fonts -->
    <SolidColorBrush x:Key="PrimaryColorBrush" Color="#FFFF0000"/>
    <SolidColorBrush x:Key="SecondaryColorBrush" Color="#FFFF6347"/>
    <SolidColorBrush x:Key="AccentColorBrush" Color="#FFFF4500"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#FF0A0A0A"/>
    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="#FF1A0F0F"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="#FF2E1A1A"/>
    <!-- Blue text fonts for 3D effect -->
    <SolidColorBrush x:Key="PrimaryFontBrush" Color="#FF0080FF"/>
    <SolidColorBrush x:Key="SecondaryFontBrush" Color="#FF4DA6FF"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#FF00FF7F"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FFFFD700"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#FFFF4444"/>
    <SolidColorBrush x:Key="BorderBrush" Color="#FFFF0000"/>

    <!-- Futuristic Chrome Button Style with Blue 3D Shadows -->
    <Style x:Key="ChromeButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Glow Background -->
                        <Border x:Name="GlowBorder" CornerRadius="8" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#33FF0000" Offset="0"/>
                                    <GradientStop Color="#22FF0000" Offset="0.5"/>
                                    <GradientStop Color="#11FF0000" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <!-- Blue 3D Shadow Effect -->
                            <Border.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="3" BlurRadius="18" Opacity="0.8"/>
                            </Border.Effect>
                        </Border>

                        <!-- Inner Chrome Layer -->
                        <Border CornerRadius="7" Margin="1" Background="{DynamicResource CardBackgroundBrush}"/>

                        <!-- Holographic Overlay -->
                        <Border CornerRadius="7" Margin="1">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#40FFFFFF" Offset="0"/>
                                    <GradientStop Color="#20FFFFFF" Offset="0.3"/>
                                    <GradientStop Color="#10FFFFFF" Offset="0.7"/>
                                    <GradientStop Color="#00FFFFFF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Cyber Grid Pattern -->
                        <Border CornerRadius="7" Margin="1" Opacity="0.1">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,20,20" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="#FFFF0000">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <LineGeometry StartPoint="0,0" EndPoint="20,0"/>
                                                    <LineGeometry StartPoint="0,0" EndPoint="0,20"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content with Blue 3D Text Shadow -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="315" ShadowDepth="2" BlurRadius="4" Opacity="0.9"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="GlowBorder" Property="Effect">
                                <Setter.Value>
                                    <!-- Enhanced Blue 3D Shadow on Hover -->
                                    <DropShadowEffect Color="#FF4DA6FF" Direction="270" ShadowDepth="5" BlurRadius="28" Opacity="1.0"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="GlowBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#55FF4500" Offset="0"/>
                                        <GradientStop Color="#44FF4500" Offset="0.5"/>
                                        <GradientStop Color="#33FF4500" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="GlowBorder" Property="Effect">
                                <Setter.Value>
                                    <!-- Pressed Blue 3D Shadow -->
                                    <DropShadowEffect Color="#FF0066CC" Direction="270" ShadowDepth="1" BlurRadius="12" Opacity="0.9"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="GlowBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#66FF0000" Offset="0"/>
                                        <GradientStop Color="#55FF0000" Offset="0.5"/>
                                        <GradientStop Color="#44FF0000" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Futuristic Mirror Button Style with Blue 3D Effects -->
    <Style x:Key="MirrorButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Mirror Base -->
                        <Border x:Name="MirrorBorder" CornerRadius="10" BorderThickness="2">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#FFFF0000" Offset="0"/>
                                    <GradientStop Color="#FFDC143C" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                            <Border.Background>
                                <RadialGradientBrush Center="0.5,0.3" RadiusX="1.2" RadiusY="1.2">
                                    <GradientStop Color="#FF1A0F0F" Offset="0"/>
                                    <GradientStop Color="#FF2E1A1A" Offset="0.4"/>
                                    <GradientStop Color="#FF3A0F0F" Offset="0.7"/>
                                    <GradientStop Color="#FF0A0A0A" Offset="1"/>
                                </RadialGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Holographic Reflection -->
                        <Border CornerRadius="10" Margin="2">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#40FF4500" Offset="0"/>
                                    <GradientStop Color="#20FF4500" Offset="0.3"/>
                                    <GradientStop Color="#10FF4500" Offset="0.7"/>
                                    <GradientStop Color="#00FF4500" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Inner Glow -->
                        <Border x:Name="InnerGlow" CornerRadius="8" Margin="3">
                            <Border.Background>
                                <RadialGradientBrush Center="0.5,0.2" RadiusX="0.8" RadiusY="0.8">
                                    <GradientStop Color="#30FF0000" Offset="0"/>
                                    <GradientStop Color="#20FF0000" Offset="0.5"/>
                                    <GradientStop Color="#00FF0000" Offset="1"/>
                                </RadialGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Cyber Circuit Pattern -->
                        <Border CornerRadius="8" Margin="4" Opacity="0.3">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,15,15" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="#FFFF0000">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <RectangleGeometry Rect="2,2,11,11"/>
                                                    <LineGeometry StartPoint="7,2" EndPoint="7,13"/>
                                                    <LineGeometry StartPoint="2,7" EndPoint="13,7"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content with Blue 3D Text Shadow -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="315" ShadowDepth="2" BlurRadius="5" Opacity="0.9"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>

                        <!-- Outer Blue 3D Glow Effect -->
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="6" BlurRadius="15" Opacity="0.7"/>
                            </Grid.Effect>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MirrorBorder" Property="Background">
                                <Setter.Value>
                                    <RadialGradientBrush Center="0.5,0.3" RadiusX="1.2" RadiusY="1.2">
                                        <GradientStop Color="#FF1A1A1A" Offset="0"/>
                                        <GradientStop Color="#FF2E2E2E" Offset="0.4"/>
                                        <GradientStop Color="#FF3A1A1A" Offset="0.7"/>
                                        <GradientStop Color="#FF1A0F0F" Offset="1"/>
                                    </RadialGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="InnerGlow" Property="Background">
                                <Setter.Value>
                                    <RadialGradientBrush Center="0.5,0.2" RadiusX="0.8" RadiusY="0.8">
                                        <GradientStop Color="#50FF4500" Offset="0"/>
                                        <GradientStop Color="#30FF4500" Offset="0.5"/>
                                        <GradientStop Color="#10FF4500" Offset="1"/>
                                    </RadialGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <!-- Enhanced Blue 3D Shadow on Hover -->
                                    <DropShadowEffect Color="#FF4DA6FF" Direction="270" ShadowDepth="10" BlurRadius="25" Opacity="1.0"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="MirrorBorder" Property="Background">
                                <Setter.Value>
                                    <RadialGradientBrush Center="0.5,0.7" RadiusX="1.2" RadiusY="1.2">
                                        <GradientStop Color="#FF0A0A0A" Offset="0"/>
                                        <GradientStop Color="#FF3A0F0F" Offset="0.4"/>
                                        <GradientStop Color="#FF2E1A1A" Offset="0.7"/>
                                        <GradientStop Color="#FF1A0F0F" Offset="1"/>
                                    </RadialGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <!-- Pressed Blue 3D Shadow -->
                                    <DropShadowEffect Color="#FF0066CC" Direction="270" ShadowDepth="3" BlurRadius="10" Opacity="0.8"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Futuristic Glass Button Style with Blue 3D Effects -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Glass Background -->
                        <Border x:Name="GlassBorder" CornerRadius="12" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#40FF0000" Offset="0"/>
                                    <GradientStop Color="#30FF0000" Offset="0.1"/>
                                    <GradientStop Color="#20FF0000" Offset="0.5"/>
                                    <GradientStop Color="#10FF0000" Offset="0.9"/>
                                    <GradientStop Color="#30FF0000" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Holographic Top Highlight -->
                        <Border CornerRadius="12,12,0,0" Height="18" VerticalAlignment="Top" Margin="1,1,1,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#60FF4500" Offset="0"/>
                                    <GradientStop Color="#20FF4500" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Inner Glass Layer -->
                        <Border CornerRadius="11" Margin="1" Background="{DynamicResource CardBackgroundBrush}" Opacity="0.8"/>

                        <!-- Cyber Grid Overlay -->
                        <Border CornerRadius="11" Margin="1" Opacity="0.2">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,10,10" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="#FFFF0000">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <LineGeometry StartPoint="0,0" EndPoint="10,0"/>
                                                    <LineGeometry StartPoint="0,0" EndPoint="0,10"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content with Blue 3D Text Glow -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="315" ShadowDepth="2" BlurRadius="6" Opacity="1.0"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>

                        <!-- Outer Blue 3D Glow -->
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="4" BlurRadius="18" Opacity="0.8"/>
                            </Grid.Effect>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#50FF4500" Offset="0"/>
                                        <GradientStop Color="#40FF4500" Offset="0.1"/>
                                        <GradientStop Color="#30FF4500" Offset="0.5"/>
                                        <GradientStop Color="#20FF4500" Offset="0.9"/>
                                        <GradientStop Color="#40FF4500" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <!-- Enhanced Blue 3D Glow on Hover -->
                                    <DropShadowEffect Color="#FF4DA6FF" Direction="270" ShadowDepth="6" BlurRadius="30" Opacity="1.0"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#30FF0000" Offset="0"/>
                                        <GradientStop Color="#20FF0000" Offset="0.1"/>
                                        <GradientStop Color="#40FF0000" Offset="0.5"/>
                                        <GradientStop Color="#50FF0000" Offset="0.9"/>
                                        <GradientStop Color="#60FF0000" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Futuristic Chrome Background Style with Blue 3D Shadow -->
    <Style x:Key="ChromeBackgroundStyle" TargetType="Border">
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#FF1A0F0F" Offset="0"/>
                    <GradientStop Color="#FF2E1A1A" Offset="0.2"/>
                    <GradientStop Color="#FF3A0F0F" Offset="0.5"/>
                    <GradientStop Color="#FF0A0A0A" Offset="0.8"/>
                    <GradientStop Color="#FF0A0A0A" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Effect">
            <Setter.Value>
                <!-- Blue 3D Shadow for Background -->
                <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="10" BlurRadius="25" Opacity="0.6"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Default Styles for Theme Consistency with Blue 3D Text Effects -->
    <Style TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource PrimaryColorBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder" CornerRadius="6" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                        <!-- Blue 3D Shadow for Button Border -->
                        <Border.Effect>
                            <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="3" BlurRadius="8" Opacity="0.6"/>
                        </Border.Effect>
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <!-- Blue 3D Text Shadow -->
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="#FF0080FF" Direction="315" ShadowDepth="1" BlurRadius="3" Opacity="0.8"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource SecondaryColorBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="Effect">
                                <Setter.Value>
                                    <!-- Enhanced Blue 3D Shadow on Hover -->
                                    <DropShadowEffect Color="#FF4DA6FF" Direction="270" ShadowDepth="5" BlurRadius="12" Opacity="0.9"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentColorBrush}"/>
                            <Setter TargetName="ButtonBorder" Property="Effect">
                                <Setter.Value>
                                    <!-- Pressed Blue 3D Shadow -->
                                    <DropShadowEffect Color="#FF0066CC" Direction="270" ShadowDepth="1" BlurRadius="6" Opacity="0.7"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <!-- Blue 3D Text Shadow for all TextBlocks -->
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#FF0080FF" Direction="315" ShadowDepth="1" BlurRadius="2" Opacity="0.7"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="5"/>
        <!-- Blue 3D Shadow for TextBox -->
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.5"/>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="Window">
        <Setter Property="Background" Value="{DynamicResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
    </Style>

    <Style TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <!-- Blue 3D Shadow for Borders -->
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#FF0080FF" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>
