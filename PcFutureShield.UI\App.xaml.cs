using Microsoft.Extensions.Logging;
using PcFutureShield.Engine.VirusScanner;
using System;
using System.Threading.Tasks;
using System.Windows;
using PcFutureShield.UI.Services;
using PcFutureShield.UI.Scan;
using PcFutureShield.Common.Services;

namespace PcFutureShield.UI
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private ILoggerFactory? _loggerFactory;
        private readonly LoggingService _loggingService = LoggingService.Instance;
        private Views.SplashWindow? _splash;

        public App()
        {
            _loggingService.LogInfo("App", "Application starting up");

            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            _loggingService.LogInfo("App", "Exception handlers registered");
        }

        private async Task InitializeServicesAsync(Views.SplashWindow? splash)
        {
            try
            {
                void Update(double p, string msg)
                {
                    try
                    {
                        splash?.Dispatcher.Invoke(() =>
                        {
                            splash.SetProgress(p);
                            splash.SetMessage(msg);
                        });
                    }
                    catch { }
                }

                Update(5, "Registering services...");

                // Register settings service first (dependency for ScanManager)
                ServiceLocator.Register<SettingsService>(new SettingsService());

                Update(15, "Initializing core services...");
                ServiceLocator.Register<IScanManager>(new ScanManager());
                ServiceLocator.Register<PcFutureShield.Common.Interfaces.IQuarantineManager>(new PcFutureShield.Engine.Quarantine.QuarantineManager());
                ServiceLocator.Register<IRealtimeProtectionService>(new RealtimeProtectionService());
                ServiceLocator.Register<IEventLogService>(new EventLogService());
                ServiceLocator.Register<ILicenseManager>(new LicenseManager());

                Update(35, "Starting analysis engines...");
                var threatIntelligenceService = new ThreatIntelligenceService();
                var behavioralAnalysisService = new BehavioralAnalysisService();
                var zeroDayDetectionService = new ZeroDayDetectionService(behavioralAnalysisService, threatIntelligenceService);
                var aiDetectionService = new AdvancedAIDetectionService();

                ServiceLocator.Register<ThreatIntelligenceService>(threatIntelligenceService);
                ServiceLocator.Register<BehavioralAnalysisService>(behavioralAnalysisService);
                ServiceLocator.Register<ZeroDayDetectionService>(zeroDayDetectionService);
                ServiceLocator.Register<AdvancedAIDetectionService>(aiDetectionService);

                var antivirusOrchestrator = new AntivirusOrchestrator(
                    zeroDayDetectionService,
                    aiDetectionService,
                    behavioralAnalysisService,
                    threatIntelligenceService,
                    Microsoft.Extensions.Logging.Abstractions.NullLogger<AntivirusOrchestrator>.Instance);

                ServiceLocator.Register<AntivirusOrchestrator>(antivirusOrchestrator);
                ServiceLocator.Register<ParentalControlService>(new ParentalControlService());
                ServiceLocator.Register<GamingProtectionService>(new GamingProtectionService());
                ServiceLocator.Register<PcOptimizationService>(new PcOptimizationService());
                ServiceLocator.Register<AdminOverrideService>(new AdminOverrideService());
                ServiceLocator.Register<BrowserExtensionService>(new BrowserExtensionService());

                Update(60, "Wiring scanner and update services...");
                // Setup logging and register VirusScannerService
                _loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var virusScannerLogger = _loggerFactory.CreateLogger<VirusScannerService>();
                var virusScannerService = new VirusScannerService(virusScannerLogger);

                ServiceLocator.Register<PcFutureShield.UI.ViewModels.IVirusScannerService>(
                    new PcFutureShield.UI.ViewModels.VirusScannerServiceAdapter(virusScannerService)
                );

                // Register notification service for background tasks
                ServiceLocator.Register<INotificationService>(new WpfNotificationService());

                Update(85, "Finalizing startup...");

                // Small delay to let UI appear polished
                await Task.Delay(250).ConfigureAwait(false);

                // Open main window on UI thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var main = new MainWindow();
                    main.Show();
                    splash?.Close();
                    splash = null;
                });

                _loggingService.LogInfo("App", "All services registered successfully");
            }
            catch (Exception ex)
            {
                _loggingService.LogFatal("App", "Failed during background initialization", ex);
                LogExceptionToFile(ex, "OnStartupBackground");
                Application.Current.Dispatcher.Invoke(() =>
                {
                    try
                    {
                        // Prefer the registered notifier
                        var notifier = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                        notifier?.ShowError("Startup Error", $"Failed to initialize application: {ex.Message}. See logs for details.");
                    }
                    catch { }

                    // Close splash and open main window with a fallback view so user can inspect
                    splash?.Close();
                    splash = null;

                    try
                    {
                        var main = new MainWindow();
                        // show a fallback view with error details so user can see what happened
                        main.DataContext = new PcFutureShield.UI.ViewModels.FallbackViewModel("Startup Error", ex.Message + "\n" + ex.StackTrace);
                        main.Show();
                    }
                    catch
                    {
                        // If even MainWindow fails, ensure app doesn't crash silently
                    }
                    // Do not call Shutdown here; keep app running for inspection
                });
            }
        }

        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                _loggingService.LogInfo("App", "OnStartup called");
                base.OnStartup(e);

                // Show splash window while we initialize heavy services
                _splash = new Views.SplashWindow();
                _splash.Show();

                // Start async initialization (real async, not wrapped in Task.Run)
                _ = InitializeServicesAsync(_splash);
            }
            catch (Exception ex)
            {
                _loggingService.LogFatal("App", "Failed during OnStartup", ex);
                LogExceptionToFile(ex, "OnStartup");
                throw;
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _loggingService.LogInfo("App", "Application exiting");
            _loggerFactory?.Dispose();
            base.OnExit(e);
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            _loggingService.LogError("App", $"DispatcherUnhandledException: {e.Exception.Message}", e.Exception);
            LogExceptionToFile(e.Exception, "DispatcherUnhandledException");

            // Show error dialog but don't close the app immediately
            var result = System.Windows.MessageBox.Show(
                $"An unexpected error occurred:\n{e.Exception.Message}\n\nWould you like to continue running the application?\n\nDetailed error information has been logged to:\n{_loggingService.GetLogFilePath()}",
                "Application Error",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Error);

            if (result == MessageBoxResult.Yes)
            {
                e.Handled = true; // Continue running
                _loggingService.LogInfo("App", "User chose to continue after error");
            }
            else
            {
                _loggingService.LogInfo("App", "User chose to exit after error");
                e.Handled = false; // Let the app close
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                _loggingService.LogFatal("App", "AppDomain.UnhandledException", ex);
                LogExceptionToFile(ex, "AppDomain.UnhandledException");
            }
        }

        private static void LogExceptionToFile(Exception ex, string source)
        {
            try
            {
                string logPath = System.IO.Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "fatal.log");
                System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(logPath)!);
                using var sw = new System.IO.StreamWriter(logPath, true);
                sw.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] [{source}] {ex.Message}\n{ex.StackTrace}\n");
            }
            catch { /* ignore logging errors */ }
        }
    }
}
