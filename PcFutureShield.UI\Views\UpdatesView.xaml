<UserControl x:Class="PcFutureShield.UI.Views.UpdatesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <TextBlock Text="Software Updates" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- Update Status -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Update Status" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="3" Rows="1">
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Current Version" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding CurrentVersion}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Latest Version" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding LatestVersion}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Updates Available" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding UpdatesAvailable}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Available Updates -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Available Updates" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <ListBox ItemsSource="{Binding AvailableUpdates}" Height="200" Margin="0,0,0,15" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel>
                                        <TextBlock Text="{Binding UpdateName}" FontSize="14" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                        <TextBlock Text="{Binding UpdateDescription}" FontSize="12" Foreground="{DynamicResource SecondaryFontBrush}" TextWrapping="Wrap" Margin="0,2,0,0"/>
                                        <TextBlock Text="{Binding UpdateSize, StringFormat=Size: {0}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,2,0,0"/>
                                        <TextBlock Text="{Binding ReleaseDate, StringFormat=Released: {0:d}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </StackPanel>
                </Border>

                <!-- Update Settings -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Update Settings" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="2">
                            <CheckBox Content="Automatic Updates" IsChecked="{Binding AutomaticUpdatesEnabled}" Margin="5" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Download Updates in Background" IsChecked="{Binding BackgroundDownloadEnabled}" Margin="5" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Notify Before Installing" IsChecked="{Binding NotifyBeforeInstall}" Margin="5" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Include Beta Releases" IsChecked="{Binding IncludeBetaReleases}" Margin="5" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Update Progress -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Update Progress" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <TextBlock Text="{Binding CurrentUpdateOperation}" FontSize="16" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,10"/>
                        <ProgressBar Value="{Binding UpdateProgress}" Height="20" Margin="0,0,0,10" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryColorBrush}"/>
                        <TextBlock Text="{Binding UpdateStatus}" FontSize="14" Foreground="{DynamicResource PrimaryFontBrush}" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Check for Updates" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding CheckForUpdatesCommand}" Margin="10"/>
                    <Button Content="Download Updates" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding DownloadUpdatesCommand}" Margin="10"/>
                    <Button Content="Install Updates" Style="{DynamicResource MirrorButtonStyle}" Command="{Binding InstallUpdatesCommand}" Margin="10"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
