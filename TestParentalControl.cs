using System;
using System.Threading.Tasks;
using PcFutureShield.Common.Services;

namespace PcFutureShield.Test
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== PcFutureShield Parental Control Test ===\n");
            
            try
            {
                // Create test service
                var testService = new ParentalControlTestService();
                
                Console.WriteLine("Running comprehensive parental control blocking tests...\n");
                Console.WriteLine("⚠️  NOTE: This test requires Administrator privileges to modify system files.");
                Console.WriteLine("⚠️  The test will modify your hosts file and DNS settings temporarily.\n");
                
                Console.Write("Press Enter to continue or Ctrl+C to cancel...");
                Console.ReadLine();
                
                // Run the test
                var result = await testService.TestBlockingAsync();
                
                // Generate and display report
                var report = testService.GenerateTestReport(result);
                Console.WriteLine(report);
                
                // Cleanup option
                Console.WriteLine("\n" + new string('=', 50));
                Console.Write("Would you like to disable all blocking for cleanup? (y/N): ");
                var cleanup = Console.ReadLine();
                
                if (cleanup?.ToLower() == "y" || cleanup?.ToLower() == "yes")
                {
                    Console.WriteLine("Disabling all blocking...");
                    var cleanupSuccess = await testService.DisableAllBlockingAsync();
                    Console.WriteLine(cleanupSuccess ? "✅ Cleanup completed successfully." : "❌ Cleanup failed.");
                }
                
                Console.WriteLine("\nTest completed. Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed with error: {ex.Message}");
                Console.WriteLine("\nThis usually means:");
                Console.WriteLine("1. The application needs to run as Administrator");
                Console.WriteLine("2. Windows Defender or antivirus is blocking the operation");
                Console.WriteLine("3. Network configuration prevents the changes");
                
                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
            }
        }
    }
}
