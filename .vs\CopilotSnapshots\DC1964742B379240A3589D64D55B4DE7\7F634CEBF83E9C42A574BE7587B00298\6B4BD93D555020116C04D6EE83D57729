﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using PcFutureShield.Common.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class PcOptimizationViewModel : BaseViewModel
    {
        private readonly PcOptimizationService _optimizationService;

        // System Health Properties
        private SystemHealthReport _currentHealthReport;
        public SystemHealthReport CurrentHealthReport
        {
            get => _currentHealthReport;
            set => SetProperty(ref _currentHealthReport, value);
        }

        private bool _isAnalyzing;
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set => SetProperty(ref _isAnalyzing, value);
        }

        // Optimization Properties
        private OptimizationResult _lastOptimizationResult;
        public OptimizationResult LastOptimizationResult
        {
            get => _lastOptimizationResult;
            set => SetProperty(ref _lastOptimizationResult, value);
        }

        private bool _isOptimizing;
        public bool IsOptimizing
        {
            get => _isOptimizing;
            set => SetProperty(ref _isOptimizing, value);
        }

        // Repair Properties
        private RepairResult _lastRepairResult;
        public RepairResult LastRepairResult
        {
            get => _lastRepairResult;
            set => SetProperty(ref _lastRepairResult, value);
        }

        private bool _isRepairing;
        public bool IsRepairing
        {
            get => _isRepairing;
            set => SetProperty(ref _isRepairing, value);
        }

        // Performance Boost Properties
        private PerformanceBoostResult _lastBoostResult;
        public PerformanceBoostResult LastBoostResult
        {
            get => _lastBoostResult;
            set => SetProperty(ref _lastBoostResult, value);
        }

        private bool _isBoosting;
        public bool IsBoosting
        {
            get => _isBoosting;
            set => SetProperty(ref _isBoosting, value);
        }

        // Uninstall Properties
        private ObservableCollection<string> _programsToUninstall;
        public ObservableCollection<string> ProgramsToUninstall
        {
            get => _programsToUninstall;
            set => SetProperty(ref _programsToUninstall, value);
        }

        private string _newProgramToAdd;
        public string NewProgramToAdd
        {
            get => _newProgramToAdd;
            set => SetProperty(ref _newProgramToAdd, value);
        }

        private UninstallResult _lastUninstallResult;
        public UninstallResult LastUninstallResult
        {
            get => _lastUninstallResult;
            set => SetProperty(ref _lastUninstallResult, value);
        }

        private bool _isUninstalling;
        public bool IsUninstalling
        {
            get => _isUninstalling;
            set => SetProperty(ref _isUninstalling, value);
        }

        // Commands
        public ICommand AnalyzeSystemCommand { get; }
        public ICommand PerformOptimizationCommand { get; }
        public ICommand PerformRepairCommand { get; }
        public ICommand BoostPerformanceCommand { get; }
        public ICommand AddProgramCommand { get; }
        public ICommand RemoveProgramCommand { get; }
        public ICommand PerformUninstallCommand { get; }

        public PcOptimizationViewModel(PcOptimizationService optimizationService)
        {
            _optimizationService = optimizationService ?? throw new ArgumentNullException(nameof(optimizationService));

            ProgramsToUninstall = new ObservableCollection<string>();

            // Initialize commands
            AnalyzeSystemCommand = new RelayCommand(async () => await AnalyzeSystemAsync());
            PerformOptimizationCommand = new RelayCommand(async () => await PerformOptimizationAsync());
            PerformRepairCommand = new RelayCommand(async () => await PerformRepairAsync());
            BoostPerformanceCommand = new RelayCommand(async () => await BoostPerformanceAsync());
            AddProgramCommand = new RelayCommand(AddProgram);
            RemoveProgramCommand = new RelayCommand<string>(RemoveProgram);
            PerformUninstallCommand = new RelayCommand(async () => await PerformUninstallAsync());
        }

        private async Task AnalyzeSystemAsync()
        {
            if (IsAnalyzing) return;

            IsAnalyzing = true;
            try
            {
                CurrentHealthReport = await _optimizationService.GenerateSystemHealthReportAsync();
            }
            catch (Exception ex)
            {
                // Handle error - could show message to user
                Console.WriteLine($"Error analyzing system: {ex.Message}");
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        private async Task PerformOptimizationAsync()
        {
            if (IsOptimizing) return;

            IsOptimizing = true;
            try
            {
                var options = new OptimizationOptions
                {
                    CleanTempFiles = true,
                    RemoveBloatware = true,
                    OptimizeStartup = true,
                    DefragmentDrives = true,
                    OptimizeMemory = true,
                    CleanRegistry = true
                };

                LastOptimizationResult = await _optimizationService.PerformDeepOptimizationAsync(options);
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error performing optimization: {ex.Message}");
            }
            finally
            {
                IsOptimizing = false;
            }
        }

        private async Task PerformRepairAsync()
        {
            if (IsRepairing) return;

            IsRepairing = true;
            try
            {
                var options = new RepairOptions
                {
                    RepairSystemFiles = true,
                    FixWindowsCorruption = true,
                    RepairBootIssues = true,
                    FixDriverIssues = true,
                    RepairNetworkIssues = true
                };

                LastRepairResult = await _optimizationService.PerformSystemRepairAsync(options);
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error performing repair: {ex.Message}");
            }
            finally
            {
                IsRepairing = false;
            }
        }

        private async Task BoostPerformanceAsync()
        {
            if (IsBoosting) return;

            IsBoosting = true;
            try
            {
                LastBoostResult = await _optimizationService.BoostPerformanceAsync();
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error boosting performance: {ex.Message}");
            }
            finally
            {
                IsBoosting = false;
            }
        }

        private void AddProgram()
        {
            if (!string.IsNullOrWhiteSpace(NewProgramToAdd) && !ProgramsToUninstall.Contains(NewProgramToAdd))
            {
                ProgramsToUninstall.Add(NewProgramToAdd);
                NewProgramToAdd = string.Empty;
            }
        }

        private void RemoveProgram(string program)
        {
            if (!string.IsNullOrEmpty(program))
            {
                ProgramsToUninstall.Remove(program);
            }
        }

        private async Task PerformUninstallAsync()
        {
            if (IsUninstalling || !ProgramsToUninstall.Any()) return;

            IsUninstalling = true;
            try
            {
                LastUninstallResult = await _optimizationService.PerformSmartUninstallAsync(ProgramsToUninstall.ToList());
                ProgramsToUninstall.Clear();
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error performing uninstall: {ex.Message}");
            }
            finally
            {
                IsUninstalling = false;
            }
        }
    }
}
