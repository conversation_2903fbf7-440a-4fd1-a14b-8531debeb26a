﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class SettingsViewModel : BaseViewModel
    {
        private readonly SettingsService _settingsService;

        // Appearance Settings
        private string _selectedTheme;
        public string SelectedTheme
        {
            get => _selectedTheme;
            set
            {
                if (_selectedTheme != value)
                {
                    _selectedTheme = value;
                    _settingsService.Set("Theme", value);
                    OnPropertyChanged();
                }
            }
        }

        public List<string> AvailableThemes { get; } = new() { "GlossyBlue", "GlossyGreen", "GlossyMidnightBlue", "GlossyPurple", "GlossyRed" };

        private bool _enableAnimations;
        public bool EnableAnimations
        {
            get => _enableAnimations;
            set => SetProperty(ref _enableAnimations, value);
        }

        private bool _showNotifications;
        public bool ShowNotifications
        {
            get => _showNotifications;
            set => SetProperty(ref _showNotifications, value);
        }

        private bool _autoStartWithWindows;
        public bool AutoStartWithWindows
        {
            get => _autoStartWithWindows;
            set => SetProperty(ref _autoStartWithWindows, value);
        }

        // Security Settings
        private bool _realTimeProtectionEnabled;
        public bool RealTimeProtectionEnabled
        {
            get => _realTimeProtectionEnabled;
            set => SetProperty(ref _realTimeProtectionEnabled, value);
        }

        private bool _autoUpdateDefinitions;
        public bool AutoUpdateDefinitions
        {
            get => _autoUpdateDefinitions;
            set => SetProperty(ref _autoUpdateDefinitions, value);
        }

        private bool _cloudProtectionEnabled;
        public bool CloudProtectionEnabled
        {
            get => _cloudProtectionEnabled;
            set => SetProperty(ref _cloudProtectionEnabled, value);
        }

        private bool _scanRemovableDrives;
        public bool ScanRemovableDrives
        {
            get => _scanRemovableDrives;
            set => SetProperty(ref _scanRemovableDrives, value);
        }

        private string _selectedScanSchedule;
        public string SelectedScanSchedule
        {
            get => _selectedScanSchedule;
            set => SetProperty(ref _selectedScanSchedule, value);
        }

        public List<string> ScanSchedules { get; } = new() { "Daily", "Weekly", "Monthly", "Never" };

        // Performance Settings
        private bool _lowResourceMode;
        public bool LowResourceMode
        {
            get => _lowResourceMode;
            set => SetProperty(ref _lowResourceMode, value);
        }

        private bool _backgroundScanningEnabled;
        public bool BackgroundScanningEnabled
        {
            get => _backgroundScanningEnabled;
            set => SetProperty(ref _backgroundScanningEnabled, value);
        }

        private double _cpuUsageLimit;
        public double CpuUsageLimit
        {
            get => _cpuUsageLimit;
            set => SetProperty(ref _cpuUsageLimit, value);
        }

        // Privacy Settings
        private bool _sendAnonymousStats;
        public bool SendAnonymousStats
        {
            get => _sendAnonymousStats;
            set => SetProperty(ref _sendAnonymousStats, value);
        }

        private bool _enableCrashReporting;
        public bool EnableCrashReporting
        {
            get => _enableCrashReporting;
            set => SetProperty(ref _enableCrashReporting, value);
        }

        private bool _storeScanHistory;
        public bool StoreScanHistory
        {
            get => _storeScanHistory;
            set => SetProperty(ref _storeScanHistory, value);
        }

        private int _dataRetentionDays;
        public int DataRetentionDays
        {
            get => _dataRetentionDays;
            set => SetProperty(ref _dataRetentionDays, value);
        }

        // About Section
        private string _applicationVersion;
        public string ApplicationVersion
        {
            get => _applicationVersion;
            set => SetProperty(ref _applicationVersion, value);
        }

        private string _licenseStatus;
        public string LicenseStatus
        {
            get => _licenseStatus;
            set => SetProperty(ref _licenseStatus, value);
        }

        private string _lastUpdateCheck;
        public string LastUpdateCheck
        {
            get => _lastUpdateCheck;
            set => SetProperty(ref _lastUpdateCheck, value);
        }

        // Commands
        public ICommand SaveSettingsCommand { get; }
        public ICommand ResetToDefaultsCommand { get; }
        public ICommand CheckForUpdatesCommand { get; }
        public ICommand ClearAllDataCommand { get; }

        public SettingsViewModel()
        {
            _settingsService = PcFutureShield.UI.Services.ServiceLocator.Get<SettingsService>();

            // Load settings from service
            SelectedTheme = _settingsService.Get("Theme", "GlossyBlue");
            EnableAnimations = _settingsService.Get("EnableAnimations", true);
            ShowNotifications = _settingsService.Get("ShowNotifications", true);
            AutoStartWithWindows = _settingsService.Get("AutoStartWithWindows", false);
            RealTimeProtectionEnabled = _settingsService.Get("RealTimeProtectionEnabled", true);
            AutoUpdateDefinitions = _settingsService.Get("AutoUpdateDefinitions", true);
            CloudProtectionEnabled = _settingsService.Get("CloudProtectionEnabled", true);
            ScanRemovableDrives = _settingsService.Get("ScanRemovableDrives", true);
            SelectedScanSchedule = _settingsService.Get("SelectedScanSchedule", "Weekly");
            LowResourceMode = _settingsService.Get("LowResourceMode", false);
            BackgroundScanningEnabled = _settingsService.Get("BackgroundScanningEnabled", true);
            CpuUsageLimit = _settingsService.Get("CpuUsageLimit", 50.0);
            SendAnonymousStats = _settingsService.Get("SendAnonymousStats", false);
            EnableCrashReporting = _settingsService.Get("EnableCrashReporting", true);
            StoreScanHistory = _settingsService.Get("StoreScanHistory", true);
            DataRetentionDays = _settingsService.Get("DataRetentionDays", 30);

            // About section
            ApplicationVersion = "1.0.0";
            LicenseStatus = "Licensed";
            LastUpdateCheck = DateTime.Now.ToString("yyyy-MM-dd");

            // Initialize commands
            SaveSettingsCommand = new RelayCommand(SaveSettings);
            ResetToDefaultsCommand = new RelayCommand(ResetToDefaults);
            CheckForUpdatesCommand = new RelayCommand(CheckForUpdates);
            ClearAllDataCommand = new RelayCommand(ClearAllData);
        }

        private void SaveSettings()
        {
            try
            {
                _settingsService.Set("Theme", SelectedTheme);
                _settingsService.Set("EnableAnimations", EnableAnimations);
                _settingsService.Set("ShowNotifications", ShowNotifications);
                _settingsService.Set("AutoStartWithWindows", AutoStartWithWindows);
                _settingsService.Set("RealTimeProtectionEnabled", RealTimeProtectionEnabled);
                _settingsService.Set("AutoUpdateDefinitions", AutoUpdateDefinitions);
                _settingsService.Set("CloudProtectionEnabled", CloudProtectionEnabled);
                _settingsService.Set("ScanRemovableDrives", ScanRemovableDrives);
                _settingsService.Set("SelectedScanSchedule", SelectedScanSchedule);
                _settingsService.Set("LowResourceMode", LowResourceMode);
                _settingsService.Set("BackgroundScanningEnabled", BackgroundScanningEnabled);
                _settingsService.Set("CpuUsageLimit", CpuUsageLimit);
                _settingsService.Set("SendAnonymousStats", SendAnonymousStats);
                _settingsService.Set("EnableCrashReporting", EnableCrashReporting);
                _settingsService.Set("StoreScanHistory", StoreScanHistory);
                _settingsService.Set("DataRetentionDays", DataRetentionDays);

                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Settings Saved", "Settings saved successfully!"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show("Settings saved successfully!", "Settings Saved", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
            }
            catch (System.Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Save Error", $"Failed to save settings: {ex.Message}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show($"Failed to save settings: {ex.Message}", "Save Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error)); }
            }
        }

        private void ResetToDefaults()
        {
            var result = System.Windows.MessageBoxResult.No;
            try { System.Windows.Application.Current?.Dispatcher.Invoke(() => { result = System.Windows.MessageBox.Show("Reset all settings to defaults?", "Confirm Reset", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question); }); } catch { result = System.Windows.MessageBoxResult.No; }
            if (result == System.Windows.MessageBoxResult.Yes)
            {
                SelectedTheme = "GlossyBlue";
                EnableAnimations = true;
                ShowNotifications = true;
                AutoStartWithWindows = false;
                RealTimeProtectionEnabled = true;
                AutoUpdateDefinitions = true;
                CloudProtectionEnabled = true;
                ScanRemovableDrives = true;
                SelectedScanSchedule = "Weekly";
                LowResourceMode = false;
                BackgroundScanningEnabled = true;
                CpuUsageLimit = 50.0;
                SendAnonymousStats = false;
                EnableCrashReporting = true;
                StoreScanHistory = true;
                DataRetentionDays = 30;

                SaveSettings();
            }
        }

        private void CheckForUpdates()
        {
            try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Update Check", "Checking for updates..."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show("Checking for updates...", "Update Check", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
            // In a real implementation, this would check for updates
            LastUpdateCheck = System.DateTime.Now.ToString("yyyy-MM-dd");
        }

        private void ClearAllData()
        {
            var result = System.Windows.MessageBoxResult.No;
            try { System.Windows.Application.Current?.Dispatcher.Invoke(() => { result = System.Windows.MessageBox.Show("This will clear all stored data. Continue?", "Confirm Clear", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Warning); }); } catch { result = System.Windows.MessageBoxResult.No; }
            if (result == System.Windows.MessageBoxResult.Yes)
            {
                // In a real implementation, this would clear all data
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Data Cleared", "All data cleared successfully."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show("All data cleared successfully.", "Data Cleared", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
            }
        }


    }
}

