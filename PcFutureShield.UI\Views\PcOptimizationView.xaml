<UserControl x:Class="PcFutureShield.UI.Views.PcOptimizationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <TextBlock Text="PC Optimization" FontSize="32" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" HorizontalAlignment="Center" Margin="0,0,0,30"/>

                <!-- System Performance Metrics -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="System Performance" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="2">
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="CPU Usage" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0}%}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Memory Usage" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding MemoryUsage, StringFormat={}{0}%}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Disk Usage" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding DiskUsage, StringFormat={}{0}%}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Performance Score" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding PerformanceScore, StringFormat={}{0}/100}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Optimization Actions -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Optimization Actions" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="3">
                            <Button Content="Clean Temporary Files" Style="{DynamicResource GlassButtonStyle}" Command="{Binding CleanTempFilesCommand}" Margin="5"/>
                            <Button Content="Defragment Drives" Style="{DynamicResource GlassButtonStyle}" Command="{Binding DefragmentCommand}" Margin="5"/>
                            <Button Content="Clear Cache" Style="{DynamicResource GlassButtonStyle}" Command="{Binding ClearCacheCommand}" Margin="5"/>
                            <Button Content="Optimize Startup" Style="{DynamicResource GlassButtonStyle}" Command="{Binding OptimizeStartupCommand}" Margin="5"/>
                            <Button Content="Update Drivers" Style="{DynamicResource GlassButtonStyle}" Command="{Binding UpdateDriversCommand}" Margin="5"/>
                            <Button Content="Full System Optimization" Style="{DynamicResource GlassButtonStyle}" Command="{Binding FullOptimizationCommand}" Margin="5"/>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Optimization Status -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Optimization Status" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <TextBlock Text="{Binding OptimizationStatus}" FontSize="16" Foreground="{DynamicResource SecondaryFontBrush}" TextWrapping="Wrap"/>
                        <ProgressBar Value="{Binding OptimizationProgress}" Height="20" Margin="0,10,0,0" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryColorBrush}"/>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Analyze System" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding AnalyzeSystemCommand}" Margin="10"/>
                    <Button Content="Generate Report" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding GenerateReportCommand}" Margin="10"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
