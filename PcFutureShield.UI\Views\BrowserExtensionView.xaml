<UserControl x:Class="PcFutureShield.UI.Views.BrowserExtensionView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <TextBlock Text="Browser Extension" FontSize="32" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" HorizontalAlignment="Center" Margin="0,0,0,30"/>

                <!-- Extension Status -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Extension Status" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="1">
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Extension Installed" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding IsExtensionInstalled, StringFormat={}{0}}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Extension Enabled" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding IsExtensionEnabled, StringFormat={}{0}}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Browser Protection Features -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Protection Features" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="3">
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Malicious Sites Blocked" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding MaliciousSitesBlocked}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource ErrorBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Phishing Attempts Prevented" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding PhishingAttemptsPrevented}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Safe Downloads" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding SafeDownloads}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Tracking Cookies Blocked" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding TrackingCookiesBlocked}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Adware Detected" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding AdwareDetected}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Extension Version" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding ExtensionVersion}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Extension Management -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Extension Management" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="2">
                            <Button Content="Install Extension" Style="{DynamicResource GlassButtonStyle}" Command="{Binding InstallExtensionCommand}" Margin="5"/>
                            <Button Content="Update Extension" Style="{DynamicResource GlassButtonStyle}" Command="{Binding UpdateExtensionCommand}" Margin="5"/>
                            <Button Content="Configure Settings" Style="{DynamicResource GlassButtonStyle}" Command="{Binding ConfigureExtensionCommand}" Margin="5"/>
                            <Button Content="View Extension Logs" Style="{DynamicResource GlassButtonStyle}" Command="{Binding ViewExtensionLogsCommand}" Margin="5"/>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Supported Browsers -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Supported Browsers" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <ListBox ItemsSource="{Binding SupportedBrowsers}" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}" Margin="0,0,0,10"/>
                        <TextBlock Text="Current Browser: " FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,10,0,0"/>
                        <TextBlock Text="{Binding CurrentBrowser}" FontSize="16" Foreground="{DynamicResource PrimaryFontBrush}"/>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Scan Browser" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding ScanBrowserCommand}" Margin="10"/>
                    <Button Content="Clear Browser Data" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding ClearBrowserDataCommand}" Margin="10"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
