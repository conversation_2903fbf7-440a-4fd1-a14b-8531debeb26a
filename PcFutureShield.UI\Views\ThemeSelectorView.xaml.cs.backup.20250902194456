using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    /// <summary>
    /// Interaction logic for ThemeSelectorView.xaml
    /// </summary>
    public partial class ThemeSelectorView : UserControl
    {
        public ThemeSelectorView()
        {
            
            // Auto-wired DataContext by AutoValidationTool
DataContext = new ThemeSelectorViewModel();

InitializeComponent();
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Auto(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
