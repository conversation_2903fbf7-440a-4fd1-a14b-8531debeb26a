using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace PcFutureShield.Common.Interfaces
{
    /// <summary>
    /// Represents the type of scan to perform
    /// </summary>
    public enum ScanType
    {
        /// <summary>Quick scan of common infection locations</summary>
        Quick,

        /// <summary>Full system scan</summary>
        Full,

        /// <summary>Custom scan of specified locations</summary>
        Custom
    }

    /// <summary>
    /// Represents the severity level of a detected threat
    /// </summary>
    public enum ThreatSeverity
    {
        /// <summary>Informational finding</summary>
        Info,

        /// <summary>Potentially unwanted program or behavior</summary>
        Suspicious,

        /// <summary>Known malware or virus</summary>
        Malicious,

        /// <summary>Critical system threat</summary>
        Critical
    }

    /// <summary>
    /// Provides virus scanning and threat detection services
    /// </summary>
    public interface IVirusScannerService : IDisposable
    {
        /// <summary>
        /// Event raised when a threat is detected
        /// </summary>
        event EventHandler<ThreatDetectedEventArgs> ThreatDetected;

        /// <summary>
        /// Event raised when scan progress is updated
        /// </summary>
        event EventHandler<ScanProgressEventArgs> ScanProgress;

        /// <summary>
        /// Scans the specified paths for threats asynchronously
        /// </summary>
        /// <param name="paths">The file or directory paths to scan</param>
        /// <param name="scanType">The type of scan to perform</param>
        /// <param name="cancellationToken">Token to cancel the operation</param>
        /// <returns>Scan result containing detected threats</returns>
        Task<VirusScanResult> ScanAsync(IEnumerable<string> paths, ScanType scanType = ScanType.Custom, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the list of quarantined items
        /// </summary>
        Task<IReadOnlyList<QuarantineItem>> GetQuarantineAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Removes an item from quarantine
        /// </summary>
        /// <returns>True if the item was successfully removed</returns>
        Task<bool> RemoveFromQuarantineAsync(string quarantineId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Restores a file from quarantine to its original location
        /// </summary>
        Task<bool> RestoreFromQuarantineAsync(string quarantineId, string targetPath, CancellationToken cancellationToken = default);

        /// <summary>
        /// Updates the virus definition database
        /// </summary>
        Task<bool> UpdateDefinitionsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the last update time of the virus definitions
        /// </summary>
        DateTime LastDefinitionUpdate { get; }
    }

    /// <summary>
    /// Contains information about a detected threat
    /// </summary>
    public class ThreatInfo
    {
        /// <summary>Unique identifier for the threat</summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>Full path to the infected file</summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>Name of the detected threat</summary>
        public string ThreatName { get; set; } = string.Empty;

        /// <summary>Type of threat (virus, trojan, etc.)</summary>
        public string ThreatType { get; set; } = "Unknown";

        /// <summary>Severity of the threat</summary>
        public ThreatSeverity Severity { get; set; } = ThreatSeverity.Suspicious;

        /// <summary>When the threat was detected</summary>
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;

        /// <summary>Whether the file is in quarantine</summary>
        public bool IsQuarantined { get; set; }

        /// <summary>Additional details about the threat</summary>
        public Dictionary<string, string> Details { get; set; }
    }

    /// <summary>
    /// Represents an item in quarantine
    /// </summary>
    public class QuarantineItem : ThreatInfo
    {
        /// <summary>Unique identifier for the quarantine item</summary>
        public new Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>Original file name</summary>
        public string OriginalFileName { get; set; } = string.Empty;

        /// <summary>Original file path</summary>
        public string OriginalPath { get; set; } = string.Empty;

        /// <summary>Size of the quarantined file in bytes</summary>
        public long OriginalSize { get; set; }

        /// <summary>When the file was quarantined</summary>
        public DateTimeOffset Timestamp { get; set; } = DateTimeOffset.UtcNow;

        /// <summary>Reason for quarantine</summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>SHA256 hash of the original file</summary>
        public string OriginalSha256 { get; set; } = string.Empty;

        /// <summary>Whether the original file was backed up before quarantine</summary>
        public bool BackupExists { get; set; }

        /// <summary>Path to the quarantined file</summary>
        public string QuarantinePath { get; set; } = string.Empty;
    }

    /// <summary>
    /// Contains the result of a virus scan operation
    /// </summary>
    public class VirusScanResult
    {
        /// <summary>List of detected threats</summary>
        public List<ThreatInfo> Threats { get; } = new();

        /// <summary>Number of files scanned</summary>
        public int FilesScanned { get; set; }

        /// <summary>Number of files that couldn't be scanned</summary>
        public int FilesFailed { get; set; }

        /// <summary>Total size of scanned data in bytes</summary>
        public long TotalBytesScanned { get; set; }

        /// <summary>Duration of the scan</summary>
        public TimeSpan Duration { get; set; }

        /// <summary>When the scan started</summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>When the scan completed</summary>
        public DateTime? EndTime { get; set; }

        /// <summary>Whether the scan completed successfully</summary>
        public bool IsComplete => EndTime.HasValue;
    }

    /// <summary>
    /// Event arguments for threat detection events
    /// </summary>
    public class ThreatDetectedEventArgs : EventArgs
    {
        public ThreatInfo Threat { get; }
        public bool Handled { get; set; }

        public ThreatDetectedEventArgs(ThreatInfo threat)
        {
            Threat = threat ?? throw new ArgumentNullException(nameof(threat));
        }
    }

    /// <summary>
    /// Event arguments for scan progress updates
    /// </summary>
    public class ScanProgressEventArgs : EventArgs
    {
        /// <summary>Current file being scanned</summary>
        public string CurrentFile { get; }

        /// <summary>Number of files scanned so far</summary>
        public int FilesScanned { get; }

        /// <summary>Total number of files to scan (if known)</summary>
        public int TotalFiles { get; }

        /// <summary>Percentage complete (0-100)</summary>
        public int PercentComplete { get; }

        /// <summary>Number of threats found so far</summary>
        public int ThreatsFound { get; }

        public ScanProgressEventArgs(string currentFile, int filesScanned, int totalFiles, int percentComplete, int threatsFound)
        {
            CurrentFile = currentFile;
            FilesScanned = filesScanned;
            TotalFiles = totalFiles;
            PercentComplete = percentComplete;
            ThreatsFound = threatsFound;
        }
    }
}
