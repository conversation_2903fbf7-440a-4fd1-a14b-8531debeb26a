using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace PcFutureShield.Engine.Scanning
{
    /// <summary>
    /// Represents the result of a scan operation
    /// </summary>
    public class ScanResult
    {
        public ScanResult() { }

        public ScanResult(string filePath, string sha256, bool isMalicious, string reason, double entropy)
        {
            FilePath = filePath;
            Sha256 = sha256;
            IsMalicious = isMalicious;
            Reason = reason;
            Entropy = entropy;
        }

        /// <summary>
        /// Path to the scanned file
        /// </summary>
        public string FilePath { get; set; } = string.Empty;

        /// <summary>
        /// SHA256 hash of the file
        /// </summary>
        public string Sha256 { get; set; } = string.Empty;

        /// <summary>
        /// Whether the file is malicious
        /// </summary>
        public bool IsMalicious { get; set; }

        /// <summary>
        /// Reason for the scan result (e.g., signature, heuristics)
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// File entropy
        /// </summary>
        public double Entropy { get; set; }

        /// <summary>
        /// Indicates if no threats were found
        /// </summary>
        public bool IsClean { get; set; } = true;

        /// <summary>
        /// Number of files that were scanned
        /// </summary>
        public int FilesScanned { get; set; }

        /// <summary>
        /// List of detected threats, if any
        /// </summary>
        public IReadOnlyList<ThreatDetection> Threats { get; set; } = new List<ThreatDetection>();

        /// <summary>
        /// Number of threats detected
        /// </summary>
        public int ThreatsFound => Threats.Count;

        /// <summary>
        /// When the scan was started
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// When the scan was completed
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Duration of the scan
        /// </summary>
        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }
}
