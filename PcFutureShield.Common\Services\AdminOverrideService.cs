#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Security.Principal;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text.Json;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Admin override service for handling elevated permissions
    /// </summary>
    public class AdminOverrideService
    {
        private readonly string _adminDbPath;
        private readonly Dictionary<string, AdminSession> _activeSessions;
        private readonly List<AdminRequest> _pendingRequests;

        public AdminOverrideService()
        {
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var adminPath = Path.Combine(appData, "PcFutureShield", "Admin");
            Directory.CreateDirectory(adminPath);
            _adminDbPath = Path.Combine(adminPath, "admin_sessions.json");

            _activeSessions = LoadActiveSessions();
            _pendingRequests = LoadPendingRequests();
        }

        public bool IsRunningAsAdmin()
        {
            using var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        public async Task<AdminRequestResult> RequestAdminAccessAsync(string reason, TimeSpan duration)
        {
            var result = new AdminRequestResult();

            try
            {
                if (IsRunningAsAdmin())
                {
                    result.Granted = true;
                    result.SessionId = CreateAdminSession(reason, duration);
                    result.Message = "Already running as administrator";
                    return result;
                }

                var request = new AdminRequest
                {
                    Id = Guid.NewGuid().ToString(),
                    Reason = reason,
                    RequestedDuration = duration,
                    RequestedAt = DateTime.UtcNow,
                    RequestedBy = Environment.UserName,
                    Status = AdminRequestStatus.Pending
                };

                _pendingRequests.Add(request);
                await SavePendingRequestsAsync();

                result.Granted = false;
                result.RequestId = request.Id;
                result.Message = "Admin access request submitted for approval";

            }
            catch (Exception ex)
            {
                result.Granted = false;
                result.Message = $"Failed to request admin access: {ex.Message}";
            }

            return result;
        }

        public async Task<ElevationResult> ElevateProcessAsync(string processPath, string arguments = "")
        {
            // Run synchronously but yield once so callers can await; starting a process is quick and may trigger UAC
            await Task.Yield();
            var result = new ElevationResult();
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = processPath,
                    Arguments = arguments,
                    UseShellExecute = true,
                    Verb = "runas" // This triggers UAC elevation
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    result.Success = true;
                    result.ProcessId = process.Id;
                    result.Message = "Process elevated successfully";
                }
                else
                {
                    result.Success = false;
                    result.Message = "Process elevation was cancelled or failed";
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Failed to elevate process: {ex.Message}";
            }

            return result;
        }

        public async Task<AdminSessionResult> CreateTemporaryAdminSessionAsync(string reason, TimeSpan duration)
        {
            var result = new AdminSessionResult();

            try
            {
                var sessionId = CreateAdminSession(reason, duration);
                result.SessionId = sessionId;
                result.Success = true;
                result.ExpiresAt = DateTime.UtcNow.Add(duration);
                result.Message = $"Temporary admin session created for {duration.TotalMinutes} minutes";
                await SaveActiveSessionsAsync();
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Failed to create admin session: {ex.Message}";
            }

            return result;
        }

        public bool ValidateAdminSession(string sessionId)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                if (session.ExpiresAt > DateTime.UtcNow)
                {
                    return true;
                }
                else
                {
                    // Session expired, remove it
                    _activeSessions.Remove(sessionId);
                    SaveActiveSessions();
                }
            }
            return false;
        }

        public void EndAdminSession(string sessionId)
        {
            if (_activeSessions.Remove(sessionId))
            {
                SaveActiveSessions();
            }
        }

        /// <summary>
        /// Asynchronously end an admin session and persist state.
        /// Use this when callers are already asynchronous and expect real async I/O.
        /// </summary>
        public async Task EndAdminSessionAsync(string sessionId)
        {
            if (_activeSessions.Remove(sessionId))
            {
                await SaveActiveSessionsAsync().ConfigureAwait(false);
            }
        }

        public List<AdminRequest> GetPendingRequests()
        {
            return _pendingRequests.Where(r => r.Status == AdminRequestStatus.Pending).ToList();
        }

        public async Task ApproveAdminRequestAsync(string requestId, string approvedBy)
        {
            var request = _pendingRequests.FirstOrDefault(r => r.Id == requestId);
            if (request != null)
            {
                request.Status = AdminRequestStatus.Approved;
                request.ApprovedBy = approvedBy;
                request.ApprovedAt = DateTime.UtcNow;

                // Create the admin session
                var sessionId = CreateAdminSession(request.Reason, request.RequestedDuration);
                request.SessionId = sessionId;

                await SavePendingRequestsAsync();
                await SaveActiveSessionsAsync();
            }
        }

        public async Task DenyAdminRequestAsync(string requestId, string deniedBy, string reason)
        {
            var request = _pendingRequests.FirstOrDefault(r => r.Id == requestId);
            if (request != null)
            {
                request.Status = AdminRequestStatus.Denied;
                request.DeniedBy = deniedBy;
                request.DeniedReason = reason;
                request.DeniedAt = DateTime.UtcNow;

                await SavePendingRequestsAsync();
            }
        }

        public AdminSessionInfo GetSessionInfo(string sessionId)
        {
            if (_activeSessions.TryGetValue(sessionId, out var session))
            {
                return new AdminSessionInfo
                {
                    SessionId = sessionId,
                    CreatedAt = session.CreatedAt,
                    ExpiresAt = session.ExpiresAt,
                    Reason = session.Reason,
                    IsActive = session.ExpiresAt > DateTime.UtcNow,
                    TimeRemaining = session.ExpiresAt > DateTime.UtcNow ?
                        session.ExpiresAt - DateTime.UtcNow : TimeSpan.Zero
                };
            }

            return new AdminSessionInfo { IsActive = false };
        }

        public List<AdminSessionInfo> GetActiveSessions()
        {
            return _activeSessions.Values
                .Where(s => s.ExpiresAt > DateTime.UtcNow)
                .Select(s => new AdminSessionInfo
                {
                    SessionId = s.Id,
                    CreatedAt = s.CreatedAt,
                    ExpiresAt = s.ExpiresAt,
                    Reason = s.Reason,
                    IsActive = true,
                    TimeRemaining = s.ExpiresAt - DateTime.UtcNow
                })
                .ToList();
        }

        public async Task<SecurityAuditResult> PerformSecurityAuditAsync()
        {
            var result = new SecurityAuditResult();

            try
            {
                // Check for expired sessions
                var expiredSessions = _activeSessions.Values
                    .Where(s => s.ExpiresAt <= DateTime.UtcNow)
                    .ToList();

                result.ExpiredSessions = expiredSessions.Count;

                // Clean up expired sessions
                foreach (var session in expiredSessions)
                {
                    _activeSessions.Remove(session.Id);
                }

                if (expiredSessions.Count > 0)
                {
                    await SaveActiveSessionsAsync();
                }

                // Check for suspicious admin requests
                var recentRequests = _pendingRequests
                    .Where(r => r.RequestedAt > DateTime.UtcNow.AddHours(-24))
                    .ToList();

                result.RecentRequests = recentRequests.Count;

                // Check for unusual patterns
                var suspiciousPatterns = DetectSuspiciousPatterns(recentRequests);
                result.SuspiciousPatterns = suspiciousPatterns;

                result.Success = true;
                result.Message = $"Audit completed. Cleaned {expiredSessions.Count} expired sessions.";

            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Security audit failed: {ex.Message}";
            }

            return result;
        }

        private async Task SavePendingRequestsAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_pendingRequests, new JsonSerializerOptions { WriteIndented = true });
                var requestsPath = Path.Combine(Path.GetDirectoryName(_adminDbPath)!, "pending_requests.json");
                await File.WriteAllTextAsync(requestsPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving pending requests: {ex.Message}");
            }
        }

        private async Task SaveActiveSessionsAsync()
        {
            try
            {
                var sessions = _activeSessions.Values.ToList();
                var json = JsonSerializer.Serialize(sessions, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_adminDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving active sessions: {ex.Message}");
            }
        }

        private string CreateAdminSession(string reason, TimeSpan duration)
        {
            var session = new AdminSession
            {
                Id = Guid.NewGuid().ToString(),
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.Add(duration),
                Reason = reason,
                CreatedBy = Environment.UserName
            };

            _activeSessions[session.Id] = session;
            SaveActiveSessions();

            return session.Id;
        }

        private List<string> DetectSuspiciousPatterns(List<AdminRequest> requests)
        {
            var patterns = new List<string>();

            // Check for rapid successive requests from same user
            var userRequests = requests.GroupBy(r => r.RequestedBy);
            foreach (var userGroup in userRequests)
            {
                var userRequestList = userGroup.OrderBy(r => r.RequestedAt).ToList();
                for (int i = 1; i < userRequestList.Count; i++)
                {
                    var timeDiff = userRequestList[i].RequestedAt - userRequestList[i - 1].RequestedAt;
                    if (timeDiff.TotalMinutes < 5) // Requests within 5 minutes
                    {
                        patterns.Add($"Rapid admin requests from user {userGroup.Key}");
                        break;
                    }
                }
            }

            // Check for unusually long requested durations
            var longDurationRequests = requests.Where(r => r.RequestedDuration.TotalHours > 8);
            if (longDurationRequests.Any())
            {
                patterns.Add("Requests for unusually long admin durations detected");
            }

            return patterns;
        }

        private Dictionary<string, AdminSession> LoadActiveSessions()
        {
            if (!File.Exists(_adminDbPath))
                return new Dictionary<string, AdminSession>();

            try
            {
                var json = File.ReadAllText(_adminDbPath);
                var sessions = JsonSerializer.Deserialize<List<AdminSession>>(json) ?? new List<AdminSession>();
                return sessions.ToDictionary(s => s.Id);
            }
            catch
            {
                return new Dictionary<string, AdminSession>();
            }
        }

        private List<AdminRequest> LoadPendingRequests()
        {
            var requestsPath = Path.Combine(Path.GetDirectoryName(_adminDbPath)!, "pending_requests.json");
            if (!File.Exists(requestsPath))
                return new List<AdminRequest>();

            try
            {
                var json = File.ReadAllText(requestsPath);
                return JsonSerializer.Deserialize<List<AdminRequest>>(json) ?? new List<AdminRequest>();
            }
            catch
            {
                return new List<AdminRequest>();
            }
        }

        private void SaveActiveSessions()
        {
            try
            {
                var sessions = _activeSessions.Values.ToList();
                var json = JsonSerializer.Serialize(sessions, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_adminDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving active sessions: {ex.Message}");
            }
        }

        private void SavePendingRequests()
        {
            try
            {
                var json = JsonSerializer.Serialize(_pendingRequests, new JsonSerializerOptions { WriteIndented = true });
                var requestsPath = Path.Combine(Path.GetDirectoryName(_adminDbPath)!, "pending_requests.json");
                File.WriteAllText(requestsPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving pending requests: {ex.Message}");
            }
        }
    }

    // Supporting classes for Admin Override
    public class AdminRequestResult
    {
        public bool Granted { get; set; }
        public string? RequestId { get; set; }
        public string? SessionId { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class ElevationResult
    {
        public bool Success { get; set; }
        public int? ProcessId { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class AdminSessionResult
    {
        public bool Success { get; set; }
        public string? SessionId { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public string Message { get; set; } = string.Empty;
    }

    public class AdminSessionInfo
    {
        public string SessionId { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string Reason { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public TimeSpan TimeRemaining { get; set; }
    }

    public class SecurityAuditResult
    {
        public bool Success { get; set; }
        public int ExpiredSessions { get; set; }
        public int RecentRequests { get; set; }
        public List<string> SuspiciousPatterns { get; set; } = new();
        public string Message { get; set; } = string.Empty;
    }

    public class AdminSession
    {
        public string Id { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime ExpiresAt { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string CreatedBy { get; set; } = string.Empty;
    }

    public class AdminRequest
    {
        public string Id { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public TimeSpan RequestedDuration { get; set; }
        public DateTime RequestedAt { get; set; }
        public string RequestedBy { get; set; } = string.Empty;
        public AdminRequestStatus Status { get; set; }
        public string? ApprovedBy { get; set; }
        public DateTime? ApprovedAt { get; set; }
        public string? DeniedBy { get; set; }
        public string? DeniedReason { get; set; }
        public DateTime? DeniedAt { get; set; }
        public string? SessionId { get; set; }
    }

    public enum AdminRequestStatus
    {
        Pending,
        Approved,
        Denied,
        Expired
    }
}
