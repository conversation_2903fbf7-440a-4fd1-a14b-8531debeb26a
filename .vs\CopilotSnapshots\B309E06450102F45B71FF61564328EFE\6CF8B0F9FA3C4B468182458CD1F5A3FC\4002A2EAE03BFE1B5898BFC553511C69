﻿<Window x:Class="PcFutureShield.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:PcFutureShield.UI"
        xmlns:views="clr-namespace:PcFutureShield.UI.Views"
        xmlns:viewModels="clr-namespace:PcFutureShield.UI.ViewModels"
        Title="Chrome Mirror Buttons Demo" Height="800" Width="1000"
        Background="{DynamicResource BackgroundBrush}">

    <Window.Resources>
        <!-- DataTemplates to map ViewModels to Views -->
        <DataTemplate DataType="{x:Type viewModels:DashboardViewModel}">
            <views:DashboardView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:VirusScanViewModel}">
            <views:VirusScanView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:EnhancedScanViewModel}">
            <views:EnhancedScanView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:RealtimeProtectionViewModel}">
            <views:RealtimeProtectionView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:ParentalControlViewModel}">
            <views:ParentalControlView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:QuarantineViewModel}">
            <views:QuarantineView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:AIDashboardViewModel}">
            <views:AIDashboardView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:GamingProtectionViewModel}">
            <views:GamingProtectionView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:AdminOverrideViewModel}">
            <views:AdminOverrideView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:SmartRepairViewModel}">
            <views:SmartRepairView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:SettingsViewModel}">
            <views:SettingsView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:LicenseManagerViewModel}">
            <views:LicenseManagerView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:UpdatesViewModel}">
            <views:UpdatesView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:PcOptimizationViewModel}">
            <views:PcOptimizationView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:BrowserExtensionViewModel}">
            <views:BrowserExtensionView/>
        </DataTemplate>
        <DataTemplate DataType="{x:Type viewModels:ThemeSelectorViewModel}">
            <views:ThemeSelectorView/>
        </DataTemplate>
    </Window.Resources>

    <Grid MinWidth="600" MinHeight="400">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" MinWidth="180"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <!-- Sidebar Navigation -->
        <Border x:Name="SidebarBorder" Grid.Row="0" Grid.Column="0" Background="{DynamicResource SidebarBackgroundBrush}" CornerRadius="18" BorderThickness="2" BorderBrush="{DynamicResource BorderBrush}" MinWidth="180" MaxWidth="320" Width="220" Margin="0,0,10,0" Padding="0">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="Navigation" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" HorizontalAlignment="Center" Margin="0,20,0,20"/>
                    <!-- Navigation Buttons -->
                    <Button Content="Dashboard" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding DashboardCommand}"/>
                    <Button Content="Enhanced Scanner" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding EnhancedScannerCommand}"/>
                    <Button Content="Real-time Protection" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding RealTimeProtectionCommand}"/>
                    <Button Content="Quarantine" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding QuarantineCommand}"/>
                    <Button Content="Smart Repair" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding SmartRepairCommand}"/>
                    <Button Content="Parental Control" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding ParentalControlCommand}"/>
                    <Button Content="AI Dashboard" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding AIDashboardCommand}"/>
                    <Button Content="Gaming Protection" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding GamingProtectionCommand}"/>
                    <Button Content="Admin Override" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding AdminOverrideCommand}"/>
                    <Button Content="Settings" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding SettingsCommand}"/>
                    <Button Content="License Manager" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding LicenseManagerCommand}"/>
                    <Button Content="Updates" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding UpdatesCommand}"/>
                    <Button Content="PC Optimization" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding PcOptimizationCommand}"/>
                    <Button Content="Browser Extension" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding BrowserExtensionCommand}"/>
                    <Button Content="Theme Selector" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding ThemeSelectorCommand}"/>
                    <Separator Margin="10,15,10,15" Background="{DynamicResource BorderBrush}"/>
                    <Button Content="View Logs" Style="{DynamicResource MirrorButtonStyle}" Click="ViewLogsButton_Click"/>
                </StackPanel>
            </ScrollViewer>
        </Border>
    <!-- Main Content Area -->
    <ContentControl Grid.Row="0" Grid.Column="1" Content="{Binding CurrentView}"/>
    </Grid>
</Window>
