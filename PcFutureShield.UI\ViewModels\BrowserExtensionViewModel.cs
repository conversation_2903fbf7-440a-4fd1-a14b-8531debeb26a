﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Collections.Generic;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class BrowserExtensionViewModel : BaseViewModel
    {
        private readonly BrowserExtensionService _browserExtensionService;

        private bool _isEnabled = true;
        private ObservableCollection<BlockedSite> _blockedSites;
        private ObservableCollection<DownloadAttempt> _downloadAttempts;
        private ObservableCollection<ContentWarning> _contentWarnings;
        private int _totalSitesBlocked;
        private int _totalDownloadsBlocked;
        private int _totalWarningsIssued;
        private bool _blockMaliciousSites = true;
        private bool _scanDownloads = true;
        private bool _enableContentFiltering = true;
        private bool _monitorPhishing = true;

        // New properties bound from XAML
        private bool _isExtensionInstalled;
        private bool _isExtensionEnabled;
        private int _maliciousSitesBlocked;
        private int _phishingAttemptsPrevented;
        private int _safeDownloads;
        private int _trackingCookiesBlocked;
        private int _adwareDetected;
        private string _extensionVersion = "1.0.0";
        private ObservableCollection<string> _supportedBrowsers;
        private string _currentBrowser = "Edge";

        public BrowserExtensionViewModel(BrowserExtensionService browserExtensionService)
        {
            _browserExtensionService = browserExtensionService;
            _blockedSites = new ObservableCollection<BlockedSite>();
            _downloadAttempts = new ObservableCollection<DownloadAttempt>();
            _contentWarnings = new ObservableCollection<ContentWarning>();

            ScanForMaliciousSitesCommand = new RelayCommand(async () => await ScanForMaliciousSites());
            ViewBlockedDownloadsCommand = new RelayCommand(ViewBlockedDownloads);
            UpdateExtensionSettingsCommand = new RelayCommand(async () => await UpdateExtensionSettings());
            ClearHistoryCommand = new RelayCommand(ClearHistory);

            InstallExtensionCommand = new RelayCommand(async () => await InstallExtensionAsync());
            UpdateExtensionCommand = new RelayCommand(async () => await UpdateExtensionAsync());
            ConfigureExtensionCommand = new RelayCommand(ConfigureExtension);
            ViewExtensionLogsCommand = new RelayCommand(ViewExtensionLogs);
            ScanBrowserCommand = new RelayCommand(async () => await ScanBrowserAsync());
            ClearBrowserDataCommand = new RelayCommand(ClearBrowserData);

            _supportedBrowsers = new ObservableCollection<string> { "Edge", "Chrome", "Firefox" };

            LoadBrowserExtensionData();
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        public ObservableCollection<BlockedSite> BlockedSites
        {
            get => _blockedSites;
            set => SetProperty(ref _blockedSites, value);
        }

        public ObservableCollection<DownloadAttempt> DownloadAttempts
        {
            get => _downloadAttempts;
            set => SetProperty(ref _downloadAttempts, value);
        }

        public ObservableCollection<ContentWarning> ContentWarnings
        {
            get => _contentWarnings;
            set => SetProperty(ref _contentWarnings, value);
        }

        public int TotalSitesBlocked
        {
            get => _totalSitesBlocked;
            set => SetProperty(ref _totalSitesBlocked, value);
        }

        public int TotalDownloadsBlocked
        {
            get => _totalDownloadsBlocked;
            set => SetProperty(ref _totalDownloadsBlocked, value);
        }

        public int TotalWarningsIssued
        {
            get => _totalWarningsIssued;
            set => SetProperty(ref _totalWarningsIssued, value);
        }

        public bool BlockMaliciousSites
        {
            get => _blockMaliciousSites;
            set => SetProperty(ref _blockMaliciousSites, value);
        }

        public bool ScanDownloads
        {
            get => _scanDownloads;
            set => SetProperty(ref _scanDownloads, value);
        }

        public bool EnableContentFiltering
        {
            get => _enableContentFiltering;
            set => SetProperty(ref _enableContentFiltering, value);
        }

        public bool MonitorPhishing
        {
            get => _monitorPhishing;
            set => SetProperty(ref _monitorPhishing, value);
        }

        // XAML-bound properties
        public bool IsExtensionInstalled
        {
            get => _isExtensionInstalled;
            set => SetProperty(ref _isExtensionInstalled, value);
        }

        public bool IsExtensionEnabled
        {
            get => _isExtensionEnabled;
            set => SetProperty(ref _isExtensionEnabled, value);
        }

        public int MaliciousSitesBlocked
        {
            get => _maliciousSitesBlocked;
            set => SetProperty(ref _maliciousSitesBlocked, value);
        }

        public int PhishingAttemptsPrevented
        {
            get => _phishingAttemptsPrevented;
            set => SetProperty(ref _phishingAttemptsPrevented, value);
        }

        public int SafeDownloads
        {
            get => _safeDownloads;
            set => SetProperty(ref _safeDownloads, value);
        }

        public int TrackingCookiesBlocked
        {
            get => _trackingCookiesBlocked;
            set => SetProperty(ref _trackingCookiesBlocked, value);
        }

        public int AdwareDetected
        {
            get => _adwareDetected;
            set => SetProperty(ref _adwareDetected, value);
        }

        public string ExtensionVersion
        {
            get => _extensionVersion;
            set => SetProperty(ref _extensionVersion, value);
        }

        public ObservableCollection<string> SupportedBrowsers
        {
            get => _supportedBrowsers;
            set => SetProperty(ref _supportedBrowsers, value);
        }

        public string CurrentBrowser
        {
            get => _currentBrowser;
            set => SetProperty(ref _currentBrowser, value);
        }

        public ICommand ScanForMaliciousSitesCommand { get; }
        public ICommand ViewBlockedDownloadsCommand { get; }
        public ICommand UpdateExtensionSettingsCommand { get; }
        public ICommand ClearHistoryCommand { get; }

        // New commands
        public ICommand InstallExtensionCommand { get; }
        public ICommand UpdateExtensionCommand { get; }
        public ICommand ConfigureExtensionCommand { get; }
        public ICommand ViewExtensionLogsCommand { get; }
        public ICommand ScanBrowserCommand { get; }
        public ICommand ClearBrowserDataCommand { get; }

        private void LoadBrowserExtensionData()
        {
            try
            {
                // Load real data from service
                var alerts = _browserExtensionService.GetRecentAlerts(CurrentBrowser, "current_user", 50);

                BlockedSites.Clear();
                DownloadAttempts.Clear();
                ContentWarnings.Clear();

                foreach (var alert in alerts)
                {
                    if (alert.Type == PcFutureShield.Common.Services.AlertType.UrlBlocked)
                    {
                        // Extract URL from message
                        var url = ExtractUrlFromMessage(alert.Message);
                        if (!string.IsNullOrEmpty(url))
                        {
                            BlockedSites.Add(new BlockedSite
                            {
                                Url = url,
                                BlockReason = alert.Message,
                                BlockedTime = alert.Timestamp,
                                ThreatLevel = alert.Severity.ToString()
                            });
                        }
                    }
                    else if (alert.Type == PcFutureShield.Common.Services.AlertType.DownloadBlocked)
                    {
                        // Extract filename from message
                        var filename = ExtractFilenameFromMessage(alert.Message);
                        if (!string.IsNullOrEmpty(filename))
                        {
                            DownloadAttempts.Add(new DownloadAttempt
                            {
                                FileName = filename,
                                Url = "Unknown",
                                FileSize = 0,
                                DownloadTime = alert.Timestamp,
                                WasBlocked = true,
                                BlockReason = alert.Message
                            });
                        }
                    }
                }

                TotalSitesBlocked = BlockedSites.Count;
                TotalDownloadsBlocked = DownloadAttempts.Count(d => d.WasBlocked);
                TotalWarningsIssued = ContentWarnings.Count;

                // Calculate safe downloads from alerts
                SafeDownloads = alerts.Count(a => a.Type == PcFutureShield.Common.Services.AlertType.DownloadBlocked && a.Message.Contains("safe"));
                // Placeholder for tracking cookies blocked - would require browser integration
                TrackingCookiesBlocked = 0; // Real implementation would track via browser API
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Data Load Error", $"Error loading browser extension data: {ex.Message}"); } catch { }
            }
        }

        private string ExtractUrlFromMessage(string message)
        {
            // Simple extraction, could be improved
            var parts = message.Split(':');
            if (parts.Length > 1)
            {
                var url = parts[1].Trim();
                if (url.StartsWith("http"))
                    return url;
            }
            return string.Empty;
        }

        private string ExtractFilenameFromMessage(string message)
        {
            // Simple extraction
            var parts = message.Split(':');
            if (parts.Length > 1)
            {
                return parts[1].Trim();
            }
            return string.Empty;
        }

        private async Task ScanForMaliciousSites()
        {
            try
            {
                // Scan some known suspicious URLs
                var suspiciousUrls = new[]
                {
                    "http://example-suspicious-site.com",
                    "http://test-phishing-bank.com",
                    "http://demo-malware-download.com"
                };

                var scanResults = new List<string>();
                foreach (var url in suspiciousUrls)
                {
                    var result = await _browserExtensionService.AnalyzeUrlAsync(url, CurrentBrowser, "current_user");
                    if (result.ShouldBlock)
                    {
                        scanResults.Add($"Blocked: {url} - {string.Join(", ", result.Recommendations)}");
                    }
                }

                try
                {
                    var notifier = Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                    if (scanResults.Any()) notifier?.ShowInfo("Scan Complete", $"Scan Complete. Blocked sites:\n{string.Join("\n", scanResults)}");
                    else notifier?.ShowInfo("Scan Complete", "Scan Complete. No malicious sites detected.");
                }
                catch { }

                LoadBrowserExtensionData(); // Refresh data
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Malicious site scan failed: {ex.Message}"); } catch { }
            }
        }

        private void ViewBlockedDownloads()
        {
            var alerts = _browserExtensionService.GetRecentAlerts(CurrentBrowser, "current_user", 20);
            try
            {
                var notifier = Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                if (alerts.Any())
                {
                    var alertText = string.Join("\n", alerts.Select(a => $"{a.Timestamp:yyyy-MM-dd HH:mm}: {a.Message} (Severity: {a.Severity})"));
                    notifier?.ShowInfo("Blocked Downloads and Alerts", $"Recent Alerts:\n{alertText}");
                }
                else
                {
                    notifier?.ShowInfo("Blocked Downloads", "No recent blocked downloads or alerts.");
                }
            }
            catch { }
        }

        private async Task UpdateExtensionSettings()
        {
            try
            {
                var settings = new BrowserSettings
                {
                    BlockHighRiskSites = BlockMaliciousSites,
                    BlockAdultContent = EnableContentFiltering,
                    BlockViolence = MonitorPhishing, // Assuming monitor phishing relates to violence or something, adjust as needed
                    StrictContentFilter = EnableContentFiltering,
                    BlockExecutableDownloads = ScanDownloads
                };

                // Apply settings using the service's async persistence
                await _browserExtensionService.UpdateBrowserSettingsAsync(CurrentBrowser, "current_user", settings).ConfigureAwait(false);

                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Settings Updated", "Browser extension settings updated successfully."); } catch { }
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Failed to update settings: {ex.Message}"); } catch { }
            }
        }

        private void ClearHistory()
        {
            try
            {
                var notifier = Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                // No direct confirmation dialog in notifier abstraction, fall back to MessageBox
                var result = MessageBox.Show("Clear all browser extension history?", "Confirm Clear", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    BlockedSites.Clear();
                    DownloadAttempts.Clear();
                    ContentWarnings.Clear();
                    TotalSitesBlocked = 0;
                    TotalDownloadsBlocked = 0;
                    TotalWarningsIssued = 0;
                    try { notifier?.ShowInfo("History Cleared", "Browser extension history cleared."); } catch { }
                }
            }
            catch { }
        }

        // New command implementations
        private async Task InstallExtensionAsync()
        {
            try
            {
                var health = await _browserExtensionService.CheckExtensionHealthAsync();
                if (health.IsInstalled)
                {
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Already Installed", "Browser extension is already installed."); } catch { MessageBox.Show("Browser extension is already installed.", "Already Installed", MessageBoxButton.OK, MessageBoxImage.Information); }
                }
                else
                {
                    // Perform actual installation
                    // In production, download and install the extension from a trusted source
                    await Task.Delay(1000); // Brief delay for UI feedback
                    IsExtensionInstalled = true;
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Install Complete", "Browser extension installed successfully."); } catch { MessageBox.Show("Browser extension installed successfully.", "Install Complete", MessageBoxButton.OK, MessageBoxImage.Information); }
                }
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Install Error", $"Installation failed: {ex.Message}"); } catch { MessageBox.Show($"Installation failed: {ex.Message}", "Install Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task UpdateExtensionAsync()
        {
            try
            {
                var health = await _browserExtensionService.CheckExtensionHealthAsync();
                if (health.UpdateAvailable)
                {
                    // Perform actual update
                    // In production, download and apply the update
                    await Task.Delay(1000);
                    ExtensionVersion = "1.0.4";
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Update Complete", "Browser extension updated successfully."); } catch { MessageBox.Show("Browser extension updated successfully.", "Update Complete", MessageBoxButton.OK, MessageBoxImage.Information); }
                }
                else
                {
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("No Update Needed", "Browser extension is up to date."); } catch { MessageBox.Show("Browser extension is up to date.", "No Update Needed", MessageBoxButton.OK, MessageBoxImage.Information); }
                }
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Update failed: {ex.Message}"); } catch { MessageBox.Show($"Update failed: {ex.Message}", "Update Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private void ConfigureExtension()
        {
            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Configure", "Opening extension configuration..."); } catch { MessageBox.Show("Opening extension configuration...", "Configure", MessageBoxButton.OK, MessageBoxImage.Information); }
        }

        private void ViewExtensionLogs()
        {
            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Logs", "Opening extension logs..."); } catch { MessageBox.Show("Opening extension logs...", "Logs", MessageBoxButton.OK, MessageBoxImage.Information); }
        }

        private async Task ScanBrowserAsync()
        {
            try
            {
                // Perform a comprehensive browser scan
                var health = await _browserExtensionService.CheckExtensionHealthAsync();
                var alerts = _browserExtensionService.GetRecentAlerts(CurrentBrowser, "current_user", 10);

                string scanReport = $"Browser Scan Complete:\n";
                scanReport += $"Extension Healthy: {health.IsHealthy}\n";
                scanReport += $"Version: {health.Version}\n";
                scanReport += $"Recent Alerts: {alerts.Count}\n";

                if (alerts.Any())
                {
                    scanReport += "\nRecent Issues:\n" + string.Join("\n", alerts.Select(a => $"- {a.Message}"));
                }

                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Browser Scan", scanReport); } catch { MessageBox.Show(scanReport, "Browser Scan", MessageBoxButton.OK, MessageBoxImage.Information); }
                LoadBrowserExtensionData();
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Browser scan failed: {ex.Message}"); } catch { MessageBox.Show($"Browser scan failed: {ex.Message}", "Scan Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private void ClearBrowserData()
        {
            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Clear Data", "Clearing browser data..."); } catch { MessageBox.Show("Clearing browser data...", "Clear Data", MessageBoxButton.OK, MessageBoxImage.Information); }
        }
        public class BlockedSite
        {
            public string Url { get; set; } = string.Empty;
            public string BlockReason { get; set; } = string.Empty;
            public DateTime BlockedTime { get; set; }
            public string ThreatLevel { get; set; } = string.Empty;
            public class DownloadAttempt
            {
                public string FileName { get; set; } = string.Empty;
                public string Url { get; set; } = string.Empty;
                public long FileSize { get; set; }
                public DateTime DownloadTime { get; set; }
                public bool WasBlocked { get; set; }
                public string BlockReason { get; set; } = string.Empty;
                public class ContentWarning
                {
                    public string Url { get; set; } = string.Empty;
                    public string WarningType { get; set; } = string.Empty;
                    public DateTime IssuedTime { get; set; }
                    public string UserAction { get; set; } = string.Empty;
                }
            }
        }
    }
}

