using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class UpdatesView : UserControl
    {
        public UpdatesView() { 
            // Auto-wired DataContext by AutoValidationTool
DataContext = new UpdatesViewModel();

}
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Auto(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
