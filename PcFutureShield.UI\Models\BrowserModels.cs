using System;
using System.ComponentModel;

namespace PcFutureShield.UI.ViewModels
{
    public class DownloadAttempt : INotifyPropertyChanged
    {
        private string _fileName = string.Empty;
        private string _url = string.Empty;
        private DateTime _attemptTime;
        private string _status = string.Empty;
        private string _riskLevel = string.Empty;
        private long _fileSize;

        public string FileName
        {
            get => _fileName;
            set
            {
                if (_fileName != value)
                {
                    _fileName = value;
                    OnPropertyChanged(nameof(FileName));
                }
            }
        }

        public string Url
        {
            get => _url;
            set
            {
                if (_url != value)
                {
                    _url = value;
                    OnPropertyChanged(nameof(Url));
                }
            }
        }

        public DateTime AttemptTime
        {
            get => _attemptTime;
            set
            {
                if (_attemptTime != value)
                {
                    _attemptTime = value;
                    OnPropertyChanged(nameof(AttemptTime));
                }
            }
        }

        public DateTime DownloadTime
        {
            get => _attemptTime;
            set
            {
                if (_attemptTime != value)
                {
                    _attemptTime = value;
                    OnPropertyChanged(nameof(DownloadTime));
                }
            }
        }

        private bool _wasBlocked;
        public bool WasBlocked
        {
            get => _wasBlocked;
            set
            {
                if (_wasBlocked != value)
                {
                    _wasBlocked = value;
                    OnPropertyChanged(nameof(WasBlocked));
                }
            }
        }

        private string _blockReason = string.Empty;
        public string BlockReason
        {
            get => _blockReason;
            set
            {
                if (_blockReason != value)
                {
                    _blockReason = value;
                    OnPropertyChanged(nameof(BlockReason));
                }
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        public string RiskLevel
        {
            get => _riskLevel;
            set
            {
                if (_riskLevel != value)
                {
                    _riskLevel = value;
                    OnPropertyChanged(nameof(RiskLevel));
                }
            }
        }

        public long FileSize
        {
            get => _fileSize;
            set
            {
                if (_fileSize != value)
                {
                    _fileSize = value;
                    OnPropertyChanged(nameof(FileSize));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class ContentWarning : INotifyPropertyChanged
    {
        private string _warningId = string.Empty;
        private string _url = string.Empty;
        private string _warningType = string.Empty;
        private DateTime _issuedTime;
        private string _userAction = string.Empty;
        private string _severity = string.Empty;

        public string WarningId
        {
            get => _warningId;
            set
            {
                if (_warningId != value)
                {
                    _warningId = value;
                    OnPropertyChanged(nameof(WarningId));
                }
            }
        }

        public string Url
        {
            get => _url;
            set
            {
                if (_url != value)
                {
                    _url = value;
                    OnPropertyChanged(nameof(Url));
                }
            }
        }

        public string WarningType
        {
            get => _warningType;
            set
            {
                if (_warningType != value)
                {
                    _warningType = value;
                    OnPropertyChanged(nameof(WarningType));
                }
            }
        }

        public DateTime IssuedTime
        {
            get => _issuedTime;
            set
            {
                if (_issuedTime != value)
                {
                    _issuedTime = value;
                    OnPropertyChanged(nameof(IssuedTime));
                }
            }
        }

        public string UserAction
        {
            get => _userAction;
            set
            {
                if (_userAction != value)
                {
                    _userAction = value;
                    OnPropertyChanged(nameof(UserAction));
                }
            }
        }

        public string Severity
        {
            get => _severity;
            set
            {
                if (_severity != value)
                {
                    _severity = value;
                    OnPropertyChanged(nameof(Severity));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}
