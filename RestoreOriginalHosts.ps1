# RESTORE ORIGINAL HOSTS FILE - Run as Administrator
# This will completely restore your Windows hosts file to its original state

Write-Host "RESTORING ORIGINAL HOSTS FILE" -ForegroundColor Red
Write-Host "=============================" -ForegroundColor Red

$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

# Create backup of current (corrupted) hosts file
Write-Host "Creating backup of current hosts file..." -ForegroundColor Yellow
$backupPath = "$hostsPath.corrupted.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Copy-Item $hostsPath $backupPath -Force
Write-Host "Backup created: $backupPath" -ForegroundColor Green

# Create the original Windows hosts file content
Write-Host "Restoring original hosts file content..." -ForegroundColor Yellow

$originalHostsContent = @"
# Copyright (c) 1993-2009 Microsoft Corp.
#
# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
#
# This file contains the mappings of IP addresses to host names. Each
# entry should be kept on an individual line. The IP address should
# be placed in the first column followed by the corresponding host name.
# The IP address and the host name should be separated by at least one
# space.
#
# Additionally, comments (such as these) may be inserted on individual
# lines or following the machine name denoted by a '#' symbol.
#
# For example:
#
#      ************     rhino.acme.com          # source server
#       ***********     x.acme.com              # x client host

# localhost name resolution is handled within DNS itself.
#	127.0.0.1       localhost
#	::1             localhost
"@

# Write the original content to the hosts file
try {
    $originalHostsContent | Out-File -FilePath $hostsPath -Encoding ASCII -Force
    Write-Host "Original hosts file restored successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error restoring hosts file: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may need to run this script as Administrator" -ForegroundColor Yellow
    exit 1
}

# Verify the restoration
Write-Host "Verifying hosts file..." -ForegroundColor Yellow
$currentContent = Get-Content $hostsPath
$pcFutureShieldEntries = $currentContent | Where-Object { $_ -match "PcFutureShield" }

if ($pcFutureShieldEntries.Count -eq 0) {
    Write-Host "✓ SUCCESS: No PcFutureShield entries found in hosts file" -ForegroundColor Green
} else {
    Write-Host "✗ WARNING: Still found $($pcFutureShieldEntries.Count) PcFutureShield entries" -ForegroundColor Red
}

# Show current hosts file content
Write-Host ""
Write-Host "Current hosts file content:" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Get-Content $hostsPath | ForEach-Object { Write-Host $_ -ForegroundColor White }

# Flush DNS cache
Write-Host ""
Write-Host "Flushing DNS cache..." -ForegroundColor Yellow
ipconfig /flushdns | Out-Null
Write-Host "DNS cache flushed!" -ForegroundColor Green

# Test connectivity
Write-Host ""
Write-Host "Testing internet connectivity..." -ForegroundColor Yellow
$testSites = @("google.com", "microsoft.com", "github.com", "facebook.com")

foreach ($site in $testSites) {
    try {
        $result = Test-NetConnection -ComputerName $site -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($result) {
            Write-Host "✓ $site - ACCESSIBLE" -ForegroundColor Green
        } else {
            Write-Host "✗ $site - NOT ACCESSIBLE" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ $site - ERROR TESTING" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "HOSTS FILE RESTORATION COMPLETE!" -ForegroundColor Green
Write-Host ""
Write-Host "What was done:" -ForegroundColor Cyan
Write-Host "• Created backup of corrupted hosts file: $backupPath" -ForegroundColor White
Write-Host "• Restored original Windows hosts file content" -ForegroundColor White
Write-Host "• Removed ALL PcFutureShield entries" -ForegroundColor White
Write-Host "• Flushed DNS cache" -ForegroundColor White
Write-Host ""
Write-Host "Your internet access should now be completely restored!" -ForegroundColor Yellow
Write-Host ""
Write-Host "If you still have issues:" -ForegroundColor Yellow
Write-Host "1. Close and restart your browser completely" -ForegroundColor White
Write-Host "2. Try incognito/private browsing mode" -ForegroundColor White
Write-Host "3. Restart your computer if problems persist" -ForegroundColor White

Read-Host "Press Enter to exit"
