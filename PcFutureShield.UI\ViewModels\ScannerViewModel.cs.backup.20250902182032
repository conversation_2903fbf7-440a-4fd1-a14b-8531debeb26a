using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Linq;
using PcFutureShield.Common.Utilities;
using PcFutureShield.Engine.Scanning;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public sealed class ScannerViewModel : INotifyPropertyChanged
    {
        private readonly IScanManager _scanManager;
        private string _targetFolder = Environment.GetFolderPath(Environment.SpecialFolder.UserProfile);
        private bool _isScanning;
        private double _progress;

        public ObservableCollection<PcFutureShield.Engine.Scanning.ScanResult> Results { get; } = new();
        public string TargetFolder { get => _targetFolder; set { _targetFolder = value; OnPropertyChanged(); } }
        public bool IsScanning { get => _isScanning; private set { _isScanning = value; OnPropertyChanged(); CommandManager.InvalidateRequerySuggested(); } }
        public double Progress { get => _progress; private set { _progress = value; OnPropertyChanged(); } }

        public RelayCommand StartScanCommand { get; }
        public RelayCommand QuarantineSelectedCommand { get; }

        private PcFutureShield.Engine.Scanning.ScanResult? _selected;
        public PcFutureShield.Engine.Scanning.ScanResult? Selected { get => _selected; set { _selected = value; OnPropertyChanged(); } }

        public ScannerViewModel()
        {
            try
            {
                _scanManager = ServiceLocator.Get<IScanManager>();
                StartScanCommand = new RelayCommand(() => StartScanAsync().ConfigureAwait(false), () => !IsScanning && !string.IsNullOrWhiteSpace(TargetFolder));
                QuarantineSelectedCommand = new RelayCommand(QuarantineSelected, () => Selected != null && Selected.IsMalicious);
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Initialization Error", $"ScannerViewModel initialization failed: {ex.Message}\n{ex.StackTrace}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show($"ScannerViewModel initialization failed: {ex.Message}\n{ex.StackTrace}", "PcFutureShield", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error)); }
                throw;
            }
        }

        private async Task StartScanAsync()
        {
            IsScanning = true;
            Results.Clear();
            Progress = 0;

            try
            {
                // Use the async ScanFolderAsync so the Scanner handles offloading appropriately
                var manager = (PcFutureShield.UI.Scan.ScanManager)_scanManager;
                var list = await manager.ScanFolderAsync(TargetFolder).ConfigureAwait(false);

                // Marshal results back to UI thread
                await App.Current.Dispatcher.InvokeAsync(() =>
                {
                    int processed = 0;
                    foreach (var result in list)
                    {
                        Results.Add(result);
                        processed++;
                        var pct = Math.Min(processed * 2.0, 100.0);
                        Progress = pct;
                    }
                });

                Progress = 100;
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", $"Scan completed. Found {Results.Count(r => r.IsMalicious)} threats."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show($"Scan completed. Found {Results.Count(r => r.IsMalicious)} threats.", "Scan Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Scan failed: {ex.Message}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show($"Scan failed: {ex.Message}", "Scan Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error)); }
            }
            finally
            {
                IsScanning = false;
            }
        }

        private void QuarantineSelected()
        {
            if (Selected == null || !Selected.IsMalicious) return;

            try
            {
                // Get quarantine manager from ServiceLocator
                var quarantineManager = (PcFutureShield.Engine.Quarantine.QuarantineManager)ServiceLocator.Get<PcFutureShield.Common.Interfaces.IQuarantineManager>();

                // Quarantine the file using the correct method
                var quarantineItem = quarantineManager.QuarantineFile(
                    Selected.FilePath,
                    Selected.Sha256,
                    Selected.Reason);

                // Remove from results
                Results.Remove(Selected);
                Selected = null;

                System.Windows.MessageBox.Show("File quarantined successfully.", "Quarantine Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"Failed to quarantine file: {ex.Message}", "Quarantine Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        private void OnPropertyChanged([CallerMemberName] string? name = null) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}
