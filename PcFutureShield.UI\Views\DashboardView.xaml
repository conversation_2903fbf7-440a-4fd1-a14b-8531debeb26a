<UserControl x:Class="PcFutureShield.UI.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d">
    <Grid Background="{DynamicResource BackgroundBrush}">
    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="PcFutureShield Security Dashboard" FontSize="36" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,24"/>
            <UniformGrid Columns="3" Rows="2" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,0,24">
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="Active Threats" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBlock Text="{Binding ActiveThreats}" FontSize="28" FontWeight="Bold" Foreground="{DynamicResource ErrorBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="Files Scanned" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBlock Text="{Binding ScannedFiles}" FontSize="28" FontWeight="Bold" Foreground="{DynamicResource PrimaryColorBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                    <StackPanel>
                        <TextBlock Text="Quarantined" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBlock Text="{Binding QuarantinedItems}" FontSize="28" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="System Health" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBlock Text="{Binding SystemHealthStatus}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="Last Scan" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBlock Text="{Binding LastScanTime}" FontSize="18" FontWeight="Bold"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="Protection Status" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        <TextBlock Text="{Binding IsRealTimeProtectionEnabled, StringFormat=Real-time: {0}}" FontSize="18" FontWeight="Bold"/>
                    </StackPanel>
                </Border>
            </UniformGrid>
            <Button Content="Run Full System Scan" Style="{DynamicResource GlassButtonStyle}" Command="{Binding FullScanCommand}" Width="260" Height="54" FontSize="18" Margin="0,24,0,0" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}"/>
        </StackPanel>
    </Grid>
</UserControl>
