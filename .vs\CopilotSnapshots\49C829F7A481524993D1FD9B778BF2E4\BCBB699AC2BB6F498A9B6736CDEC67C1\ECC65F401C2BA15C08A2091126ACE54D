﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- Glass Toggle Button Style -->
    <Style x:Key="GlassToggleButtonStyle" TargetType="ToggleButton">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Grid>
                        <!-- Cyber Glass Background -->
                        <Border x:Name="GlassBorder" CornerRadius="12" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="0"/>
                                    <GradientStop Color="{DynamicResource SecondaryColorBrush}" Offset="0.1"/>
                                    <GradientStop Color="{DynamicResource SecondaryColorBrush}" Offset="0.5"/>
                                    <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="0.9"/>
                                    <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Holographic Top Highlight -->
                        <Border CornerRadius="12,12,0,0" Height="18" VerticalAlignment="Top" Margin="1,1,1,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="0"/>
                                    <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Inner Glass Layer -->
                        <Border CornerRadius="11" Margin="1" Background="{DynamicResource CardBackgroundBrush}" Opacity="0.8"/>

                        <!-- Cyber Grid Overlay -->
                        <Border CornerRadius="11" Margin="1" Opacity="0.2">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,10,10" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="{DynamicResource PrimaryColorBrush}">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <LineGeometry StartPoint="0,0" EndPoint="10,0"/>
                                                    <LineGeometry StartPoint="0,0" EndPoint="0,10"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content with Glow -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="{DynamicResource AccentColorBrush}" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.8"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>

                        <!-- Outer Glow -->
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="{DynamicResource PrimaryColorBrush}" Direction="270" ShadowDepth="0" BlurRadius="15" Opacity="0.6"/>
                            </Grid.Effect>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsChecked" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background" Value="{DynamicResource PrimaryColorBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource AccentColorBrush}" Direction="270" ShadowDepth="0" BlurRadius="25" Opacity="0.9"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background" Value="{DynamicResource SecondaryColorBrush}"/>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource AccentColorBrush}" Direction="270" ShadowDepth="0" BlurRadius="20" Opacity="0.8"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
    </Style>

    <!-- Glass Button Style -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Glass Background -->
                        <Border x:Name="GlassBorder" CornerRadius="12" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="0"/>
                                    <GradientStop Color="{DynamicResource SecondaryColorBrush}" Offset="0.1"/>
                                    <GradientStop Color="{DynamicResource SecondaryColorBrush}" Offset="0.5"/>
                                    <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="0.9"/>
                                    <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Holographic Top Highlight -->
                        <Border CornerRadius="12,12,0,0" Height="18" VerticalAlignment="Top" Margin="1,1,1,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="0"/>
                                    <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Inner Glass Layer -->
                        <Border CornerRadius="11" Margin="1" Background="{DynamicResource CardBackgroundBrush}" Opacity="0.8"/>

                        <!-- Cyber Grid Overlay -->
                        <Border CornerRadius="11" Margin="1" Opacity="0.2">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,10,10" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="{DynamicResource PrimaryColorBrush}">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <LineGeometry StartPoint="0,0" EndPoint="10,0"/>
                                                    <LineGeometry StartPoint="0,0" EndPoint="0,10"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content with Glow -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="{DynamicResource AccentColorBrush}" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.8"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>

                        <!-- Outer Glow -->
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="{DynamicResource PrimaryColorBrush}" Direction="270" ShadowDepth="0" BlurRadius="15" Opacity="0.6"/>
                            </Grid.Effect>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="0"/>
                                        <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="0.1"/>
                                        <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="0.5"/>
                                        <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="0.9"/>
                                        <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="{DynamicResource AccentColorBrush}" Direction="270" ShadowDepth="0" BlurRadius="25" Opacity="0.9"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="{DynamicResource SecondaryColorBrush}" Offset="0"/>
                                        <GradientStop Color="{DynamicResource SecondaryColorBrush}" Offset="0.1"/>
                                        <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="0.5"/>
                                        <GradientStop Color="{DynamicResource PrimaryColorBrush}" Offset="0.9"/>
                                        <GradientStop Color="{DynamicResource AccentColorBrush}" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
    </Style>
</ResourceDictionary>
