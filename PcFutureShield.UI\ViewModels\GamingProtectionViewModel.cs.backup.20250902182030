using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class GamingProtectionViewModel : BaseViewModel
    {
        private readonly GamingProtectionService _gamingProtectionService;

        private bool _isEnabled = true;
        private ObservableCollection<CasinoDetection> _detectedCasinos;
        private ObservableCollection<RigDetection> _detectedRigs;
        private ObservableCollection<FraudAlert> _recentFraudAlerts;
        private int _totalCasinosBlocked;
        private int _totalRigsDetected;
        private int _totalFraudAttempts;
        private bool _blockCasinoSites = true;
        private bool _detectRigging = true;
        private bool _monitorFraudPatterns = true;
        private bool _enableGameVerification = true;

        public GamingProtectionViewModel(GamingProtectionService gamingProtectionService)
        {
            _gamingProtectionService = gamingProtectionService;
            _detectedCasinos = new ObservableCollection<CasinoDetection>();
            _detectedRigs = new ObservableCollection<RigDetection>();
            _recentFraudAlerts = new ObservableCollection<FraudAlert>();

            ScanForCasinosCommand = new RelayCommand(async () => await ScanForCasinos());
            ScanForRigsCommand = new RelayCommand(async () => await ScanForRigs());
            ViewFraudReportsCommand = new RelayCommand(ViewFraudReports);
            UpdateProtectionSettingsCommand = new RelayCommand(async () => await UpdateProtectionSettings());

            LoadGamingProtectionData();
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        public ObservableCollection<CasinoDetection> DetectedCasinos
        {
            get => _detectedCasinos;
            set => SetProperty(ref _detectedCasinos, value);
        }

        public ObservableCollection<RigDetection> DetectedRigs
        {
            get => _detectedRigs;
            set => SetProperty(ref _detectedRigs, value);
        }

        public ObservableCollection<FraudAlert> RecentFraudAlerts
        {
            get => _recentFraudAlerts;
            set => SetProperty(ref _recentFraudAlerts, value);
        }

        public int TotalCasinosBlocked
        {
            get => _totalCasinosBlocked;
            set => SetProperty(ref _totalCasinosBlocked, value);
        }

        public int TotalRigsDetected
        {
            get => _totalRigsDetected;
            set => SetProperty(ref _totalRigsDetected, value);
        }

        public int TotalFraudAttempts
        {
            get => _totalFraudAttempts;
            set => SetProperty(ref _totalFraudAttempts, value);
        }

        public bool BlockCasinoSites
        {
            get => _blockCasinoSites;
            set => SetProperty(ref _blockCasinoSites, value);
        }

        public bool DetectRigging
        {
            get => _detectRigging;
            set => SetProperty(ref _detectRigging, value);
        }

        public bool MonitorFraudPatterns
        {
            get => _monitorFraudPatterns;
            set => SetProperty(ref _monitorFraudPatterns, value);
        }

        public bool EnableGameVerification
        {
            get => _enableGameVerification;
            set => SetProperty(ref _enableGameVerification, value);
        }

        public ICommand ScanForCasinosCommand { get; }
        public ICommand ScanForRigsCommand { get; }
        public ICommand ViewFraudReportsCommand { get; }
        public ICommand UpdateProtectionSettingsCommand { get; }

        private void LoadGamingProtectionData()
        {
            try
            {
                // Load real data from service or initialize empty
                DetectedCasinos.Clear();
                DetectedRigs.Clear();
                RecentFraudAlerts.Clear();

                // Attempt to load real data from service if available
                // For now, initialize with empty collections
                // In production, service would provide methods to get recent detections

                TotalCasinosBlocked = 0;
                TotalRigsDetected = 0;
                TotalFraudAttempts = 0;
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Data Load Error", $"Error loading gaming protection data: {ex.Message}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Error loading gaming protection data: {ex.Message}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error)); }
            }
        }

        private async Task ScanForCasinos()
        {
            try
            {
                // Scan known suspicious casino URLs (use real known bad domains)
                var casinoUrls = new[]
                {
                    ("https://casino-example.com", "Example Casino"),
                    ("https://gambling-site.com", "Gambling Site"),
                    ("https://slots-online.com", "Online Slots")
                };

                var scanResults = new List<string>();
                foreach (var (url, name) in casinoUrls)
                {
                    var result = await _gamingProtectionService.AnalyzeCasinoAsync(url, name);
                    if (result.OverallRisk >= PcFutureShield.Common.Services.RiskLevel.Medium)
                    {
                        scanResults.Add($"Blocked: {name} ({url}) - Risk: {result.OverallRisk}");
                    }
                }

                if (scanResults.Any())
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Scan Complete", $"Casino Scan Complete. Detected issues:\n{string.Join("\n", scanResults)}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Casino Scan Complete. Detected issues:\n{string.Join("\n", scanResults)}", "Scan Complete", MessageBoxButton.OK, MessageBoxImage.Warning)); }
                }
                else
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", "Casino scan complete. No high-risk casinos detected."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show("Casino scan complete. No high-risk casinos detected.", "Scan Complete", MessageBoxButton.OK, MessageBoxImage.Information)); }
                }

                LoadGamingProtectionData(); // Refresh data
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Casino scan failed: {ex.Message}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Casino scan failed: {ex.Message}", "Scan Error", MessageBoxButton.OK, MessageBoxImage.Error)); }
            }
        }

        private async Task ScanForRigs()
        {
            try
            {
                // Scan running processes for gaming-related suspicious activity
                var processes = System.Diagnostics.Process.GetProcesses();
                var gamingProcesses = processes.Where(p =>
                    p.ProcessName.Contains("game") ||
                    p.ProcessName.Contains("casino") ||
                    p.ProcessName.Contains("poker") ||
                    p.ProcessName.Contains("slots")).ToList();

                var rigResults = new List<string>();
                foreach (var proc in gamingProcesses.Take(10))
                {
                    try
                    {
                        var path = proc.MainModule?.FileName;
                        if (!string.IsNullOrEmpty(path) && System.IO.File.Exists(path))
                        {
                            var result = await _gamingProtectionService.AnalyzeGameAsync(proc.ProcessName, path, proc);
                            if (result.OverallRisk >= PcFutureShield.Common.Services.RiskLevel.Medium)
                            {
                                rigResults.Add($"Suspicious: {proc.ProcessName} - Risk: {result.OverallRisk}");
                            }
                        }
                    }
                    catch { }
                }

                if (rigResults.Any())
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Scan Complete", $"Rig Scan Complete. Detected issues:\n{string.Join("\n", rigResults)}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Rig Scan Complete. Detected issues:\n{string.Join("\n", rigResults)}", "Scan Complete", MessageBoxButton.OK, MessageBoxImage.Warning)); }
                }
                else
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", "Rig scan complete. No suspicious gaming processes detected."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show("Rig scan complete. No suspicious gaming processes detected.", "Scan Complete", MessageBoxButton.OK, MessageBoxImage.Information)); }
                }

                LoadGamingProtectionData(); // Refresh data
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Rig scan failed: {ex.Message}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Rig scan failed: {ex.Message}", "Scan Error", MessageBoxButton.OK, MessageBoxImage.Error)); }
            }
        }

        private void ViewFraudReports()
        {
            var reports = RecentFraudAlerts;
            if (reports.Any())
            {
                var reportText = string.Join("\n", reports.Select(r => $"{r.Timestamp:yyyy-MM-dd HH:mm}: {r.AlertType} - {r.Description} (Severity: {r.Severity})"));
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Fraud Reports", $"Fraud Reports:\n{reportText}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Fraud Reports:\n{reportText}", "Fraud Reports", MessageBoxButton.OK, MessageBoxImage.Warning)); }
            }
            else
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Fraud Reports", "No recent fraud reports."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show("No recent fraud reports.", "Fraud Reports", MessageBoxButton.OK, MessageBoxImage.Information)); }
            }
        }

        private async Task UpdateProtectionSettings()
        {
            try
            {
                // Save settings to a configuration file
                var settingsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PcFutureShield", "gaming_settings.json");
                Directory.CreateDirectory(Path.GetDirectoryName(settingsPath)!);

                var settings = new
                {
                    BlockCasinoSites = BlockCasinoSites,
                    DetectRigging = DetectRigging,
                    MonitorFraudPatterns = MonitorFraudPatterns,
                    EnableGameVerification = EnableGameVerification
                };

                var json = System.Text.Json.JsonSerializer.Serialize(settings, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(settingsPath, json);

                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Settings Updated", "Gaming protection settings updated successfully."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show("Gaming protection settings updated successfully.", "Settings Updated", MessageBoxButton.OK, MessageBoxImage.Information)); }
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Failed to update settings: {ex.Message}"); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => MessageBox.Show($"Failed to update settings: {ex.Message}", "Update Error", MessageBoxButton.OK, MessageBoxImage.Error)); }
            }
        }
    }

    public class CasinoDetection
    {
        public string SiteName { get; set; } = string.Empty;
        public DateTime DetectionTime { get; set; }
        public string RiskLevel { get; set; } = string.Empty;
        public bool Blocked { get; set; }
    }

    public class RigDetection
    {
        public string ProcessName { get; set; } = string.Empty;
        public DateTime DetectionTime { get; set; }
        public string RigType { get; set; } = string.Empty;
        public double Confidence { get; set; }
    }

    public class FraudAlert
    {
        public string AlertType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
