using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Linq;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class EventLogViewModel : BaseViewModel
    : System.ComponentModel.INotifyPropertyChanged
{
        private readonly IEventLogService _eventLogService;

        public ObservableCollection<RealtimeEvent> Events { get; } = new();

        private RealtimeEvent? _selectedEvent;
        public RealtimeEvent? SelectedEvent
        {
            get => _selectedEvent;
            set => SetProperty(ref _selectedEvent, value);
        }

        private string _filterText = string.Empty;
        public string FilterText
        {
            get => _filterText;
            set
            {
                if (SetProperty(ref _filterText, value))
                {
                    ApplyFilter();
                }
            }
        }

        private string _selectedSeverity = "All";
        public string SelectedSeverity
        {
            get => _selectedSeverity;
            set
            {
                if (SetProperty(ref _selectedSeverity, value))
                {
                    ApplyFilter();
                }
            }
        }

        private ObservableCollection<string> _availableSeverities;
        public ObservableCollection<string> AvailableSeverities { get; } = new() { "All", "Critical", "High", "Medium", "Low", "Info" };

        public ICommand ClearEventsCommand { get; }
        public ICommand ExportEventsCommand { get; }
        public ICommand ViewEventDetailsCommand { get; }
        public ICommand RefreshEventsCommand { get; }

        public EventLogViewModel()
        {
            _eventLogService = ServiceLocator.Get<IEventLogService>();
            _availableSeverities = new ObservableCollection<string>(AvailableSeverities);

            ClearEventsCommand = new RelayCommand(ClearEvents);
            ExportEventsCommand = new RelayCommand(ExportEvents);
            ViewEventDetailsCommand = new RelayCommand(ViewEventDetails, () => SelectedEvent != null);
            RefreshEventsCommand = new RelayCommand(RefreshEvents);

            LoadEvents();
        }

        private void LoadEvents()
        {
            try
            {
                Events.Clear();

                // Load recent events from the event log service
                var recentEvents = _eventLogService.GetRecentEvents();
                foreach (var evt in recentEvents)
                {
                    Events.Add(evt);
                }

                // Sort events by time (most recent first)
                var sortedEvents = Events.OrderByDescending(e => e.Time).ToList();
                Events.Clear();
                foreach (var evt in sortedEvents)
                {
                    Events.Add(evt);
                }
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Load Error", $"Error loading events: {ex.Message}"); } catch { System.Windows.MessageBox.Show($"Error loading events: {ex.Message}", "Load Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error); }
            }
        }

        private void ApplyFilter()
        {
            try
            {
                var allEvents = _eventLogService.GetRecentEvents();

                var filteredEvents = allEvents.Where(evt =>
                {
                    // Filter by severity
                    if (SelectedSeverity != "All" && !evt.Severity.Equals(SelectedSeverity, StringComparison.OrdinalIgnoreCase))
                        return false;

                    // Filter by text
                    if (!string.IsNullOrWhiteSpace(FilterText))
                    {
                        var searchText = FilterText.ToLower();
                        return evt.Event.ToLower().Contains(searchText) ||
                               evt.File.ToLower().Contains(searchText) ||
                               evt.Severity.ToLower().Contains(searchText);
                    }

                    return true;
                }).OrderByDescending(e => e.Time).ToList();

                Events.Clear();
                foreach (var evt in filteredEvents)
                {
                    Events.Add(evt);
                }
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Filter Error", $"Error applying filter: {ex.Message}"); } catch { System.Windows.MessageBox.Show($"Error applying filter: {ex.Message}", "Filter Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error); }
            }
        }

        private void ClearEvents()
        {
            var result = System.Windows.MessageBox.Show(
                "Clear all events from the log? This action cannot be undone.",
                "Confirm Clear",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    _eventLogService.ClearEvents();
                    Events.Clear();
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Events Cleared", "Event log cleared successfully."); } catch { System.Windows.MessageBox.Show("Event log cleared successfully.", "Events Cleared", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information); }
                }
                catch (Exception ex)
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Clear Error", $"Error clearing events: {ex.Message}"); } catch { System.Windows.MessageBox.Show($"Error clearing events: {ex.Message}", "Clear Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error); }
                }
            }
        }

        private void ExportEvents()
        {
            try
            {
                var exportPath = _eventLogService.ExportEvents();
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Export Complete", $"Events exported successfully to: {exportPath}"); } catch { System.Windows.MessageBox.Show($"Events exported successfully to: {exportPath}", "Export Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information); }
            }
            catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Export Error", $"Error exporting events: {ex.Message}"); } catch { System.Windows.MessageBox.Show($"Error exporting events: {ex.Message}", "Export Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error); }
            }
        }

        private void ViewEventDetails()
        {
            if (SelectedEvent == null) return;

            var details = $"Event Details:\n\n" +
                         $"Time: {SelectedEvent.Time:G}\n" +
                         $"Severity: {SelectedEvent.Severity}\n" +
                         $"Event: {SelectedEvent.Event}\n" +
                         $"File: {SelectedEvent.File}";

            try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Event Details", details); } catch { System.Windows.MessageBox.Show(details, "Event Details", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information); }
        }

        private void RefreshEvents()
        {
            LoadEvents();
        }
    }


		public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
		protected void OnPropertyChanged(string name) =>
			PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(name));
}
