using System;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.Services
{
    public class LicenseManager : ILicenseManager
    {
        private string _licenseStatus = "Trial";
        public string GetLicenseStatus()
        {
            // Check for a license file in the application data directory
            string appData = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData);
            string licensePath = System.IO.Path.Combine(appData, "PcFutureShield", "license.key");
            if (System.IO.File.Exists(licensePath))
            {
                string key = System.IO.File.ReadAllText(licensePath).Trim();
                if (key == "PCFS-TRIAL-2025")
                {
                    _licenseStatus = "Trial";
                }
                else if (key == "PCFS-ACTIVE-2025")
                {
                    _licenseStatus = "Active";
                }
                else if (key == "PCFS-EXPIRED-2025")
                {
                    _licenseStatus = "Expired";
                }
                else
                {
                    _licenseStatus = "Invalid";
                }
            }
            else
            {
                _licenseStatus = "No License";
            }
            return _licenseStatus;
        }
        // Optionally, add methods to activate, deactivate, etc.
    }
}
