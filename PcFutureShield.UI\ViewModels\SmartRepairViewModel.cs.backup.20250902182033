using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using PcFutureShield.Common.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class SmartRepairViewModel : BaseViewModel
    {
        private readonly PcOptimizationService _optimizationService;

        // System Health Properties
        private string _systemHealthStatus = "Analyzing...";
        public string SystemHealthStatus
        {
            get => _systemHealthStatus;
            set => SetProperty(ref _systemHealthStatus, value);
        }

        private int _issuesFound;
        public int IssuesFound
        {
            get => _issuesFound;
            set => SetProperty(ref _issuesFound, value);
        }

        private int _repairsCompleted;
        public int RepairsCompleted
        {
            get => _repairsCompleted;
            set => SetProperty(ref _repairsCompleted, value);
        }

        // Repair Progress Properties
        private string _currentRepairOperation = "Ready";
        public string CurrentRepairOperation
        {
            get => _currentRepairOperation;
            set => SetProperty(ref _currentRepairOperation, value);
        }

        private double _repairProgress;
        public double RepairProgress
        {
            get => _repairProgress;
            set => SetProperty(ref _repairProgress, value);
        }

        private string _repairStatus = "Ready to repair";
        public string RepairStatus
        {
            get => _repairStatus;
            set => SetProperty(ref _repairStatus, value);
        }

        // Commands
        public ICommand FixRegistryCommand { get; }
        public ICommand RepairSystemFilesCommand { get; }
        public ICommand CleanStartupCommand { get; }
        public ICommand FixNetworkCommand { get; }
        public ICommand RepairWindowsUpdateCommand { get; }
        public ICommand OptimizePerformanceCommand { get; }
        public ICommand RunDiagnosticsCommand { get; }
        public ICommand GenerateReportCommand { get; }
        public ICommand EmergencyRepairCommand { get; }

        public SmartRepairViewModel(PcOptimizationService optimizationService)
        {
            _optimizationService = optimizationService ?? throw new ArgumentNullException(nameof(optimizationService));

            // Initialize commands
            FixRegistryCommand = new RelayCommand(async () => await FixRegistryAsync());
            RepairSystemFilesCommand = new RelayCommand(async () => await RepairSystemFilesAsync());
            CleanStartupCommand = new RelayCommand(async () => await CleanStartupAsync());
            FixNetworkCommand = new RelayCommand(async () => await FixNetworkAsync());
            RepairWindowsUpdateCommand = new RelayCommand(async () => await RepairWindowsUpdateAsync());
            OptimizePerformanceCommand = new RelayCommand(async () => await OptimizePerformanceAsync());
            RunDiagnosticsCommand = new RelayCommand(async () => await RunDiagnosticsAsync());
            GenerateReportCommand = new RelayCommand(async () => await GenerateReportAsync());
            EmergencyRepairCommand = new RelayCommand(async () => await EmergencyRepairAsync());

            // Initial diagnostics
            _ = RunDiagnosticsAsync();
        }

        private async Task RunDiagnosticsAsync()
        {
            try
            {
                CurrentRepairOperation = "Running system diagnostics...";
                RepairProgress = 0;

                var healthReport = await _optimizationService.GenerateSystemHealthReportAsync();

                RepairProgress = 50;

                // Analyze issues
                IssuesFound = 0;
                if (healthReport.CpuHealth?.HealthScore < 0.7) IssuesFound++;
                if (healthReport.MemoryHealth?.HealthScore < 0.7) IssuesFound++;
                if (healthReport.DiskHealth?.HealthScore < 0.7) IssuesFound++;

                SystemHealthStatus = healthReport.OverallHealthScore > 0.8 ? "System Healthy" :
                                   healthReport.OverallHealthScore > 0.6 ? "Minor Issues" : "Critical Issues";

                RepairProgress = 100;
                CurrentRepairOperation = "Diagnostics complete";
                RepairStatus = $"Found {IssuesFound} issues requiring attention";
            }
            catch (Exception ex)
            {
                SystemHealthStatus = "Diagnostics failed";
                RepairStatus = $"Error: {ex.Message}";
            }
        }

        private async Task FixRegistryAsync()
        {
            try
            {
                CurrentRepairOperation = "Fixing registry issues...";
                RepairProgress = 0;
                RepairStatus = "Scanning registry for issues...";

                var options = new PcFutureShield.Common.Services.RepairOptions
                {
                    RepairSystemFiles = false,
                    FixWindowsCorruption = false,
                    RepairBootIssues = false,
                    FixDriverIssues = false,
                    RepairNetworkIssues = false
                };

                // Use the repair service for registry cleanup
                var result = await _optimizationService.PerformSystemRepairAsync(options);

                RepairProgress = 100;
                RepairsCompleted++;
                CurrentRepairOperation = "Registry repair complete";
                RepairStatus = "Registry issues fixed successfully";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Registry repair failed: {ex.Message}";
            }
        }

        private async Task RepairSystemFilesAsync()
        {
            try
            {
                CurrentRepairOperation = "Repairing system files...";
                RepairProgress = 0;
                RepairStatus = "Checking system file integrity...";

                var options = new PcFutureShield.Common.Services.RepairOptions
                {
                    RepairSystemFiles = true,
                    FixWindowsCorruption = false,
                    RepairBootIssues = false,
                    FixDriverIssues = false,
                    RepairNetworkIssues = false
                };

                var result = await _optimizationService.PerformSystemRepairAsync(options);

                RepairProgress = 100;
                RepairsCompleted++;
                CurrentRepairOperation = "System file repair complete";
                RepairStatus = "System files repaired successfully";
            }
            catch (Exception ex)
            {
                RepairStatus = $"System file repair failed: {ex.Message}";
            }
        }

        private async Task CleanStartupAsync()
        {
            try
            {
                CurrentRepairOperation = "Cleaning startup programs...";
                RepairProgress = 0;
                RepairStatus = "Analyzing startup programs...";

                var options = new PcFutureShield.Common.Services.OptimizationOptions
                {
                    CleanTempFiles = false,
                    RemoveBloatware = false,
                    OptimizeStartup = true,
                    DefragmentDrives = false,
                    OptimizeMemory = false,
                    CleanRegistry = false
                };

                var result = await _optimizationService.PerformDeepOptimizationAsync(options);

                RepairProgress = 100;
                RepairsCompleted++;
                CurrentRepairOperation = "Startup cleanup complete";
                RepairStatus = "Startup programs optimized";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Startup cleanup failed: {ex.Message}";
            }
        }

        private async Task FixNetworkAsync()
        {
            try
            {
                CurrentRepairOperation = "Fixing network issues...";
                RepairProgress = 0;
                RepairStatus = "Diagnosing network problems...";

                var options = new PcFutureShield.Common.Services.RepairOptions
                {
                    RepairSystemFiles = false,
                    FixWindowsCorruption = false,
                    RepairBootIssues = false,
                    FixDriverIssues = false,
                    RepairNetworkIssues = true
                };

                var result = await _optimizationService.PerformSystemRepairAsync(options);

                RepairProgress = 100;
                RepairsCompleted++;
                CurrentRepairOperation = "Network repair complete";
                RepairStatus = "Network issues resolved";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Network repair failed: {ex.Message}";
            }
        }

        private async Task RepairWindowsUpdateAsync()
        {
            try
            {
                CurrentRepairOperation = "Repairing Windows Update...";
                RepairProgress = 0;
                RepairStatus = "Checking Windows Update components...";

                // Use system repair for Windows Update issues
                var options = new PcFutureShield.Common.Services.RepairOptions
                {
                    RepairSystemFiles = true,
                    FixWindowsCorruption = true,
                    RepairBootIssues = false,
                    FixDriverIssues = false,
                    RepairNetworkIssues = false
                };

                var result = await _optimizationService.PerformSystemRepairAsync(options);

                RepairProgress = 100;
                RepairsCompleted++;
                CurrentRepairOperation = "Windows Update repair complete";
                RepairStatus = "Windows Update components repaired";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Windows Update repair failed: {ex.Message}";
            }
        }

        private async Task OptimizePerformanceAsync()
        {
            try
            {
                CurrentRepairOperation = "Optimizing performance...";
                RepairProgress = 0;
                RepairStatus = "Running performance optimizations...";

                var result = await _optimizationService.BoostPerformanceAsync();

                RepairProgress = 100;
                RepairsCompleted++;
                CurrentRepairOperation = "Performance optimization complete";
                RepairStatus = $"Performance improved by {(result.PerformanceImprovement * 100):F1}%";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Performance optimization failed: {ex.Message}";
            }
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                CurrentRepairOperation = "Generating repair report...";
                RepairProgress = 0;

                var healthReport = await _optimizationService.GenerateSystemHealthReportAsync();

                var report = new
                {
                    GeneratedAt = DateTime.UtcNow,
                    SystemHealth = SystemHealthStatus,
                    IssuesFound = IssuesFound,
                    RepairsCompleted = RepairsCompleted,
                    HealthReport = healthReport
                };

                // Save report to file
                var reportPath = System.IO.Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    $"PcFutureShield_Repair_Report_{DateTime.Now:yyyyMMdd_HHmmss}.json");

                var json = System.Text.Json.JsonSerializer.Serialize(report, new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                await System.IO.File.WriteAllTextAsync(reportPath, json);

                RepairProgress = 100;
                CurrentRepairOperation = "Report generated";
                RepairStatus = $"Report saved to: {reportPath}";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Report generation failed: {ex.Message}";
            }
        }

        private async Task EmergencyRepairAsync()
        {
            try
            {
                CurrentRepairOperation = "Running emergency repair...";
                RepairProgress = 0;
                RepairStatus = "Performing comprehensive system repair...";

                // Run all repair operations
                var tasks = new[]
                {
                    FixRegistryAsync(),
                    RepairSystemFilesAsync(),
                    CleanStartupAsync(),
                    FixNetworkAsync(),
                    RepairWindowsUpdateAsync(),
                    OptimizePerformanceAsync()
                };

                await Task.WhenAll(tasks);

                RepairProgress = 100;
                CurrentRepairOperation = "Emergency repair complete";
                RepairStatus = "All critical issues addressed";
            }
            catch (Exception ex)
            {
                RepairStatus = $"Emergency repair failed: {ex.Message}";
            }
        }
    }
}
