﻿#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Win32;
using System.Threading;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Comprehensive PC optimization and repair service
    /// </summary>
    public class PcOptimizationService
    {
        private readonly string _optimizationDbPath;
        private readonly SystemAnalyzer _systemAnalyzer;
        private readonly PerformanceOptimizer _performanceOptimizer;
        private readonly RepairEngine _repairEngine;

        public PcOptimizationService()
        {
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var optimizationPath = Path.Combine(appData, "PcFutureShield", "Optimization");
            Directory.CreateDirectory(optimizationPath);
            _optimizationDbPath = Path.Combine(optimizationPath, "optimization_db.json");

            _systemAnalyzer = new SystemAnalyzer();
            _performanceOptimizer = new PerformanceOptimizer();
            _repairEngine = new RepairEngine();
        }

        public async Task<SystemHealthReport> GenerateSystemHealthReportAsync()
        {
            var report = new SystemHealthReport
            {
                ScanTime = DateTime.UtcNow,
                SystemInfo = await _systemAnalyzer.GetSystemInformationAsync()
            };

            try
            {
                // Analyze CPU health
                report.CpuHealth = await _systemAnalyzer.AnalyzeCpuHealthAsync();

                // Analyze memory health
                report.MemoryHealth = await _systemAnalyzer.AnalyzeMemoryHealthAsync();

                // Analyze disk health
                report.DiskHealth = await _systemAnalyzer.AnalyzeDiskHealthAsync();

                // Analyze system performance
                report.PerformanceMetrics = await _systemAnalyzer.GetPerformanceMetricsAsync();

                // Generate overall health score
                report.OverallHealthScore = CalculateOverallHealthScore(report);

                // Generate recommendations
                report.Recommendations = GenerateHealthRecommendations(report);

            }
            catch (Exception ex)
            {
                report.Error = ex.Message;
                report.OverallHealthScore = 0;
            }

            return report;
        }

        public async Task<OptimizationResult> PerformDeepOptimizationAsync(OptimizationOptions options)
        {
            var result = new OptimizationResult
            {
                StartTime = DateTime.UtcNow,
                Options = options
            };

            try
            {
                // Clean temporary files
                if (options.CleanTempFiles)
                {
                    result.TempFileCleanup = await _performanceOptimizer.CleanTemporaryFilesAsync();
                }

                // Remove bloatware (detection and optional report)
                if (options.RemoveBloatware)
                {
                    result.BloatwareRemoval = await _performanceOptimizer.RemoveBloatwareAsync();
                }

                // Optimize startup programs (analysis)
                if (options.OptimizeStartup)
                {
                    result.StartupOptimization = await _performanceOptimizer.OptimizeStartupProgramsAsync();
                }

                // Defragment drives
                if (options.DefragmentDrives)
                {
                    result.DiskDefragmentation = await _performanceOptimizer.DefragmentDrivesAsync();
                }

                // Optimize memory
                if (options.OptimizeMemory)
                {
                    result.MemoryOptimization = await _performanceOptimizer.OptimizeMemoryAsync();
                }

                // Clean registry
                if (options.CleanRegistry)
                {
                    result.RegistryCleanup = await _performanceOptimizer.CleanRegistryAsync();
                }

                // Calculate optimization impact
                result.OptimizationScore = CalculateOptimizationScore(result);

                result.EndTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<RepairResult> PerformSystemRepairAsync(RepairOptions options)
        {
            var result = new RepairResult
            {
                StartTime = DateTime.UtcNow,
                Options = options
            };

            try
            {
                // Repair system files
                if (options.RepairSystemFiles)
                {
                    result.SystemFileRepair = await _repairEngine.RepairSystemFilesAsync();
                }

                // Fix Windows corruption
                if (options.FixWindowsCorruption)
                {
                    result.WindowsCorruptionFix = await _repairEngine.FixWindowsCorruptionAsync();
                }

                // Repair boot issues
                if (options.RepairBootIssues)
                {
                    result.BootRepair = await _repairEngine.RepairBootIssuesAsync();
                }

                // Fix driver issues
                if (options.FixDriverIssues)
                {
                    result.DriverRepair = await _repairEngine.RepairDriversAsync();
                }

                // Network repair
                if (options.RepairNetworkIssues)
                {
                    result.NetworkRepair = await _repairEngine.RepairNetworkAsync();
                }

                // Calculate repair success rate
                result.SuccessRate = CalculateRepairSuccessRate(result);

                result.EndTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<PerformanceBoostResult> BoostPerformanceAsync()
        {
            var result = new PerformanceBoostResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Get baseline performance
                result.BaselineMetrics = await _systemAnalyzer.GetPerformanceMetricsAsync();

                // Apply performance optimizations
                var memoryTask = _performanceOptimizer.OptimizeMemoryAsync();
                var cpuTask = _performanceOptimizer.OptimizeCpuAsync();
                var diskTask = _performanceOptimizer.OptimizeDiskAsync();
                var networkTask = _performanceOptimizer.OptimizeNetworkAsync();

                await Task.WhenAll(memoryTask, cpuTask, diskTask, networkTask);

                // Store results
                var memoryResult = await memoryTask;
                var cpuResult = await cpuTask;
                var diskResult = await diskTask;
                var networkResult = await networkTask;

                // Get post-optimization metrics
                result.PostOptimizationMetrics = await _systemAnalyzer.GetPerformanceMetricsAsync();

                // Calculate performance improvement
                result.PerformanceImprovement = CalculatePerformanceImprovement(
                    result.BaselineMetrics, result.PostOptimizationMetrics);

                result.EndTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<UninstallResult> PerformSmartUninstallAsync(List<string> programsToRemove)
        {
            var result = new UninstallResult
            {
                StartTime = DateTime.UtcNow,
                ProgramsToRemove = programsToRemove
            };

            try
            {
                foreach (var program in programsToRemove)
                {
                    var uninstallResult = await _performanceOptimizer.UninstallProgramAsync(program);
                    result.UninstallResults.Add(uninstallResult);
                }

                // Clean up leftovers
                result.LeftoversCleanup = await _performanceOptimizer.CleanUninstallLeftoversAsync(programsToRemove);

                // Calculate success rate
                result.SuccessRate = result.UninstallResults.Count(r => r.Success) / (double)result.UninstallResults.Count;

                result.EndTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        private double CalculateOverallHealthScore(SystemHealthReport report)
        {
            var scores = new List<double>();

            if (report.CpuHealth != null)
                scores.Add(report.CpuHealth.HealthScore);

            if (report.MemoryHealth != null)
                scores.Add(report.MemoryHealth.HealthScore);

            if (report.DiskHealth != null)
                scores.Add(report.DiskHealth.HealthScore);

            if (report.PerformanceMetrics != null)
                scores.Add(CalculatePerformanceScore(report.PerformanceMetrics));

            return scores.Count > 0 ? scores.Average() : 0.0;
        }

        private double CalculatePerformanceScore(PerformanceMetrics metrics)
        {
            // Calculate performance score based on various metrics
            var score = 1.0;

            // CPU usage impact
            if (metrics.CpuUsage > 80) score -= 0.3;
            else if (metrics.CpuUsage > 60) score -= 0.1;

            // Memory usage impact
            if (metrics.MemoryUsagePercent > 90) score -= 0.3;
            else if (metrics.MemoryUsagePercent > 75) score -= 0.1;

            // Disk usage impact
            if (metrics.DiskUsagePercent > 95) score -= 0.2;
            else if (metrics.DiskUsagePercent > 85) score -= 0.1;

            return Math.Max(0.0, Math.Min(1.0, score));
        }

        private List<string> GenerateHealthRecommendations(SystemHealthReport report)
        {
            var recommendations = new List<string>();

            if (report.CpuHealth?.HealthScore < 0.7)
                recommendations.Add("CPU health is poor - consider hardware upgrade or cooling improvements");

            if (report.MemoryHealth?.HealthScore < 0.7)
                recommendations.Add("Memory health is poor - consider adding more RAM or replacing faulty modules");

            if (report.DiskHealth?.HealthScore < 0.7)
                recommendations.Add("Disk health is poor - backup data and consider disk replacement");

            if (report.PerformanceMetrics?.CpuUsage > 80)
                recommendations.Add("High CPU usage detected - close unnecessary programs");

            if (report.PerformanceMetrics?.MemoryUsagePercent > 90)
                recommendations.Add("High memory usage - close memory-intensive applications");

            if (report.OverallHealthScore < 0.5)
                recommendations.Add("System health is critical - professional repair recommended");

            return recommendations;
        }

        private double CalculateOptimizationScore(OptimizationResult result)
        {
            var score = 0.0;
            var totalOperations = 0;

            if (result.TempFileCleanup != null) { score += result.TempFileCleanup.Success ? 1 : 0; totalOperations++; }
            if (result.BloatwareRemoval != null) { score += result.BloatwareRemoval.Success ? 1 : 0; totalOperations++; }
            if (result.StartupOptimization != null) { score += result.StartupOptimization.Success ? 1 : 0; totalOperations++; }
            if (result.DiskDefragmentation != null) { score += result.DiskDefragmentation.Success ? 1 : 0; totalOperations++; }
            if (result.MemoryOptimization != null) { score += result.MemoryOptimization.Success ? 1 : 0; totalOperations++; }
            if (result.RegistryCleanup != null) { score += result.RegistryCleanup.Success ? 1 : 0; totalOperations++; }

            return totalOperations > 0 ? score / totalOperations : 0.0;
        }

        private double CalculateRepairSuccessRate(RepairResult result)
        {
            var successful = 0;
            var total = 0;

            if (result.SystemFileRepair != null) { if (result.SystemFileRepair.Success) successful++; total++; }
            if (result.WindowsCorruptionFix != null) { if (result.WindowsCorruptionFix.Success) successful++; total++; }
            if (result.BootRepair != null) { if (result.BootRepair.Success) successful++; total++; }
            if (result.DriverRepair != null) { if (result.DriverRepair.Success) successful++; total++; }
            if (result.NetworkRepair != null) { if (result.NetworkRepair.Success) successful++; total++; }

            return total > 0 ? (double)successful / total : 0.0;
        }

        private double CalculatePerformanceImprovement(PerformanceMetrics baseline, PerformanceMetrics postOptimization)
        {
            var improvements = new List<double>();

            // CPU improvement
            if (baseline.CpuUsage > postOptimization.CpuUsage)
            {
                var improvement = (baseline.CpuUsage - postOptimization.CpuUsage) / baseline.CpuUsage;
                improvements.Add(improvement);
            }

            // Memory improvement
            if (baseline.MemoryUsagePercent > postOptimization.MemoryUsagePercent)
            {
                var improvement = (baseline.MemoryUsagePercent - postOptimization.MemoryUsagePercent) / baseline.MemoryUsagePercent;
                improvements.Add(improvement);
            }

            // Disk improvement
            if (baseline.DiskUsagePercent > postOptimization.DiskUsagePercent)
            {
                var improvement = (baseline.DiskUsagePercent - postOptimization.DiskUsagePercent) / baseline.DiskUsagePercent;
                improvements.Add(improvement);
            }

            return improvements.Count > 0 ? improvements.Average() : 0.0;
        }
    }

    // Supporting classes for PC Optimization
    public class SystemHealthReport
    {
        public DateTime ScanTime { get; set; }
        public SystemInformation? SystemInfo { get; set; }
        public CpuHealth? CpuHealth { get; set; }
        public MemoryHealth? MemoryHealth { get; set; }
        public DiskHealth? DiskHealth { get; set; }
        public PerformanceMetrics? PerformanceMetrics { get; set; }
        public double OverallHealthScore { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class OptimizationResult
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public OptimizationOptions Options { get; set; } = new();
        public CleanupResult? TempFileCleanup { get; set; }
        public BloatwareResult? BloatwareRemoval { get; set; }
        public StartupResult? StartupOptimization { get; set; }
        public DefragResult? DiskDefragmentation { get; set; }
        public MemoryResult? MemoryOptimization { get; set; }
        public RegistryResult? RegistryCleanup { get; set; }
        public double OptimizationScore { get; set; }
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class RepairResult
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public RepairOptions Options { get; set; } = new();
        public RepairOperation? SystemFileRepair { get; set; }
        public RepairOperation? WindowsCorruptionFix { get; set; }
        public RepairOperation? BootRepair { get; set; }
        public RepairOperation? DriverRepair { get; set; }
        public RepairOperation? NetworkRepair { get; set; }
        public double SuccessRate { get; set; }
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class PerformanceBoostResult
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public PerformanceMetrics? BaselineMetrics { get; set; }
        public PerformanceMetrics? PostOptimizationMetrics { get; set; }
        public double PerformanceImprovement { get; set; }
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class UninstallResult
    {
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public List<string> ProgramsToRemove { get; set; } = new();
        public List<UninstallOperation> UninstallResults { get; set; } = new();
        public CleanupResult? LeftoversCleanup { get; set; }
        public double SuccessRate { get; set; }
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class SystemInformation
    {
        public string OsVersion { get; set; } = string.Empty;
        public string Processor { get; set; } = string.Empty;
        public int ProcessorCount { get; set; }
        public long TotalMemory { get; set; }
        public long AvailableMemory { get; set; }
        public List<DiskInfo> Disks { get; set; } = new();
        public string ComputerName { get; set; } = string.Empty;
        public TimeSpan Uptime { get; set; }
    }

    public class DiskInfo
    {
        public string Name { get; set; } = string.Empty;
        public long TotalSize { get; set; }
        public long FreeSpace { get; set; }
        public string FileSystem { get; set; } = string.Empty;
        public bool IsSystemDisk { get; set; }
    }

    public class CpuHealth
    {
        public double HealthScore { get; set; }
        public double Temperature { get; set; }
        public double UsagePercent { get; set; }
        public int CoreCount { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    public class MemoryHealth
    {
        public double HealthScore { get; set; }
        public long TotalMemory { get; set; }
        public long UsedMemory { get; set; }
        public double UsagePercent { get; set; }
        public List<string> Issues { get; set; } = new();
    }

    public class DiskHealth
    {
        public double HealthScore { get; set; }
        public List<DiskInfo> Disks { get; set; } = new();
        public List<string> Issues { get; set; } = new();
    }

    public class PerformanceMetrics
    {
        public double CpuUsage { get; set; }
        public double MemoryUsagePercent { get; set; }
        public double DiskUsagePercent { get; set; }
        public int ProcessCount { get; set; }
        public int ThreadCount { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class OptimizationOptions
    {
        public bool CleanTempFiles { get; set; } = true;
        public bool RemoveBloatware { get; set; } = true;
        public bool OptimizeStartup { get; set; } = true;
        public bool DefragmentDrives { get; set; } = true;
        public bool OptimizeMemory { get; set; } = true;
        public bool CleanRegistry { get; set; } = true;
    }

    public class RepairOptions
    {
        public bool RepairSystemFiles { get; set; } = true;
        public bool FixWindowsCorruption { get; set; } = true;
        public bool RepairBootIssues { get; set; } = true;
        public bool FixDriverIssues { get; set; } = true;
        public bool RepairNetworkIssues { get; set; } = true;
    }

    public class CleanupResult
    {
        public bool Success { get; set; }
        public long BytesCleaned { get; set; }
        public int FilesRemoved { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class BloatwareResult
    {
        public bool Success { get; set; }
        public int ProgramsRemoved { get; set; }
        public List<string> RemovedPrograms { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }

    public class StartupResult
    {
        public bool Success { get; set; }
        public int ProgramsDisabled { get; set; }
        public int ProgramsEnabled { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class DefragResult
    {
        public bool Success { get; set; }
        public List<string> DefragmentedDrives { get; set; } = new();
        public long TotalBytesProcessed { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class MemoryResult
    {
        public bool Success { get; set; }
        public long MemoryFreed { get; set; }
        public int ProcessesOptimized { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class RegistryResult
    {
        public bool Success { get; set; }
        public int KeysCleaned { get; set; }
        public int ErrorsFixed { get; set; }
        public List<string> Errors { get; set; } = new();
    }

    public class RepairOperation
    {
        public bool Success { get; set; }
        public string Operation { get; set; } = string.Empty;
        public List<string> Details { get; set; } = new();
        public List<string> Errors { get; set; } = new();
    }

    public class UninstallOperation
    {
        public string ProgramName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Method { get; set; } = string.Empty;
        public List<string> Errors { get; set; } = new();
    }

    // System analysis and optimization engines
    public class SystemAnalyzer
    {
        public async Task<SystemInformation> GetSystemInformationAsync()
        {
            var info = new SystemInformation();

            try
            {
                info.OsVersion = Environment.OSVersion.ToString();
                info.Processor = Environment.GetEnvironmentVariable("PROCESSOR_IDENTIFIER") ?? "Unknown";
                info.ProcessorCount = Environment.ProcessorCount;
                info.TotalMemory = GetTotalMemory();
                info.AvailableMemory = GetAvailableMemory();
                info.ComputerName = Environment.MachineName;
                info.Uptime = TimeSpan.FromMilliseconds(Environment.TickCount64);
                info.Disks = GetDiskInformation();

            }
            catch (Exception ex)
            {
                // Log to console as fallback; callers should handle exceptions
                Console.WriteLine($"Error getting system information: {ex.Message}");
            }

            return info;
        }

        public async Task<CpuHealth> AnalyzeCpuHealthAsync()
        {
            var health = new CpuHealth();

            try
            {
                health.CoreCount = Environment.ProcessorCount;

                // Use PerformanceCounter to sample CPU usage across all processors
                using var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total", true);
                // First call initializes counter; wait a short interval for an accurate reading
                _ = cpuCounter.NextValue();
                await Task.Delay(500);
                health.UsagePercent = Math.Round(cpuCounter.NextValue(), 2);

                // Temperature reading requires vendor-specific or WMI access; attempt a best-effort via WMI
                try
                {
                    // Win32_TemperatureProbe is often not populated; attempt MSAcpi_ThermalZoneTemperature
                    using var search = new ManagementObjectSearcher("root\\WMI", "SELECT CurrentTemperature FROM MSAcpi_ThermalZoneTemperature");
                    foreach (ManagementObject obj in search.Get())
                    {
                        var tempKelvin = Convert.ToDouble(obj["CurrentTemperature"]);
                        // Temperature reported in tenths of degrees Kelvin
                        health.Temperature = Math.Round((tempKelvin / 10.0) - 273.15, 1);
                        break;
                    }
                }
                catch
                {
                    // Temperature not available on many systems
                    health.Temperature = 0.0;
                }

                // Calculate health score
                health.HealthScore = CalculateCpuHealthScore(health);

                if (health.UsagePercent > 90) health.Issues.Add("CPU usage is very high");
                if (health.Temperature > 80) health.Issues.Add("CPU temperature is high");

            }
            catch (Exception ex)
            {
                health.Issues.Add($"CPU analysis error: {ex.Message}");
                health.HealthScore = 0.5;
            }

            return health;
        }

        public async Task<MemoryHealth> AnalyzeMemoryHealthAsync()
        {
            var health = new MemoryHealth();

            try
            {
                health.TotalMemory = GetTotalMemory();
                health.UsedMemory = health.TotalMemory - GetAvailableMemory();
                health.UsagePercent = health.TotalMemory > 0 ? (double)health.UsedMemory / health.TotalMemory * 100 : 0;

                health.HealthScore = CalculateMemoryHealthScore(health);

                if (health.UsagePercent > 90) health.Issues.Add("Memory usage is critically high");
                else if (health.UsagePercent > 80) health.Issues.Add("Memory usage is high");

            }
            catch (Exception ex)
            {
                health.Issues.Add($"Memory analysis error: {ex.Message}");
                health.HealthScore = 0.5;
            }

            return health;
        }

        public async Task<DiskHealth> AnalyzeDiskHealthAsync()
        {
            var health = new DiskHealth();

            try
            {
                health.Disks = GetDiskInformation();

                foreach (var disk in health.Disks)
                {
                    var usagePercent = disk.TotalSize > 0 ? (double)(disk.TotalSize - disk.FreeSpace) / disk.TotalSize * 100 : 0;

                    if (usagePercent > 95) health.Issues.Add($"Disk {disk.Name} is nearly full");
                    else if (usagePercent > 85) health.Issues.Add($"Disk {disk.Name} is getting full");
                }

                health.HealthScore = CalculateDiskHealthScore(health);

            }
            catch (Exception ex)
            {
                health.Issues.Add($"Disk analysis error: {ex.Message}");
                health.HealthScore = 0.5;
            }

            return health;
        }

        public async Task<PerformanceMetrics> GetPerformanceMetricsAsync()
        {
            var metrics = new PerformanceMetrics();

            try
            {
                // CPU usage via PerformanceCounter
                using var cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total", true);
                _ = cpuCounter.NextValue();
                await Task.Delay(500);
                metrics.CpuUsage = Math.Round(cpuCounter.NextValue(), 2);

                // Memory usage via GlobalMemoryStatusEx
                var total = GetTotalMemory();
                var available = GetAvailableMemory();
                metrics.MemoryUsagePercent = total > 0 ? Math.Round(((double)(total - available) / total) * 100.0, 2) : 0.0;

                // Disk usage - aggregate system drive usage
                var systemDrive = Path.GetPathRoot(Environment.SystemDirectory);
                if (!string.IsNullOrEmpty(systemDrive))
                {
                    var driveInfo = new DriveInfo(systemDrive);
                    if (driveInfo.IsReady)
                    {
                        metrics.DiskUsagePercent = Math.Round((double)(driveInfo.TotalSize - driveInfo.AvailableFreeSpace) / driveInfo.TotalSize * 100, 2);
                    }
                }

                // Process and thread counts
                var processes = Process.GetProcesses();
                metrics.ProcessCount = processes.Length;
                metrics.ThreadCount = processes.Sum(p => p.Threads.Count);

                metrics.Timestamp = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting performance metrics: {ex.Message}");
            }

            return metrics;
        }

        private long GetTotalMemory()
        {
            var memStatus = new MEMORYSTATUSEX();
            if (GlobalMemoryStatusEx(memStatus))
            {
                return (long)memStatus.ullTotalPhys;
            }

            // Fallback to Environment.WorkingSet as last resort
            return GC.GetGCMemoryInfo().TotalAvailableMemoryBytes;
        }

        private long GetAvailableMemory()
        {
            var memStatus = new MEMORYSTATUSEX();
            if (GlobalMemoryStatusEx(memStatus))
            {
                return (long)memStatus.ullAvailPhys;
            }

            // Fallback
            return (long)(GC.GetGCMemoryInfo().TotalAvailableMemoryBytes / 2);
        }

        private List<DiskInfo> GetDiskInformation()
        {
            var disks = new List<DiskInfo>();

            foreach (var drive in DriveInfo.GetDrives())
            {
                try
                {
                    if (drive.IsReady)
                    {
                        disks.Add(new DiskInfo
                        {
                            Name = drive.Name,
                            TotalSize = drive.TotalSize,
                            FreeSpace = drive.AvailableFreeSpace,
                            FileSystem = drive.DriveFormat,
                            IsSystemDisk = drive.Name.Equals(Path.GetPathRoot(Environment.SystemDirectory), StringComparison.OrdinalIgnoreCase)
                        });
                    }
                }
                catch
                {
                    // ignore drives we cannot access
                }
            }

            return disks;
        }

        private double CalculateCpuHealthScore(CpuHealth health)
        {
            var score = 1.0;

            if (health.UsagePercent > 90) score -= 0.4;
            else if (health.UsagePercent > 70) score -= 0.2;

            if (health.Temperature > 80) score -= 0.3;
            else if (health.Temperature > 60) score -= 0.1;

            return Math.Max(0.0, Math.Min(1.0, score));
        }

        private double CalculateMemoryHealthScore(MemoryHealth health)
        {
            var score = 1.0;

            if (health.UsagePercent > 95) score -= 0.5;
            else if (health.UsagePercent > 85) score -= 0.3;
            else if (health.UsagePercent > 75) score -= 0.1;

            return Math.Max(0.0, Math.Min(1.0, score));
        }

        private double CalculateDiskHealthScore(DiskHealth health)
        {
            var score = 1.0;

            foreach (var disk in health.Disks)
            {
                var usagePercent = disk.TotalSize > 0 ? (double)(disk.TotalSize - disk.FreeSpace) / disk.TotalSize * 100 : 0;

                if (usagePercent > 95) score -= 0.3;
                else if (usagePercent > 85) score -= 0.1;
            }

            return Math.Max(0.0, Math.Min(1.0, score));
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private class MEMORYSTATUSEX
        {
            public uint dwLength = (uint)Marshal.SizeOf(typeof(MEMORYSTATUSEX));
            public uint dwMemoryLoad;
            public ulong ullTotalPhys;
            public ulong ullAvailPhys;
            public ulong ullTotalPageFile;
            public ulong ullAvailPageFile;
            public ulong ullTotalVirtual;
            public ulong ullAvailVirtual;
            public ulong ullAvailExtendedVirtual;
        }

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool GlobalMemoryStatusEx([In, Out] MEMORYSTATUSEX lpBuffer);
    }

    public class PerformanceOptimizer
    {
        public async Task<CleanupResult> CleanTemporaryFilesAsync(CancellationToken cancellationToken = default)
        {
            var result = new CleanupResult();

            try
            {
                var tempPaths = new[]
                {
                    Path.GetTempPath(),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Temp"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.InternetCache))
                };

                foreach (var path in tempPaths)
                {
                    if (string.IsNullOrWhiteSpace(path)) continue;
                    if (!Directory.Exists(path)) continue;

                    var dirStack = new Stack<string>();
                    dirStack.Push(path);

                    while (dirStack.Count > 0)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        var dir = dirStack.Pop();
                        string[] subDirs = Array.Empty<string>();
                        try
                        {
                            subDirs = Directory.GetDirectories(dir);
                        }
                        catch { /* ignore inaccessible directories */ }

                        foreach (var sd in subDirs)
                        {
                            dirStack.Push(sd);
                        }

                        string[] files = Array.Empty<string>();
                        try
                        {
                            files = Directory.GetFiles(dir);
                        }
                        catch { /* ignore */ }

                        foreach (var file in files)
                        {
                            try
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                if (!IsFileSafeToDelete(file)) continue;
                                var fi = new FileInfo(file);
                                var len = fi.Length;
                                fi.Delete();
                                result.FilesRemoved++;
                                result.BytesCleaned += len;
                            }
                            catch
                            {
                                // skip files that are locked or cause errors
                            }
                        }
                    }
                }

                result.Success = true;
            }
            catch (OperationCanceledException)
            {
                result.Success = false;
                result.Errors.Add("Operation cancelled");
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<BloatwareResult> RemoveBloatwareAsync()
        {
            var result = new BloatwareResult();

            try
            {
                // Detect common bloatware by scanning installed programs
                var candidates = new List<string>(StringComparer.OrdinalIgnoreCase)
                {
                    "Ask Toolbar", "McAfee", "Mcafee", "Trial", "Offer", "Bing Bar", "Java 8 Update", "Adobe" // example tokens
                };

                var installed = GetInstalledPrograms();

                foreach (var p in installed)
                {
                    if (candidates.Any(token => p.IndexOf(token, StringComparison.OrdinalIgnoreCase) >= 0))
                    {
                        result.RemovedPrograms.Add(p);
                    }
                }

                result.ProgramsRemoved = result.RemovedPrograms.Count;
                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<StartupResult> OptimizeStartupProgramsAsync()
        {
            var result = new StartupResult();

            try
            {
                // Read common startup locations and return discovered entries (read-only)
                var entries = new List<string>();

                // Registry Run keys
                var runKeys = new[]
                {
                    Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", false),
                    Registry.LocalMachine.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", false),
                    Registry.LocalMachine.OpenSubKey("SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Run", false)
                };

                foreach (var key in runKeys)
                {
                    if (key == null) continue;
                    foreach (var name in key.GetValueNames())
                    {
                        try
                        {
                            var val = key.GetValue(name)?.ToString() ?? string.Empty;
                            entries.Add($"{name} => {val}");
                        }
                        catch { }
                    }
                }

                result.Success = true;
                result.ProgramsDisabled = 0; // No automatic disabling performed
                result.ProgramsEnabled = entries.Count;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<DefragResult> DefragmentDrivesAsync()
        {
            var result = new DefragResult();

            try
            {
                var drives = DriveInfo.GetDrives().Where(d => d.DriveType == DriveType.Fixed && d.IsReady).ToList();

                foreach (var drive in drives)
                {
                    try
                    {
                        using var psi = new ProcessStartInfo
                        {
                            FileName = "defrag",
                            Arguments = $"{drive.Name.TrimEnd('\\')} /U",
                            CreateNoWindow = true,
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true
                        };

                        using var proc = Process.Start(psi);
                        if (proc != null)
                        {
                            var output = await proc.StandardOutput.ReadToEndAsync();
                            var error = await proc.StandardError.ReadToEndAsync();
                            await proc.WaitForExitAsync();

                            if (proc.ExitCode == 0)
                            {
                                result.DefragmentedDrives.Add(drive.Name);
                            }
                            else
                            {
                                result.Errors.Add($"Defrag {drive.Name} failed: {error}");
                            }

                            result.TotalBytesProcessed += drive.TotalSize - drive.AvailableFreeSpace;
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Errors.Add(ex.Message);
                    }
                }

                result.Success = result.DefragmentedDrives.Count > 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<MemoryResult> OptimizeMemoryAsync()
        {
            var result = new MemoryResult();

            try
            {
                var before = GC.GetTotalMemory(false);

                // Force garbage collection and reduce working set
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                EmptyWorkingSet(Process.GetCurrentProcess().Handle);

                var after = GC.GetTotalMemory(false);
                result.Success = true;
                result.MemoryFreed = Math.Max(0, before - after);
                result.ProcessesOptimized = 1;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<RegistryResult> CleanRegistryAsync()
        {
            var result = new RegistryResult();

            try
            {
                // Implement a non-destructive scan for obvious invalid uninstall entries under Uninstall keys
                var uninstallKeys = new[]
                {
                    Registry.LocalMachine.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall", false),
                    Registry.LocalMachine.OpenSubKey("SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall", false),
                    Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall", false)
                };

                int found = 0;
                foreach (var key in uninstallKeys)
                {
                    if (key == null) continue;
                    foreach (var sub in key.GetSubKeyNames())
                    {
                        try
                        {
                            using var sk = key.OpenSubKey(sub);
                            var displayName = sk?.GetValue("DisplayName")?.ToString() ?? string.Empty;
                            if (string.IsNullOrEmpty(displayName))
                            {
                                found++;
                            }
                        }
                        catch { }
                    }
                }

                result.Success = true;
                result.KeysCleaned = 0; // No automatic deletion performed
                result.ErrorsFixed = 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<MemoryResult> OptimizeCpuAsync()
        {
            var result = new MemoryResult();

            try
            {
                // On Windows, CPU optimization can mean setting process priorities for non-critical processes
                // For safety, we'll not change other process priorities; instead report candidate processes
                result.Success = true;
                result.ProcessesOptimized = 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<DefragResult> OptimizeDiskAsync()
        {
            var result = new DefragResult();

            try
            {
                // Reuse DefragmentDrivesAsync for optimizing disks
                var defrag = await DefragmentDrivesAsync();
                result = defrag;
                result.Success = defrag.Success;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<NetworkResult> OptimizeNetworkAsync()
        {
            var result = new NetworkResult();

            try
            {
                // Example: flush DNS and reset some TCP settings
                var psi = new ProcessStartInfo("ipconfig", "/flushdns") { CreateNoWindow = true, UseShellExecute = false };
                using var p = Process.Start(psi);
                if (p != null) await p.WaitForExitAsync();

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<UninstallOperation> UninstallProgramAsync(string programName)
        {
            var result = new UninstallOperation { ProgramName = programName };

            try
            {
                // Look up uninstall information in registry
                var uninstallEntries = new[]
                {
                    "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall",
                    "SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall"
                };

                string? uninstallCommand = null;
                foreach (var hivePath in uninstallEntries)
                {
                    using var baseKey = Registry.LocalMachine.OpenSubKey(hivePath);
                    if (baseKey == null) continue;
                    foreach (var subKeyName in baseKey.GetSubKeyNames())
                    {
                        using var subKey = baseKey.OpenSubKey(subKeyName);
                        var displayName = subKey?.GetValue("DisplayName")?.ToString();
                        if (string.IsNullOrEmpty(displayName)) continue;
                        if (displayName.IndexOf(programName, StringComparison.OrdinalIgnoreCase) >= 0)
                        {
                            uninstallCommand = subKey.GetValue("UninstallString")?.ToString();
                            break;
                        }
                    }
                    if (uninstallCommand != null) break;
                }

                if (string.IsNullOrEmpty(uninstallCommand))
                {
                    result.Success = false;
                    result.Errors.Add("Uninstall command not found");
                    return result;
                }

                // Try to execute uninstall command (best-effort). Many uninstallers support silent flags (/S, /quiet, /qn)
                var (exe, args) = SplitCommand(uninstallCommand);

                using var psi = new ProcessStartInfo
                {
                    FileName = exe,
                    Arguments = args,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var proc = Process.Start(psi);
                if (proc != null)
                {
                    await proc.WaitForExitAsync();
                    result.Success = proc.ExitCode == 0;
                    result.Method = "RegistryUninstall";
                    if (!result.Success) result.Errors.Add($"ExitCode={proc.ExitCode}");
                }
                else
                {
                    result.Success = false;
                    result.Errors.Add("Failed to start uninstall process");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<CleanupResult> CleanUninstallLeftoversAsync(List<string> programs)
        {
            var result = new CleanupResult();

            try
            {
                // Search Program Files directories for folders matching program names and attempt to remove them
                var programFiles = new[] { Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86) };

                foreach (var prog in programs)
                {
                    foreach (var root in programFiles)
                    {
                        if (string.IsNullOrEmpty(root) || !Directory.Exists(root)) continue;
                        var candidates = Directory.EnumerateDirectories(root)
                            .Where(d => Path.GetFileName(d).IndexOf(prog, StringComparison.OrdinalIgnoreCase) >= 0);

                        foreach (var cand in candidates)
                        {
                            try
                            {
                                Directory.Delete(cand, true);
                                result.FilesRemoved++;
                            }
                            catch (Exception ex)
                            {
                                result.Errors.Add(ex.Message);
                            }
                        }
                    }
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        private bool IsFileSafeToDelete(string filePath)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);

                // Don't delete files that are too new (might be in use)
                if (fileInfo.LastWriteTime > DateTime.Now.AddHours(-1))
                    return false;

                // Don't delete system files
                var systemFiles = new[] { "desktop.ini", "thumbs.db" };
                if (systemFiles.Contains(fileInfo.Name.ToLowerInvariant()))
                    return false;

                // Only delete certain file types
                var safeExtensions = new[] { ".tmp", ".temp", ".log", ".cache", ".bak", ".old" };
                return safeExtensions.Contains(fileInfo.Extension.ToLowerInvariant());

            }
            catch
            {
                return false;
            }
        }

        private IEnumerable<string> GetInstalledPrograms()
        {
            var results = new List<string>();
            var uninstallLocations = new[]
            {
                (RegistryKey)Registry.LocalMachine.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall"),
                Registry.LocalMachine.OpenSubKey("SOFTWARE\\WOW6432Node\\Microsoft\\Windows\\CurrentVersion\\Uninstall"),
                Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall")
            };

            foreach (var key in uninstallLocations)
            {
                if (key == null) continue;
                foreach (var sub in key.GetSubKeyNames())
                {
                    try
                    {
                        using var sk = key.OpenSubKey(sub);
                        var name = sk?.GetValue("DisplayName")?.ToString();
                        if (!string.IsNullOrEmpty(name)) results.Add(name);
                    }
                    catch { }
                }
            }

            return results.Distinct(StringComparer.OrdinalIgnoreCase);
        }

        private static (string exe, string args) SplitCommand(string command)
        {
            command = command.Trim();
            if (command.StartsWith("\""))
            {
                var end = command.IndexOf('\"', 1);
                if (end > 1)
                {
                    var exe = command.Substring(1, end - 1);
                    var args = command.Length > end + 1 ? command.Substring(end + 1).Trim() : string.Empty;
                    return (exe, args);
                }
            }

            var parts = command.Split(' ', 2, StringSplitOptions.RemoveEmptyEntries);
            var exe2 = parts[0];
            var args2 = parts.Length > 1 ? parts[1] : string.Empty;
            return (exe2, args2);
        }

        [DllImport("psapi.dll")]
        private static extern int EmptyWorkingSet(IntPtr hwProc);
    }

    public class RepairEngine
    {
        public async Task<RepairOperation> RepairSystemFilesAsync()
        {
            var result = new RepairOperation { Operation = "System File Repair" };

            try
            {
                // Run SFC /scannow - requires elevation to repair
                var psi = new ProcessStartInfo("sfc", "/scannow") { CreateNoWindow = true, UseShellExecute = false, RedirectStandardOutput = true, RedirectStandardError = true };
                using var proc = Process.Start(psi);
                if (proc != null)
                {
                    var output = await proc.StandardOutput.ReadToEndAsync();
                    var error = await proc.StandardError.ReadToEndAsync();
                    await proc.WaitForExitAsync();
                    result.Success = proc.ExitCode == 0;
                    result.Details.Add(output);
                    if (!string.IsNullOrEmpty(error)) result.Errors.Add(error);
                }
                else
                {
                    result.Success = false;
                    result.Errors.Add("Failed to start sfc process");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> FixWindowsCorruptionAsync()
        {
            var result = new RepairOperation { Operation = "Windows Corruption Fix" };

            try
            {
                // Run DISM to check and restore health
                var psi = new ProcessStartInfo("dism", "/Online /Cleanup-Image /RestoreHealth") { CreateNoWindow = true, UseShellExecute = false, RedirectStandardOutput = true, RedirectStandardError = true };
                using var proc = Process.Start(psi);
                if (proc != null)
                {
                    var output = await proc.StandardOutput.ReadToEndAsync();
                    var error = await proc.StandardError.ReadToEndAsync();
                    await proc.WaitForExitAsync();
                    result.Success = proc.ExitCode == 0;
                    result.Details.Add(output);
                    if (!string.IsNullOrEmpty(error)) result.Errors.Add(error);
                }
                else
                {
                    result.Success = false;
                    result.Errors.Add("Failed to start DISM process");
                }
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> RepairBootIssuesAsync()
        {
            var result = new RepairOperation { Operation = "Boot Repair" };

            try
            {
                // Running bootrec requires boot environment; attempt BootRec /fixmbr and /fixboot
                var commands = new[] { 
                    ("bootrec", "/fixmbr"),
                    ("bootrec", "/fixboot")
                };

                foreach (var (cmd, args) in commands)
                {
                    var psi = new ProcessStartInfo(cmd, args) { CreateNoWindow = true, UseShellExecute = false, RedirectStandardOutput = true, RedirectStandardError = true };
                    using var proc = Process.Start(psi);
                    if (proc != null)
                    {
                        var output = await proc.StandardOutput.ReadToEndAsync();
                        var error = await proc.StandardError.ReadToEndAsync();
                        await proc.WaitForExitAsync();
                        result.Details.Add(output);
                        if (!string.IsNullOrEmpty(error)) result.Errors.Add(error);
                    }
                }

                result.Success = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> RepairDriversAsync()
        {
            var result = new RepairOperation { Operation = "Driver Repair" };

            try
            {
                // Use pnputil to enumerate and attempt driver reprovision where feasible
                var psi = new ProcessStartInfo("pnputil", "/enum-drivers") { CreateNoWindow = true, UseShellExecute = false, RedirectStandardOutput = true, RedirectStandardError = true };
                using var proc = Process.Start(psi);
                if (proc != null)
                {
                    var output = await proc.StandardOutput.ReadToEndAsync();
                    await proc.WaitForExitAsync();
                    result.Details.Add(output);
                }

                result.Success = true;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> RepairNetworkAsync()
        {
            var result = new RepairOperation { Operation = "Network Repair" };

            try
            {
                // Reset common network stacks
                var cmds = new[] { ("netsh", "int ip reset"), ("netsh", "winsock reset") };
                foreach (var (cmd, args) in cmds)
                {
                    var psi = new ProcessStartInfo(cmd, args) { CreateNoWindow = true, UseShellExecute = false, RedirectStandardOutput = true, RedirectStandardError = true };
                    using var proc = Process.Start(psi);
                    if (proc != null)
                    {
                        var output = await proc.StandardOutput.ReadToEndAsync();
                        var error = await proc.StandardError.ReadToEndAsync();
                        await proc.WaitForExitAsync();
                        result.Details.Add(output);
                        if (!string.IsNullOrEmpty(error)) result.Errors.Add(error);
                    }
                }

                result.Success = result.Errors.Count == 0;
            }
            catch (Exception ex)
            {
                result.Errors.Add(ex.Message);
                result.Success = false;
            }

            return result;
        }
    }

    public class NetworkResult
    {
        public bool Success { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}
