{"format": 1, "restore": {"D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\PcFutureShield.UI.csproj": {}}, "projects": {"D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj", "projectName": "PcFutureShield.Common", "projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Data.Sqlite": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.ML": {"target": "Package", "version": "[3.0.1, )"}, "System.Diagnostics.PerformanceCounter": {"target": "Package", "version": "[8.0.1, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\PcFutureShield.Engine.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\PcFutureShield.Engine.csproj", "projectName": "PcFutureShield.Engine", "projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\PcFutureShield.Engine.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj": {"projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Data.Sqlite": {"target": "Package", "version": "[8.0.8, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.8, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.8, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\PcFutureShield.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\PcFutureShield.UI.csproj", "projectName": "PcFutureShield.UI", "projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\PcFutureShield.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj": {"projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\PcFutureShield.Common.csproj"}, "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\PcFutureShield.Engine.csproj": {"projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\PcFutureShield.Engine.csproj"}}}}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}