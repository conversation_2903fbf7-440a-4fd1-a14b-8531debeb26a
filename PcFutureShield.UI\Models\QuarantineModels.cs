using System;
using System.ComponentModel;

namespace PcFutureShield.UI.ViewModels
{
    public class QuarantinedItem : INotifyPropertyChanged
    {
        private string _itemId = string.Empty;
        private string _fileName = string.Empty;
        private string _originalPath = string.Empty;
        private DateTime _quarantineDate;
        private string _threatType = string.Empty;
        private string _fileSize = string.Empty;
        private string _riskLevel = string.Empty;

        public string ItemId
        {
            get => _itemId;
            set
            {
                if (_itemId != value)
                {
                    _itemId = value;
                    OnPropertyChanged(nameof(ItemId));
                }
            }
        }

        public string Id
        {
            get => _itemId;
            set
            {
                if (_itemId != value)
                {
                    _itemId = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        private string _quarantinePath = string.Empty;
        public string QuarantinePath
        {
            get => _quarantinePath;
            set
            {
                if (_quarantinePath != value)
                {
                    _quarantinePath = value;
                    OnPropertyChanged(nameof(QuarantinePath));
                }
            }
        }

        public string ThreatName
        {
            get => _threatType;
            set
            {
                if (_threatType != value)
                {
                    _threatType = value;
                    OnPropertyChanged(nameof(ThreatName));
                }
            }
        }

        public DateTime DetectionDate
        {
            get => _quarantineDate;
            set
            {
                if (_quarantineDate != value)
                {
                    _quarantineDate = value;
                    OnPropertyChanged(nameof(DetectionDate));
                }
            }
        }

        public string FileName
        {
            get => _fileName;
            set
            {
                if (_fileName != value)
                {
                    _fileName = value;
                    OnPropertyChanged(nameof(FileName));
                }
            }
        }

        public string OriginalPath
        {
            get => _originalPath;
            set
            {
                if (_originalPath != value)
                {
                    _originalPath = value;
                    OnPropertyChanged(nameof(OriginalPath));
                }
            }
        }

        public DateTime QuarantineDate
        {
            get => _quarantineDate;
            set
            {
                if (_quarantineDate != value)
                {
                    _quarantineDate = value;
                    OnPropertyChanged(nameof(QuarantineDate));
                }
            }
        }

        public string ThreatType
        {
            get => _threatType;
            set
            {
                if (_threatType != value)
                {
                    _threatType = value;
                    OnPropertyChanged(nameof(ThreatType));
                }
            }
        }

        public string FileSize
        {
            get => _fileSize;
            set
            {
                if (_fileSize != value)
                {
                    _fileSize = value;
                    OnPropertyChanged(nameof(FileSize));
                }
            }
        }

        public string RiskLevel
        {
            get => _riskLevel;
            set
            {
                if (_riskLevel != value)
                {
                    _riskLevel = value;
                    OnPropertyChanged(nameof(RiskLevel));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}
