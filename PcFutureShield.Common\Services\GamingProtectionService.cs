#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Text;
using System.Security.Cryptography;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced gaming protection service for detecting fraud, scams, and unsafe gaming environments
    /// </summary>
    public class GamingProtectionService
    {
        private readonly HttpClient _httpClient;
        private readonly string _gamingDbPath;
        private readonly Dictionary<string, GameProfile> _gameProfiles;
        private readonly FraudDetectionEngine _fraudEngine;
        private readonly CasinoAnalyzer _casinoAnalyzer;
        private readonly Dictionary<string, CasinoProfile> _casinoProfiles;

        public GamingProtectionService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(15);

            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var gamingPath = Path.Combine(appData, "PcFutureShield", "Gaming");
            Directory.CreateDirectory(gamingPath);
            _gamingDbPath = Path.Combine(gamingPath, "gaming_db.json");

            _gameProfiles = LoadGameProfiles();
            _casinoProfiles = LoadCasinoProfiles();
            _fraudEngine = new FraudDetectionEngine();
            _casinoAnalyzer = new CasinoAnalyzer();
        }

        public async Task<GamingAnalysisResult> AnalyzeGameAsync(string gameName, string gamePath, Process? gameProcess = null)
        {
            var result = new GamingAnalysisResult
            {
                GameName = gameName,
                GamePath = gamePath,
                AnalysisTime = DateTime.UtcNow
            };

            try
            {
                // Get or create game profile
                var gameProfile = GetOrCreateGameProfile(gameName, gamePath);

                // Analyze game executable
                result.ExecutableAnalysis = await AnalyzeGameExecutableAsync(gamePath);

                // Check for known game modifications
                result.ModificationCheck = await CheckGameModificationsAsync(gamePath, gameProfile);

                // Analyze network traffic if process is running
                if (gameProcess != null)
                {
                    result.GameNetworkAnalysis = await AnalyzeGameNetworkAsync(gameProcess);
                }

                // Check for gambling elements
                result.GamblingDetection = await DetectGamblingElementsAsync(gamePath, gameName);

                // Overall risk assessment
                result.OverallRisk = CalculateOverallGameRisk(result);
                result.Recommendations = GenerateGameRecommendations(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.OverallRisk = RiskLevel.High; // Default to high risk on error
            }

            return result;
        }

        public async Task<CasinoAnalysisResult> AnalyzeCasinoAsync(string casinoUrl, string casinoName)
        {
            var result = new CasinoAnalysisResult
            {
                CasinoUrl = casinoUrl,
                CasinoName = casinoName,
                AnalysisTime = DateTime.UtcNow
            };

            try
            {
                // Get or create casino profile
                var casinoProfile = GetOrCreateCasinoProfile(casinoUrl, casinoName);

                // Analyze casino website
                result.WebsiteAnalysis = await AnalyzeCasinoWebsiteAsync(casinoUrl);

                // Check licensing and regulation
                result.LicensingCheck = await CheckCasinoLicensingAsync(casinoUrl);

                // Analyze game fairness
                result.FairnessAnalysis = await AnalyzeGameFairnessAsync(casinoUrl, casinoProfile);

                // Check for scam indicators
                result.ScamDetection = await DetectCasinoScamsAsync(casinoUrl, casinoProfile);

                // Financial risk assessment
                result.FinancialRisk = await AssessFinancialRiskAsync(casinoProfile);

                // Overall risk assessment
                result.OverallRisk = CalculateOverallCasinoRisk(result);
                result.Recommendations = GenerateCasinoRecommendations(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.OverallRisk = RiskLevel.Critical; // Default to critical risk on error
            }

            return result;
        }

        public async Task<FraudAlertResult> CheckForFraudAsync(string transactionData, decimal amount, string gameOrCasino)
        {
            return await _fraudEngine.AnalyzeTransactionAsync(transactionData, amount, gameOrCasino);
        }

        public void ReportRiggedGame(string gameName, string evidence)
        {
            var gameProfile = GetOrCreateGameProfile(gameName, "");
            gameProfile.RigReports++;
            gameProfile.LastRigReport = DateTime.UtcNow;
            gameProfile.RigEvidence.Add(new RigEvidence
            {
                ReportDate = DateTime.UtcNow,
                Evidence = evidence,
                ReportedBy = Environment.UserName
            });
            SaveGameProfiles();
        }

        public void ReportCasinoScam(string casinoUrl, string scamType, string evidence)
        {
            var casinoProfile = GetOrCreateCasinoProfile(casinoUrl, "");
            casinoProfile.ScamReports++;
            casinoProfile.LastScamReport = DateTime.UtcNow;
            casinoProfile.ScamEvidence.Add(new ScamEvidence
            {
                ReportDate = DateTime.UtcNow,
                ScamType = scamType,
                Evidence = evidence,
                ReportedBy = Environment.UserName
            });
            SaveCasinoProfiles();
        }

        private async Task<ExecutableAnalysis> AnalyzeGameExecutableAsync(string gamePath)
        {
            var analysis = new ExecutableAnalysis();

            try
            {
                if (!File.Exists(gamePath))
                {
                    analysis.IsValidExecutable = false;
                    analysis.RiskFactors.Add("File does not exist");
                    return analysis;
                }

                // Check file signature
                analysis.IsValidExecutable = HashingService.LooksLikePortableExecutable(gamePath);

                // Calculate hash for integrity checking
                analysis.FileHash = HashingService.ComputeSHA256(gamePath);

                // Check file size (unusually small executables might be suspicious)
                var fileInfo = new FileInfo(gamePath);
                analysis.FileSize = fileInfo.Length;

                if (fileInfo.Length < 1024 * 10) // Less than 10KB
                    analysis.RiskFactors.Add("Unusually small executable");

                // Check for known malware patterns
                analysis.MalwareIndicators = await CheckForMalwarePatternsAsync(gamePath);

            }
            catch (Exception ex)
            {
                analysis.RiskFactors.Add($"Analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<ModificationCheck> CheckGameModificationsAsync(string gamePath, GameProfile profile)
        {
            var check = new ModificationCheck();

            try
            {
                var currentHash = HashingService.ComputeSHA256(gamePath);

                if (!string.IsNullOrEmpty(profile.OriginalHash))
                {
                    check.IsModified = !currentHash.Equals(profile.OriginalHash, StringComparison.OrdinalIgnoreCase);
                    if (check.IsModified)
                    {
                        check.ModificationType = "File hash changed";
                        check.RiskLevel = RiskLevel.Medium;
                    }
                }
                else
                {
                    // First time seeing this game, store original hash
                    profile.OriginalHash = currentHash;
                    SaveGameProfiles();
                }

                // Check for known cheat modifications
                check.KnownCheats = await CheckForKnownCheatsAsync(gamePath, profile.GameName);

            }
            catch (Exception ex)
            {
                check.Error = ex.Message;
            }

            return check;
        }

        private async Task<GameNetworkAnalysis> AnalyzeGameNetworkAsync(Process gameProcess)
        {
            // Lightweight synchronous checks; yield once so callers can await
            await Task.Yield();
            var analysis = new GameNetworkAnalysis();

            try
            {
                // This would require network monitoring capabilities
                // For now, we'll do basic analysis
                analysis.HasNetworkAccess = true; // Assume online game

                // Check for suspicious connections
                analysis.SuspiciousConnections = new List<string>();

                // Perform basic network analysis
                if (gameProcess != null && !gameProcess.HasExited)
                {
                    // Assume online if process is running
                    analysis.HasNetworkAccess = true;
                }
                else
                {
                    analysis.HasNetworkAccess = false;
                }
            }
            catch (Exception ex)
            {
                analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<GamblingDetection> DetectGamblingElementsAsync(string gamePath, string gameName)
        {
            await Task.Yield();
            var detection = new GamblingDetection();

            try
            {
                // Check game name for gambling keywords
                var gamblingKeywords = new[] { "casino", "poker", "blackjack", "roulette", "slots", "bet", "gamble" };

                foreach (var keyword in gamblingKeywords)
                {
                    if (gameName.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                    {
                        detection.ContainsGambling = true;
                        detection.GamblingType = keyword;
                        break;
                    }
                }

                // Check for loot box mechanics (gambling-like elements)
                detection.ContainsLootBoxes = CheckForLootBoxes(gameName);

                if (detection.ContainsGambling || detection.ContainsLootBoxes)
                {
                    detection.RiskLevel = RiskLevel.Medium;
                    detection.Warnings.Add("Game contains gambling or gambling-like elements");
                }
            }
            catch (Exception ex)
            {
                detection.Error = ex.Message;
            }

            return detection;
        }

        private async Task<WebsiteAnalysis> AnalyzeCasinoWebsiteAsync(string casinoUrl)
        {
            var analysis = new WebsiteAnalysis();

            try
            {
                var response = await _httpClient.GetAsync(casinoUrl);
                analysis.IsAccessible = response.IsSuccessStatusCode;

                if (analysis.IsAccessible)
                {
                    var content = await response.Content.ReadAsStringAsync();

                    // Check for HTTPS
                    analysis.UsesHttps = casinoUrl.StartsWith("https://");

                    // Check for security indicators
                    analysis.HasSecurityBadges = content.Contains("secure") || content.Contains("SSL");

                    // Check for age verification
                    analysis.HasAgeVerification = content.Contains("18+") || content.Contains("21+");

                    // Check for responsible gambling information
                    analysis.HasResponsibleGambling = content.Contains("responsible") && content.Contains("gamble");
                }

            }
            catch (Exception ex)
            {
                analysis.Error = ex.Message;
                analysis.IsAccessible = false;
            }

            return analysis;
        }

        private async Task<LicensingCheck> CheckCasinoLicensingAsync(string casinoUrl)
        {
            var check = new LicensingCheck();

            try
            {
                // Check for known licensing authorities
                var licensingBodies = new[]
                {
                    "malta gaming authority", "mga", "uk gambling commission", "gibraltar",
                    "curacao", " Kahnawake", " Isle of Man", " Alderney"
                };

                // Perform actual web scraping to check for licensing
                var response = await _httpClient.GetStringAsync(casinoUrl);

                // Check for licensing keywords
                var licensingKeywords = licensingBodies.Concat(new[] { "licensed", "license", "gambling commission" }).ToArray();

                check.IsLicensed = licensingKeywords.Any(keyword => response.Contains(keyword, StringComparison.OrdinalIgnoreCase));

                if (check.IsLicensed)
                {
                    // Try to identify the authority
                    if (response.Contains("mga", StringComparison.OrdinalIgnoreCase) || response.Contains("malta gaming authority", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "Malta Gaming Authority";
                    else if (response.Contains("uk gambling commission", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "UK Gambling Commission";
                    else if (response.Contains("curacao", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "Curacao eGaming";
                    else if (response.Contains("kahnawake", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "Kahnawake Gaming Commission";
                    else if (response.Contains("isle of man", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "Isle of Man Gambling Supervision Commission";
                    else if (response.Contains("alderney", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "Alderney Gambling Control Commission";
                    else if (response.Contains("gibraltar", StringComparison.OrdinalIgnoreCase))
                        check.LicensingAuthority = "Gibraltar Gambling Commissioner";
                    else
                        check.LicensingAuthority = "Licensed";
                }
                else
                {
                    check.LicensingAuthority = "Not Found";
                }

            }
            catch (Exception ex)
            {
                check.Error = ex.Message;
            }

            return check;
        }

        private async Task<FairnessAnalysis> AnalyzeGameFairnessAsync(string casinoUrl, CasinoProfile profile)
        {
            await Task.Yield();
            var analysis = new FairnessAnalysis();

            try
            {
                // Check RTP (Return to Player) if available
                analysis.RTP = profile.AverageRTP;

                // Check for RNG certification
                analysis.HasRNGCertification = profile.HasRNGCertification;

                // Analyze payout patterns from reports
                if (profile.TotalReports > 10)
                {
                    analysis.ComplaintRatio = (double)profile.NegativeReports / profile.TotalReports;
                }
            }
            catch (Exception ex)
            {
                analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<ScamDetection> DetectCasinoScamsAsync(string casinoUrl, CasinoProfile profile)
        {
            await Task.Yield();
            var detection = new ScamDetection();

            try
            {
                // Check for known scam patterns
                detection.KnownScams = profile.ScamReports > 0;

                // Check for red flags
                detection.RedFlags = new List<string>();

                if (profile.ScamReports > 5)
                    detection.RedFlags.Add("Multiple scam reports");

                if (!profile.HasRNGCertification)
                    detection.RedFlags.Add("No RNG certification");

                if (profile.AverageRTP < 90)
                    detection.RedFlags.Add("Low RTP rates");
            }
            catch (Exception ex)
            {
                detection.Error = ex.Message;
            }

            return detection;
        }

        private async Task<FinancialRisk> AssessFinancialRiskAsync(CasinoProfile profile)
        {
            await Task.Yield();
            var risk = new FinancialRisk();

            try
            {
                // Calculate risk based on various factors
                var riskScore = 0.0;

                if (profile.ScamReports > 0) riskScore += 0.3;
                if (!profile.HasRNGCertification) riskScore += 0.2;
                if (profile.AverageRTP < 95) riskScore += 0.2;
                if (profile.ComplaintRatio > 0.1) riskScore += 0.3;

                risk.RiskScore = riskScore;
                risk.RiskLevel = DetermineRiskLevel(riskScore);

                if (riskScore > 0.5)
                {
                    risk.Warnings.Add("High financial risk - potential for significant losses");
                }
            }
            catch (Exception ex)
            {
                risk.Error = ex.Message;
            }

            return risk;
        }

        private async Task<List<string>> CheckForMalwarePatternsAsync(string gamePath)
        {
            await Task.Yield();
            var indicators = new List<string>();

            try
            {
                if (!File.Exists(gamePath)) return indicators;

                // Quick heuristic: check filename and basic strings in file for known patterns
                var fileName = Path.GetFileName(gamePath).ToLowerInvariant();
                var suspiciousNames = new[] { "crack", "keygen", "patch", "trainer", "cheat" };
                if (suspiciousNames.Any(n => fileName.Contains(n)))
                {
                    indicators.Add("Filename suggests possible crack/cheat");
                }

                // Scan file bytes for ASCII keywords (cheap heuristic)
                var content = await File.ReadAllTextAsync(gamePath).ConfigureAwait(false);
                var patterns = new[] { "loader", "inject", "unauthorized", "keygen", "crack" };
                foreach (var p in patterns)
                {
                    if (content.IndexOf(p, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        indicators.Add($"Contains suspicious pattern: {p}");
                    }
                }
            }
            catch
            {
                // ignore IO parse issues
            }

            return indicators;
        }

        private async Task<List<string>> CheckForKnownCheatsAsync(string gamePath, string gameName)
        {
            await Task.Yield();
            var cheats = new List<string>();

            try
            {
                if (!File.Exists(gamePath))
                    return cheats;

                // 1) Name heuristics
                var lowerName = gameName?.ToLowerInvariant() ?? string.Empty;
                var cheatKeywords = new[] { "cheat", "trainer", "unlimited", "godmode", "aimbot", "hack", "injector", "loader" };
                if (cheatKeywords.Any(k => lowerName.Contains(k)))
                    cheats.Add("Game name suggests embedded cheat/trainer");

                // 2) Directory companion files (common trainers/injectors next to exe)
                var dir = Path.GetDirectoryName(gamePath);
                if (!string.IsNullOrEmpty(dir) && Directory.Exists(dir))
                {
                    var files = Directory.GetFiles(dir);
                    foreach (var f in files)
                    {
                        var fn = Path.GetFileName(f).ToLowerInvariant();
                        if (cheatKeywords.Any(k => fn.Contains(k)))
                            cheats.Add($"Found companion file suggesting cheat: {fn}");
                    }
                }

                // 3) Known cheat signatures / database (optional local JSON)
                try
                {
                    var dbDir = Path.GetDirectoryName(_gamingDbPath) ?? dir ?? string.Empty;
                    var cheatDbPath = Path.Combine(dbDir, "cheat_signatures.json");
                    if (File.Exists(cheatDbPath))
                    {
                        var json = await File.ReadAllTextAsync(cheatDbPath).ConfigureAwait(false);
                        using var doc = JsonDocument.Parse(json);
                        var root = doc.RootElement;

                        // Hash list
                        if (root.TryGetProperty("hashes", out var hashes) && hashes.ValueKind == JsonValueKind.Array)
                        {
                            var fileHash = HashingService.ComputeSHA256(gamePath);
                            foreach (var h in hashes.EnumerateArray())
                            {
                                if (h.ValueKind == JsonValueKind.String && string.Equals(h.GetString(), fileHash, StringComparison.OrdinalIgnoreCase))
                                {
                                    cheats.Add($"File hash matches known cheat signature: {fileHash}");
                                    break;
                                }
                            }
                        }

                        // Filename patterns in DB
                        if (root.TryGetProperty("filenames", out var fileNames) && fileNames.ValueKind == JsonValueKind.Array)
                        {
                            var fnLower = Path.GetFileName(gamePath).ToLowerInvariant();
                            foreach (var p in fileNames.EnumerateArray())
                            {
                                if (p.ValueKind == JsonValueKind.String && fnLower.Contains(p.GetString() ?? string.Empty, StringComparison.OrdinalIgnoreCase))
                                {
                                    cheats.Add($"Filename matches known cheat pattern: {p.GetString()}");
                                    break;
                                }
                            }
                        }

                        // Byte patterns (hex strings) to search inside file (first N bytes)
                        if (root.TryGetProperty("bytePatterns", out var bytePatterns) && bytePatterns.ValueKind == JsonValueKind.Array)
                        {
                            var maxRead = 1024 * 1024; // read up to 1MB for pattern scanning
                            var fileLength = new FileInfo(gamePath).Length;
                            var toRead = (int)Math.Min(maxRead, fileLength);
                            var sample = await File.ReadAllBytesAsync(gamePath).ConfigureAwait(false);
                            foreach (var bp in bytePatterns.EnumerateArray())
                            {
                                if (bp.ValueKind != JsonValueKind.String) continue;
                                var hex = bp.GetString() ?? string.Empty;
                                try
                                {
                                    var pattern = Convert.FromHexString(hex);
                                    if (IndexOfSequence(sample, pattern) >= 0)
                                        cheats.Add($"Binary pattern match found for signature: {hex[..Math.Min(16, hex.Length)]}...");
                                }
                                catch
                                {
                                    // ignore malformed patterns
                                }
                            }
                        }
                    }
                }
                catch
                {
                    // non-fatal DB parse errors
                }

                // 4) Quick content scan for suspicious API names (indicative of injectors/trainers)
                try
                {
                    var suspiciousApis = new[] { "WriteProcessMemory", "CreateRemoteThread", "VirtualAllocEx", "SetWindowsHookEx", "GetAsyncKeyState", "GetProcAddress", "LoadLibrary", "OpenProcess" };
                    var sampleSize = (int)Math.Min(64 * 1024, new FileInfo(gamePath).Length);
                    var buffer = await File.ReadAllBytesAsync(gamePath).ConfigureAwait(false);
                    var sampleText = Encoding.ASCII.GetString(buffer, 0, Math.Min(buffer.Length, sampleSize));
                    foreach (var api in suspiciousApis)
                    {
                        if (sampleText.IndexOf(api, StringComparison.OrdinalIgnoreCase) >= 0)
                            cheats.Add($"Suspicious native API string found: {api}");
                    }
                }
                catch
                {
                    // ignore IO errors
                }

                // 5) Entropy heuristic to detect packing/obfuscation (high entropy may indicate a packed trainer)
                try
                {
                    var entropy = HashingService.ComputeFileEntropy(gamePath);
                    if (entropy > 7.5)
                        cheats.Add($"High entropy ({entropy:F2}) suggests packing/obfuscation often used by cheats/packs");
                }
                catch
                {
                }
            }
            catch
            {
                // swallow top-level IO/parsing errors to keep behavior-preserving
            }

            // Deduplicate and return
            return cheats.Distinct().ToList();
        }

        private bool CheckForLootBoxes(string gameName)
        {
            var lootBoxKeywords = new[] { "loot", "box", "crate", "battle pass", "microtransaction" };
            return lootBoxKeywords.Any(keyword => gameName.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private RiskLevel CalculateOverallGameRisk(GamingAnalysisResult result)
        {
            var riskScore = 0.0;

            if (result.ExecutableAnalysis?.RiskFactors.Count > 0) riskScore += 0.3;
            if (result.ModificationCheck?.IsModified == true) riskScore += 0.4;
            if (result.GamblingDetection?.ContainsGambling == true) riskScore += 0.3;

            return DetermineRiskLevel(riskScore);
        }

        private RiskLevel CalculateOverallCasinoRisk(CasinoAnalysisResult result)
        {
            var riskScore = 0.0;

            if (result.ScamDetection?.KnownScams == true) riskScore += 0.5;
            if (result.FinancialRisk?.RiskScore > 0.5) riskScore += 0.4;
            if (!result.LicensingCheck?.IsLicensed == true) riskScore += 0.3;

            return DetermineRiskLevel(riskScore);
        }

        private List<string> GenerateGameRecommendations(GamingAnalysisResult result)
        {
            var recommendations = new List<string>();

            if (result.OverallRisk >= RiskLevel.High)
                recommendations.Add("Avoid playing this game - high risk detected");

            if (result.GamblingDetection?.ContainsGambling == true)
                recommendations.Add("This game contains gambling elements - set spending limits");

            if (result.ModificationCheck?.IsModified == true)
                recommendations.Add("Game has been modified - verify it's from a legitimate source");

            return recommendations;
        }

        private List<string> GenerateCasinoRecommendations(CasinoAnalysisResult result)
        {
            var recommendations = new List<string>();

            if (result.OverallRisk >= RiskLevel.Critical)
                recommendations.Add("DO NOT PLAY - This casino appears to be a scam");

            if (result.FinancialRisk?.RiskScore > 0.7)
                recommendations.Add("High risk of financial loss - set strict betting limits");

            if (!result.LicensingCheck?.IsLicensed == true)
                recommendations.Add("Casino is not properly licensed - play at your own risk");

            return recommendations;
        }

        private RiskLevel DetermineRiskLevel(double score)
        {
            if (score >= 0.8) return RiskLevel.Critical;
            if (score >= 0.6) return RiskLevel.High;
            if (score >= 0.4) return RiskLevel.Medium;
            if (score >= 0.2) return RiskLevel.Low;
            return RiskLevel.Safe;
        }

        // Helper: find first index of a byte sequence inside another byte array, or -1 if not found
        private static int IndexOfSequence(byte[] haystack, byte[] needle)
        {
            if (needle == null || needle.Length == 0) return 0;
            if (haystack == null || haystack.Length == 0) return -1;
            for (int i = 0; i <= haystack.Length - needle.Length; i++)
            {
                bool match = true;
                for (int j = 0; j < needle.Length; j++)
                {
                    if (haystack[i + j] != needle[j])
                    {
                        match = false;
                        break;
                    }
                }
                if (match) return i;
            }
            return -1;
        }

        private GameProfile GetOrCreateGameProfile(string gameName, string gamePath)
        {
            var key = $"{gameName}_{HashingService.ComputeSHA256(gamePath)}";

            if (!_gameProfiles.TryGetValue(key, out var profile))
            {
                profile = new GameProfile
                {
                    GameName = gameName,
                    GamePath = gamePath,
                    FirstSeen = DateTime.UtcNow
                };
                _gameProfiles[key] = profile;
                SaveGameProfiles();
            }

            return profile;
        }

        private CasinoProfile GetOrCreateCasinoProfile(string casinoUrl, string casinoName)
        {
            var key = casinoUrl;

            if (!_casinoProfiles.TryGetValue(key, out var profile))
            {
                profile = new CasinoProfile
                {
                    CasinoUrl = casinoUrl,
                    CasinoName = casinoName,
                    FirstSeen = DateTime.UtcNow
                };
                _casinoProfiles[key] = profile;
                SaveCasinoProfiles();
            }

            return profile;
        }

        private Dictionary<string, GameProfile> LoadGameProfiles()
        {
            var profilesPath = Path.Combine(Path.GetDirectoryName(_gamingDbPath)!, "game_profiles.json");
            if (!File.Exists(profilesPath))
                return new Dictionary<string, GameProfile>();

            try
            {
                var json = File.ReadAllText(profilesPath);
                return JsonSerializer.Deserialize<Dictionary<string, GameProfile>>(json) ?? new Dictionary<string, GameProfile>();
            }
            catch
            {
                return new Dictionary<string, GameProfile>();
            }
        }

        private Dictionary<string, CasinoProfile> LoadCasinoProfiles()
        {
            var profilesPath = Path.Combine(Path.GetDirectoryName(_gamingDbPath)!, "casino_profiles.json");
            if (!File.Exists(profilesPath))
                return new Dictionary<string, CasinoProfile>();

            try
            {
                var json = File.ReadAllText(profilesPath);
                return JsonSerializer.Deserialize<Dictionary<string, CasinoProfile>>(json) ?? new Dictionary<string, CasinoProfile>();
            }
            catch
            {
                return new Dictionary<string, CasinoProfile>();
            }
        }

        private void SaveGameProfiles()
        {
            try
            {
                var json = JsonSerializer.Serialize(_gameProfiles, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(Path.Combine(Path.GetDirectoryName(_gamingDbPath)!, "game_profiles.json"), json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving game profiles: {ex.Message}");
            }
        }

        private void SaveCasinoProfiles()
        {
            try
            {
                var json = JsonSerializer.Serialize(_casinoProfiles, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(Path.Combine(Path.GetDirectoryName(_gamingDbPath)!, "casino_profiles.json"), json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving casino profiles: {ex.Message}");
            }
        }
    }

    // Supporting classes for Gaming Protection
    public class GamingAnalysisResult
    {
        public string GameName { get; set; } = string.Empty;
        public string GamePath { get; set; } = string.Empty;
        public DateTime AnalysisTime { get; set; }
        public ExecutableAnalysis? ExecutableAnalysis { get; set; }
        public ModificationCheck? ModificationCheck { get; set; }
        public GameNetworkAnalysis? GameNetworkAnalysis { get; set; }
        public GamblingDetection? GamblingDetection { get; set; }
        public RiskLevel OverallRisk { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class CasinoAnalysisResult
    {
        public string CasinoUrl { get; set; } = string.Empty;
        public string CasinoName { get; set; } = string.Empty;
        public DateTime AnalysisTime { get; set; }
        public WebsiteAnalysis? WebsiteAnalysis { get; set; }
        public LicensingCheck? LicensingCheck { get; set; }
        public FairnessAnalysis? FairnessAnalysis { get; set; }
        public ScamDetection? ScamDetection { get; set; }
        public FinancialRisk? FinancialRisk { get; set; }
        public RiskLevel OverallRisk { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class ExecutableAnalysis
    {
        public bool IsValidExecutable { get; set; }
        public string FileHash { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public List<string> RiskFactors { get; set; } = new();
        public List<string> MalwareIndicators { get; set; } = new();
    }

    public class ModificationCheck
    {
        public bool IsModified { get; set; }
        public string ModificationType { get; set; } = string.Empty;
        public RiskLevel RiskLevel { get; set; }
        public List<string> KnownCheats { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class GameNetworkAnalysis
    {
        public bool HasNetworkAccess { get; set; }
        public List<string> SuspiciousConnections { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class GamblingDetection
    {
        public bool ContainsGambling { get; set; }
        public bool ContainsLootBoxes { get; set; }
        public string GamblingType { get; set; } = string.Empty;
        public RiskLevel RiskLevel { get; set; }
        public List<string> Warnings { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class WebsiteAnalysis
    {
        public bool IsAccessible { get; set; }
        public bool UsesHttps { get; set; }
        public bool HasSecurityBadges { get; set; }
        public bool HasAgeVerification { get; set; }
        public bool HasResponsibleGambling { get; set; }
        public string Error { get; set; } = string.Empty;
    }

    public class LicensingCheck
    {
        public bool IsLicensed { get; set; }
        public string LicensingAuthority { get; set; } = string.Empty;
        public string Error { get; set; } = string.Empty;
    }

    public class FairnessAnalysis
    {
        public double RTP { get; set; }
        public bool HasRNGCertification { get; set; }
        public double ComplaintRatio { get; set; }
        public string Error { get; set; } = string.Empty;
    }

    public class ScamDetection
    {
        public bool KnownScams { get; set; }
        public List<string> RedFlags { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class FinancialRisk
    {
        public double RiskScore { get; set; }
        public RiskLevel RiskLevel { get; set; }
        public List<string> Warnings { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class FraudAlertResult
    {
        public bool IsFraudulent { get; set; }
        public double FraudScore { get; set; }
        public List<string> FraudIndicators { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class GameProfile
    {
        public string GameName { get; set; } = string.Empty;
        public string GamePath { get; set; } = string.Empty;
        public string OriginalHash { get; set; } = string.Empty;
        public DateTime FirstSeen { get; set; }
        public DateTime LastPlayed { get; set; }
        public int RigReports { get; set; }
        public DateTime? LastRigReport { get; set; }
        public List<RigEvidence> RigEvidence { get; set; } = new();
    }

    public class CasinoProfile
    {
        public string CasinoUrl { get; set; } = string.Empty;
        public string CasinoName { get; set; } = string.Empty;
        public DateTime FirstSeen { get; set; }
        public DateTime LastVisited { get; set; }
        public bool HasRNGCertification { get; set; }
        public double AverageRTP { get; set; } = 95.0; // Default RTP
        public int TotalReports { get; set; }
        public int NegativeReports { get; set; }
        public double ComplaintRatio { get; set; }
        public int ScamReports { get; set; }
        public DateTime? LastScamReport { get; set; }
        public List<ScamEvidence> ScamEvidence { get; set; } = new();
    }

    public class RigEvidence
    {
        public DateTime ReportDate { get; set; }
        public string Evidence { get; set; } = string.Empty;
        public string ReportedBy { get; set; } = string.Empty;
    }

    public class ScamEvidence
    {
        public DateTime ReportDate { get; set; }
        public string ScamType { get; set; } = string.Empty;
        public string Evidence { get; set; } = string.Empty;
        public string ReportedBy { get; set; } = string.Empty;
    }

    public class FraudDetectionEngine
    {
        public async Task<FraudAlertResult> AnalyzeTransactionAsync(string transactionData, decimal amount, string gameOrCasino)
        {
            await Task.Yield();
            var result = new FraudAlertResult();

            try
            {
                // Analyze transaction patterns
                var fraudIndicators = new List<string>();

                // Check for unusual amounts
                if (amount > 1000) fraudIndicators.Add("Large transaction amount");

                // Check for rapid successive transactions
                // This would require transaction history analysis

                // Check for known scam patterns
                if (transactionData.Contains("fake") || transactionData.Contains("test"))
                    fraudIndicators.Add("Suspicious transaction data");

                result.FraudIndicators = fraudIndicators;
                result.FraudScore = fraudIndicators.Count * 0.2;
                result.IsFraudulent = result.FraudScore > 0.5;

                if (result.IsFraudulent)
                {
                    result.Recommendations.Add("Cancel this transaction immediately");
                    result.Recommendations.Add("Contact your bank/financial institution");
                    result.Recommendations.Add("Report to gaming authorities if applicable");
                }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }
    }

    public class CasinoAnalyzer
    {
        // Additional casino analysis methods would go here
    }
}
