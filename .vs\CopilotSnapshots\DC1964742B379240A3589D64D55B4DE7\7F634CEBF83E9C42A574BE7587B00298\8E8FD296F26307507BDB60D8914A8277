﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class ParentalControlViewModel : BaseViewModel
    {
        private readonly ParentalControlService _parentalControlService;

        private bool _isEnabled = true;
        private string _currentUserProfile = "Default";
        private ObservableCollection<UserProfile> _userProfiles;
        private ObservableCollection<BlockedContent> _recentBlocks;
        private ObservableCollection<ContentFilter> _contentFilters;
        private TimeSpan _dailyScreenTimeLimit = TimeSpan.FromHours(2);
        private bool _blockSocialMedia = true;
        private bool _blockAdultContent = true;
        private bool _blockGambling = true;
        private bool _monitorChatActivity = true;
        private bool _enableSafeSearch = true;

        private UserProfile? _selectedUserProfile;

        public ParentalControlViewModel(ParentalControlService parentalControlService)
        {
            _parentalControlService = parentalControlService;
            _userProfiles = new ObservableCollection<UserProfile>();
            _recentBlocks = new ObservableCollection<BlockedContent>();
            _contentFilters = new ObservableCollection<ContentFilter>();

            AddUserProfileCommand = new RelayCommand(AddUserProfile);
            RemoveUserProfileCommand = new RelayCommand<UserProfile>(RemoveUserProfile);
            UpdateFiltersCommand = new RelayCommand(async () => await UpdateFilters());
            ViewBlockedContentCommand = new RelayCommand(ViewBlockedContent);

            LoadParentalControlData();
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        public string CurrentUserProfile
        {
            get => _currentUserProfile;
            set => SetProperty(ref _currentUserProfile, value);
        }

        public ObservableCollection<UserProfile> UserProfiles
        {
            get => _userProfiles;
            set => SetProperty(ref _userProfiles, value);
        }

        public ObservableCollection<BlockedContent> RecentBlocks
        {
            get => _recentBlocks;
            set => SetProperty(ref _recentBlocks, value);
        }

        public ObservableCollection<ContentFilter> ContentFilters
        {
            get => _contentFilters;
            set => SetProperty(ref _contentFilters, value);
        }

        public TimeSpan DailyScreenTimeLimit
        {
            get => _dailyScreenTimeLimit;
            set => SetProperty(ref _dailyScreenTimeLimit, value);
        }

        public bool BlockSocialMedia
        {
            get => _blockSocialMedia;
            set
            {
                if (SetProperty(ref _blockSocialMedia, value))
                {
                    UpdateContentFilter("Social Media", value);
                }
            }
        }

        public bool BlockAdultContent
        {
            get => _blockAdultContent;
            set
            {
                if (SetProperty(ref _blockAdultContent, value))
                {
                    UpdateContentFilter("Adult Content", value);
                }
            }
        }

        public bool BlockGambling
        {
            get => _blockGambling;
            set
            {
                if (SetProperty(ref _blockGambling, value))
                {
                    UpdateContentFilter("Gambling", value);
                }
            }
        }

        public bool MonitorChatActivity
        {
            get => _monitorChatActivity;
            set => SetProperty(ref _monitorChatActivity, value);
        }

        public bool EnableSafeSearch
        {
            get => _enableSafeSearch;
            set => SetProperty(ref _enableSafeSearch, value);
        }

        // New property bound from XAML
        public UserProfile? SelectedUserProfile
        {
            get => _selectedUserProfile;
            set => SetProperty(ref _selectedUserProfile, value);
        }

        public ICommand AddUserProfileCommand { get; }
        public ICommand RemoveUserProfileCommand { get; }
        public ICommand UpdateFiltersCommand { get; }
        public ICommand ViewBlockedContentCommand { get; }

        private void LoadParentalControlData()
        {
            try
            {
                // Load user profiles - for now create a default one
                UserProfiles.Clear();
                var defaultProfile = _parentalControlService.GetUserProfile("default");
                UserProfiles.Add(new UserProfile
                {
                    Name = "Default User",
                    AgeGroup = $"{defaultProfile.Age}+",
                    IsActive = true,
                    ScreenTimeUsed = TimeSpan.Zero,
                    ScreenTimeLimit = defaultProfile.Restrictions.DailyScreenTimeLimit
                });

                // Initialize content filters based on service filters
                ContentFilters.Clear();
                // Since we don't have a direct method to get all filters, we'll create based on restrictions
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Predator Detection",
                    IsBlocked = defaultProfile.Restrictions.BlockPredators,
                    Keywords = "grooming, secret friend, meet offline"
                });
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Inappropriate Content",
                    IsBlocked = defaultProfile.Restrictions.BlockInappropriate,
                    Keywords = "porn, sex, adult, xxx"
                });
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Violence",
                    IsBlocked = defaultProfile.Restrictions.BlockViolence,
                    Keywords = "kill, murder, blood, gore"
                });
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Bullying",
                    IsBlocked = defaultProfile.Restrictions.BlockBullying,
                    Keywords = "stupid, ugly, worthless, loser"
                });

                // Load current settings
                DailyScreenTimeLimit = defaultProfile.Restrictions.DailyScreenTimeLimit;
                BlockSocialMedia = defaultProfile.Restrictions.BlockedCategories.Contains("Social Media");
                BlockAdultContent = defaultProfile.Restrictions.BlockInappropriate;
                BlockGambling = defaultProfile.Restrictions.BlockedCategories.Contains("Gambling");
                MonitorChatActivity = true; // Default to enabled
                EnableSafeSearch = true; // Default to enabled

                // Recent blocks would need to be tracked separately - for now empty
                RecentBlocks.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading parental control data: {ex.Message}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddUserProfile()
        {
            // TODO: Implement user profile creation dialog
            MessageBox.Show("Add User Profile functionality will be implemented.", "Feature Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RemoveUserProfile(UserProfile profile)
        {
            if (profile != null)
            {
                var result = MessageBox.Show($"Remove user profile '{profile.Name}'?", "Confirm Removal", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    UserProfiles.Remove(profile);
                    // TODO: Remove from service
                }
            }
        }

        private async Task UpdateFilters()
        {
            try
            {
                // Update content filters through individual filter updates
                foreach (var filter in ContentFilters)
                {
                    var serviceFilter = new PcFutureShield.Common.Services.ContentFilter
                    {
                        Id = filter.Category.ToLower().Replace(" ", "_"),
                        Name = filter.Category,
                        Enabled = filter.IsBlocked,
                        Keywords = filter.Keywords.Split(", ").ToList()
                    };
                    _parentalControlService.AddContentFilter(serviceFilter);
                }

                LoadParentalControlData(); // Refresh data
                MessageBox.Show("Content filters updated successfully.", "Update Complete", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to update filters: {ex.Message}", "Update Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewBlockedContent()
        {
            // TODO: Implement blocked content viewer dialog
            MessageBox.Show("Blocked content viewer will be implemented.", "Feature Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdateContentFilter(string category, bool isBlocked)
        {
            var filter = ContentFilters.FirstOrDefault(f => f.Category == category);
            if (filter != null)
            {
                filter.IsBlocked = isBlocked;
            }
            else
            {
                ContentFilters.Add(new ContentFilter
                {
                    Category = category,
                    IsBlocked = isBlocked,
                    Keywords = GetDefaultKeywordsForCategory(category)
                });
            }
        }

        private string GetDefaultKeywordsForCategory(string category)
        {
            return category switch
            {
                "Social Media" => "facebook, twitter, instagram, tiktok, snapchat",
                "Adult Content" => "porn, adult, xxx, sex",
                "Gambling" => "casino, poker, betting, lottery",
                _ => ""
            };
        }
    }

    public class UserProfile
    {
        public string Name { get; set; } = string.Empty;
        public string AgeGroup { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public TimeSpan ScreenTimeUsed { get; set; }
        public TimeSpan ScreenTimeLimit { get; set; }
    }

    public class BlockedContent
    {
        public string Url { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public string BlockReason { get; set; } = string.Empty;
        public DateTime BlockedTime { get; set; }
        public string UserName { get; set; } = string.Empty;

        // Added for XAML bindings that expect a 'Content' field
        public string Content => !string.IsNullOrEmpty(BlockReason) ? $"{BlockReason} ({Url})" : Url;
    }

    public class ContentFilter
    {
        public string Category { get; set; } = string.Empty;
        public bool IsBlocked { get; set; }
        public string Keywords { get; set; } = string.Empty;
    }
}
