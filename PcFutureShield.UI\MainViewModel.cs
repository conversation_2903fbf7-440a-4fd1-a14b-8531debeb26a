using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;
using PcFutureShield.UI.Services;
using Microsoft.Extensions.Logging.Abstractions;

namespace PcFutureShield.UI
{
    public class MainViewModel : BaseViewModel
    {
        private string _title = "PcFutureShield";
        private string _errorMessage = string.Empty;
        private object _currentView;
        private string _currentTheme;
        private bool _isRealTimeProtectionEnabled;
        private string _statusMessage;
        private int _threatCount;
        private double _systemHealthScore;

        // Services
        private AntivirusOrchestrator _antivirusOrchestrator;
        private ParentalControlService _parentalControlService;
        private GamingProtectionService _gamingProtectionService;
        private PcOptimizationService _pcOptimizationService;
        private AdminOverrideService _adminOverrideService;
        private BrowserExtensionService _browserExtensionService;
        private PcFutureShield.UI.Services.LicenseManager _licenseManager;

        // Logging
        private readonly LoggingService _logger = LoggingService.Instance;

        // View Models
        public DashboardViewModel DashboardViewModel { get; }
        public VirusScanViewModel VirusScanViewModel { get; }
        public EnhancedScanViewModel EnhancedScanViewModel { get; }
        public RealtimeProtectionViewModel RealtimeProtectionViewModel { get; }
        public ParentalControlViewModel ParentalControlViewModel { get; }
        public QuarantineViewModel QuarantineViewModel { get; }
        public AIDashboardViewModel AIDashboardViewModel { get; }
        public GamingProtectionViewModel GamingProtectionViewModel { get; }
        public AdminOverrideViewModel AdminOverrideViewModel { get; }
        public SmartRepairViewModel SmartRepairViewModel { get; }
        public SettingsViewModel SettingsViewModel { get; }
        public LicenseManagerViewModel LicenseManagerViewModel { get; set; }
        public UpdatesViewModel UpdatesViewModel { get; }
        public PcOptimizationViewModel PcOptimizationViewModel { get; }
        public BrowserExtensionViewModel BrowserExtensionViewModel { get; }
        public ThemeSelectorViewModel ThemeSelectorViewModel { get; }

        public MainViewModel()
        {
            _logger.LogInfo("MainViewModel", "Initializing MainViewModel");

            // Initialize and register services
            try
            {
                InitializeServices();
                _logger.LogInfo("MainViewModel", "Services initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError("MainViewModel", "Service initialization failed", ex);
                throw new Exception($"Service initialization failed: {ex.Message}", ex);
            }

            // Initialize ViewModels with services - simplified for debugging
            try
            {
                DashboardViewModel = new DashboardViewModel(_antivirusOrchestrator, new ThreatIntelligenceService());
                ParentalControlViewModel = new ParentalControlViewModel(_parentalControlService);
                GamingProtectionViewModel = new GamingProtectionViewModel(_gamingProtectionService);
                PcOptimizationViewModel = new PcOptimizationViewModel(_pcOptimizationService);
                AdminOverrideViewModel = new AdminOverrideViewModel(_adminOverrideService);
                BrowserExtensionViewModel = new BrowserExtensionViewModel(_browserExtensionService);
                ThemeSelectorViewModel = new ThemeSelectorViewModel();

                // Add simpler ViewModels that don't require complex services
                EnhancedScanViewModel = new EnhancedScanViewModel(_antivirusOrchestrator);
                RealtimeProtectionViewModel = new RealtimeProtectionViewModel();
                QuarantineViewModel = new QuarantineViewModel();
                SmartRepairViewModel = new SmartRepairViewModel(_pcOptimizationService);
                AIDashboardViewModel = new AIDashboardViewModel();
                SettingsViewModel = new SettingsViewModel();
                // LicenseManagerViewModel will be initialized after services are set up
                UpdatesViewModel = new UpdatesViewModel();

                // Try to add the complex VirusScanViewModel with a proper logger
                try
                {
                    // Use a NullLogger<T> to satisfy the scanner's ILogger<T> dependency without pulling in extra logging configuration here.
                    var vsLogger = Microsoft.Extensions.Logging.Abstractions.NullLogger<PcFutureShield.Engine.VirusScanner.VirusScannerService>.Instance;
                    var sigDbPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Signatures", "signatures.db");
                    var signatureDb = new PcFutureShield.Engine.VirusScanner.SignatureDatabase(sigDbPath);
                    var engineScanner = new PcFutureShield.Engine.VirusScanner.VirusScannerService(vsLogger, signatureDb);
                    // Register instances for other components
                    PcFutureShield.UI.Services.ServiceLocator.Register<PcFutureShield.Engine.VirusScanner.VirusScannerService>(engineScanner);
                    PcFutureShield.UI.Services.ServiceLocator.Register<PcFutureShield.Engine.VirusScanner.SignatureDatabase>(signatureDb);
                    VirusScanViewModel = new VirusScanViewModel(new VirusScannerServiceAdapter(engineScanner));
                }
                catch (Exception ex)
                {
                    _logger.LogError("MainViewModel", "Failed to initialize VirusScanViewModel", ex);
                    VirusScanViewModel = null;
                }

                // Set default view
                CurrentView = DashboardViewModel;
            }
            catch (Exception ex)
            {
                throw new Exception($"ViewModel initialization failed: {ex.Message}", ex);
            }

            // Initialize properties
            CurrentTheme = "GlossyBlue";
            IsRealTimeProtectionEnabled = true;
            StatusMessage = "System Protected";
            ThreatCount = 0;
            SystemHealthScore = 95.0;
            Title = "PcFutureShield";
            ErrorMessage = string.Empty;

            // Initialize commands
            InitializeCommands();
        }

        public object CurrentView
        {
            get => _currentView;
            set
            {
                _currentView = value;
                OnPropertyChanged();
            }
        }

        public string CurrentTheme
        {
            get => _currentTheme;
            set
            {
                if (_currentTheme != value)
                {
                    _logger.LogInfo("MainViewModel", $"Theme changing from '{_currentTheme}' to '{value}'");
                    _currentTheme = value;
                    OnPropertyChanged();
                    OnRequestThemeChange?.Invoke(value);
                    _logger.LogInfo("MainViewModel", $"Theme change request sent for '{value}'");
                }
            }
        }

        public bool IsRealTimeProtectionEnabled
        {
            get => _isRealTimeProtectionEnabled;
            set
            {
                _isRealTimeProtectionEnabled = value;
                OnPropertyChanged();
                UpdateStatusMessage();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public int ThreatCount
        {
            get => _threatCount;
            set
            {
                _threatCount = value;
                OnPropertyChanged();
                UpdateStatusMessage();
            }
        }

        public double SystemHealthScore
        {
            get => _systemHealthScore;
            set
            {
                _systemHealthScore = value;
                OnPropertyChanged();
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                if (_errorMessage != value)
                {
                    _errorMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        // Commands
        public ICommand DashboardCommand { get; private set; }
        public ICommand VirusScanCommand { get; private set; }
        public ICommand EnhancedScannerCommand { get; private set; }
        public ICommand RealTimeProtectionCommand { get; private set; }
        public ICommand ParentalControlCommand { get; private set; }
        public ICommand QuarantineCommand { get; private set; }
        public ICommand AIDashboardCommand { get; private set; }
        public ICommand GamingProtectionCommand { get; private set; }
        public ICommand AdminOverrideCommand { get; private set; }
        public ICommand SmartRepairCommand { get; private set; }
        public ICommand SettingsCommand { get; private set; }
        public ICommand LicenseManagerCommand { get; private set; }
        public ICommand UpdatesCommand { get; private set; }
        public ICommand PcOptimizationCommand { get; private set; }
        public ICommand BrowserExtensionCommand { get; private set; }
        public ICommand ThemeSelectorCommand { get; private set; }

        // Events
        public event Action<string> OnRequestThemeChange;

        private void InitializeServices()
        {
            _logger.LogInfo("MainViewModel", "Initializing services");

            // Initialize core services
            var behavioralAnalysis = new BehavioralAnalysisService();
            var threatIntelligence = new ThreatIntelligenceService();
            var aiDetectionService = new AdvancedAIDetectionService();
            var zeroDayService = new ZeroDayDetectionService(behavioralAnalysis, threatIntelligence);

            // Initialize main services
            _antivirusOrchestrator = new AntivirusOrchestrator(
                zeroDayService,
                aiDetectionService,
                behavioralAnalysis,
                threatIntelligence,
                NullLogger<AntivirusOrchestrator>.Instance
            );

            _parentalControlService = new ParentalControlService();
            _gamingProtectionService = new GamingProtectionService();
            _pcOptimizationService = new PcOptimizationService();
            _adminOverrideService = new AdminOverrideService();
            _browserExtensionService = new BrowserExtensionService();

            // Initialize additional services
            var settingsService = new SettingsService();
            _licenseManager = new PcFutureShield.UI.Services.LicenseManager();
            var updateService = new UpdateService(settingsService);

            // Register services in ServiceLocator for ViewModels to access
            ServiceLocator.Register(_antivirusOrchestrator);
            ServiceLocator.Register(_parentalControlService);
            ServiceLocator.Register(_gamingProtectionService);
            ServiceLocator.Register(_pcOptimizationService);
            ServiceLocator.Register(_adminOverrideService);
            ServiceLocator.Register(_browserExtensionService);
            ServiceLocator.Register(behavioralAnalysis);
            ServiceLocator.Register(threatIntelligence);
            ServiceLocator.Register(aiDetectionService);
            ServiceLocator.Register(zeroDayService);
            ServiceLocator.Register(settingsService);
            ServiceLocator.Register<ILicenseManager>(_licenseManager);
            ServiceLocator.Register(updateService);

            // Initialize scanner services
            try
            {
                var vsLogger = NullLogger<PcFutureShield.Engine.VirusScanner.VirusScannerService>.Instance;
                var sigDbPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Signatures", "signatures.db");
                var signatureDb = new PcFutureShield.Engine.VirusScanner.SignatureDatabase(sigDbPath);
                var engineScanner = new PcFutureShield.Engine.VirusScanner.VirusScannerService(vsLogger, signatureDb);

                ServiceLocator.Register(engineScanner);
                ServiceLocator.Register(signatureDb);
                ServiceLocator.Register(new VirusScannerServiceAdapter(engineScanner));
            }
            catch (Exception ex)
            {
                _logger.LogError("MainViewModel", "Failed to initialize scanner services", ex);
            }

            _logger.LogInfo("MainViewModel", "Services registered successfully");

            // Now initialize ViewModels that require services
            LicenseManagerViewModel = new LicenseManagerViewModel(_licenseManager);
        }

        private void InitializeCommands()
        {
            _logger.LogDebug("MainViewModel", "Initializing commands");

            DashboardCommand = new RelayCommand(() =>
            {
                CurrentView = DashboardViewModel;
                // Load dashboard data when user navigates to it
                DashboardViewModel.LoadDataCommand.Execute(null);
            });
            ParentalControlCommand = new RelayCommand(() => CurrentView = ParentalControlViewModel);
            GamingProtectionCommand = new RelayCommand(() => CurrentView = GamingProtectionViewModel);
            PcOptimizationCommand = new RelayCommand(() => CurrentView = PcOptimizationViewModel);
            AdminOverrideCommand = new RelayCommand(() => CurrentView = AdminOverrideViewModel);
            BrowserExtensionCommand = new RelayCommand(() => CurrentView = BrowserExtensionViewModel);
            ThemeSelectorCommand = new RelayCommand(() => CurrentView = ThemeSelectorViewModel);
            EnhancedScannerCommand = new RelayCommand(() => CurrentView = EnhancedScanViewModel);
            RealTimeProtectionCommand = new RelayCommand(() => CurrentView = RealtimeProtectionViewModel);
            QuarantineCommand = new RelayCommand(() => CurrentView = QuarantineViewModel);
            SmartRepairCommand = new RelayCommand(() => CurrentView = SmartRepairViewModel);
            AIDashboardCommand = new RelayCommand(() => CurrentView = AIDashboardViewModel);
            VirusScanCommand = new RelayCommand(() => { if (VirusScanViewModel != null) CurrentView = VirusScanViewModel; });
            SettingsCommand = new RelayCommand(() => CurrentView = SettingsViewModel);
            LicenseManagerCommand = new RelayCommand(() => CurrentView = LicenseManagerViewModel);
            UpdatesCommand = new RelayCommand(() => CurrentView = UpdatesViewModel);
        }

        private void UpdateStatusMessage()
        {
            if (!IsRealTimeProtectionEnabled)
            {
                StatusMessage = "Protection Disabled";
            }
            else if (ThreatCount > 0)
            {
                StatusMessage = $"{ThreatCount} Threat{(ThreatCount == 1 ? "" : "s")} Detected";
            }
            else
            {
                StatusMessage = "System Protected";
            }
        }


    }
}
