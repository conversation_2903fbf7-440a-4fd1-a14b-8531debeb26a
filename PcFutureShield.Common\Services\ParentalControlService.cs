#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using System.Net;
using System.Net.NetworkInformation;
using Microsoft.Win32;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced parental control service for protecting children from online threats
    /// </summary>
    public class ParentalControlService
    {
        private readonly HttpClient _httpClient;
        private readonly string _parentalDbPath;
        private readonly Dictionary<string, ContentFilter> _contentFilters;
        private readonly Dictionary<string, UserProfile> _userProfiles;
        private readonly BehaviorAnalyzer _behaviorAnalyzer;
        private readonly NetworkBlockingService _networkBlocker;
        private readonly DnsFilteringService _dnsFilter;

        public ParentalControlService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(10);

            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var parentalPath = Path.Combine(appData, "PcFutureShield", "Parental");
            Directory.CreateDirectory(parentalPath);
            _parentalDbPath = Path.Combine(parentalPath, "parental_db.json");

            _contentFilters = InitializeContentFilters();
            _userProfiles = LoadUserProfiles();
            _behaviorAnalyzer = new BehaviorAnalyzer();
            _networkBlocker = new NetworkBlockingService();
            _dnsFilter = new DnsFilteringService();
        }

        public async Task<ParentalAnalysisResult> AnalyzeContentAsync(string content, string url, UserProfile userProfile)
        {
            var result = new ParentalAnalysisResult
            {
                Content = content,
                Url = url,
                UserProfile = userProfile,
                AnalysisTime = DateTime.UtcNow
            };

            try
            {
                // Parallel analysis of different threat categories
                var predatorTask = AnalyzePredatorContentAsync(content, url);
                var inappropriateTask = AnalyzeInappropriateContentAsync(content);
                var violenceTask = AnalyzeViolenceContentAsync(content);
                var bullyingTask = AnalyzeBullyingContentAsync(content);
                var suicideTask = AnalyzeSuicideIndicatorsAsync(content);

                await Task.WhenAll(predatorTask, inappropriateTask, violenceTask, bullyingTask, suicideTask);

                result.PredatorRisk = await predatorTask;
                result.InappropriateRisk = await inappropriateTask;
                result.ViolenceRisk = await violenceTask;
                result.BullyingRisk = await bullyingTask;
                result.SuicideRisk = await suicideTask;

                // Calculate overall risk score
                result.OverallRiskScore = CalculateOverallRiskScore(result);

                // Determine if content should be blocked
                result.ShouldBlock = ShouldBlockContent(result, userProfile);

                // Generate parental alerts if needed
                result.Alerts = GenerateParentalAlerts(result);

                // Update user behavior patterns
                await _behaviorAnalyzer.UpdateBehaviorPatternsAsync(userProfile, result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.ShouldBlock = true; // Default to blocking on error
            }

            return result;
        }

        public async Task<BehaviorAnalysisResult> AnalyzeUserBehaviorAsync(UserProfile userProfile, List<string> recentActivities)
        {
            return await _behaviorAnalyzer.AnalyzeBehaviorAsync(userProfile, recentActivities);
        }

        public void AddContentFilter(ContentFilter filter)
        {
            _contentFilters[filter.Id] = filter;
            SaveContentFilters();
        }

        /// <summary>
        /// Asynchronously add or update a content filter and persist to storage.
        /// </summary>
        public async Task AddContentFilterAsync(ContentFilter filter)
        {
            _contentFilters[filter.Id] = filter;
            await SaveContentFiltersAsync().ConfigureAwait(false);
        }

        public void UpdateUserProfile(UserProfile profile)
        {
            _userProfiles[profile.UserId] = profile;
            SaveUserProfiles();
        }

        public UserProfile GetUserProfile(string userId)
        {
            return _userProfiles.TryGetValue(userId, out var profile) ? profile : CreateDefaultProfile(userId);
        }

        /// <summary>
        /// Enable network-level blocking for parental controls
        /// </summary>
        public async Task<bool> EnableNetworkBlockingAsync()
        {
            try
            {
                // Enable comprehensive DNS filtering
                var dnsSuccess = await _dnsFilter.EnableDnsFilteringAsync();

                // Block default categories
                await _dnsFilter.BlockCategoryAsync("adult");
                await _dnsFilter.BlockCategoryAsync("gambling");

                // Set up network proxy if needed
                await _networkBlocker.EnableProxyFilteringAsync();

                return dnsSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to enable network blocking: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable network-level blocking
        /// </summary>
        public async Task<bool> DisableNetworkBlockingAsync()
        {
            try
            {
                // Disable DNS filtering
                var dnsSuccess = await _dnsFilter.DisableDnsFilteringAsync();

                // Disable proxy filtering
                await _networkBlocker.DisableProxyFilteringAsync();

                return dnsSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to disable network blocking: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Add a domain to the block list and immediately apply blocking
        /// </summary>
        public async Task<bool> BlockDomainAsync(string domain)
        {
            try
            {
                return await _dnsFilter.BlockDomainAsync(domain);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to block domain {domain}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove a domain from the block list
        /// </summary>
        public async Task<bool> UnblockDomainAsync(string domain)
        {
            try
            {
                return await _dnsFilter.UnblockDomainAsync(domain);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to unblock domain {domain}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Block entire category of domains (adult, gambling, etc.)
        /// </summary>
        public async Task<bool> BlockCategoryAsync(string category)
        {
            try
            {
                return await _dnsFilter.BlockCategoryAsync(category);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to block category {category}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unblock entire category of domains
        /// </summary>
        public async Task<bool> UnblockCategoryAsync(string category)
        {
            try
            {
                return await _dnsFilter.UnblockCategoryAsync(category);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to unblock category {category}: {ex.Message}");
                return false;
            }
        }

        private Task<RiskLevel> AnalyzePredatorContentAsync(string content, string url)
        {
            // Offload regex/CPU-bound analysis to a background thread
            return Task.Run(() =>
            {
                var riskScore = 0.0;

                var groomingPatterns = new[]
                {
                    @"(secret|special) (friend|buddy)",
                    @"don't tell (your )?(parents|mom|dad)",
                    @"meet (me|us) (in person|irl|offline)",
                    @"send (me )?(picture|photo|pic)",
                    @"age is just a number",
                    @"you're (mature|different) for your age"
                };

                foreach (var pattern in groomingPatterns)
                {
                    if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                        riskScore += 0.3;
                }

                var suspiciousUrls = new[] { "chat", "meet", "date", "friend", "secret" };
                if (suspiciousUrls.Any(word => url.Contains(word, StringComparison.OrdinalIgnoreCase)))
                    riskScore += 0.2;

                return DetermineRiskLevel(riskScore);
            });
        }

        private Task<RiskLevel> AnalyzeInappropriateContentAsync(string content)
        {
            return Task.Run(() =>
            {
                var riskScore = 0.0;

                var explicitKeywords = new[]
                {
                    "porn", "sex", "nude", "naked", "adult", "xxx",
                    "erotic", "sexual", "intimate", "bedroom"
                };

                foreach (var keyword in explicitKeywords)
                {
                    if (content.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                        riskScore += 0.4;
                }

                if (Regex.IsMatch(content, @"1[3-9]|[2-9]\d", RegexOptions.IgnoreCase)) // Ages 13+
                    riskScore += 0.2;

                return DetermineRiskLevel(riskScore);
            });
        }

        private Task<RiskLevel> AnalyzeViolenceContentAsync(string content)
        {
            return Task.Run(() =>
            {
                var riskScore = 0.0;

                var violenceKeywords = new[]
                {
                    "kill", "murder", "death", "blood", "gore", "torture",
                    "fight", "weapon", "gun", "knife", "bomb", "explosion"
                };

                foreach (var keyword in violenceKeywords)
                {
                    if (content.Contains(keyword, StringComparison.OrdinalIgnoreCase))
                        riskScore += 0.3;
                }

                return DetermineRiskLevel(riskScore);
            });
        }

        private Task<RiskLevel> AnalyzeBullyingContentAsync(string content)
        {
            return Task.Run(() =>
            {
                var riskScore = 0.0;

                var bullyingPatterns = new[]
                {
                    @"you're (stupid|ugly|fat|loser|worthless)",
                    @"nobody likes you",
                    @"go kill yourself",
                    @"you're a failure",
                    @"everyone hates you"
                };

                foreach (var pattern in bullyingPatterns)
                {
                    if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                        riskScore += 0.4;
                }

                return DetermineRiskLevel(riskScore);
            });
        }

        private Task<RiskLevel> AnalyzeSuicideIndicatorsAsync(string content)
        {
            return Task.Run(() =>
            {
                var riskScore = 0.0;

                var suicideIndicators = new[]
                {
                    @"(want to )?kill myself",
                    @"end it all",
                    @"not worth living",
                    @"better off dead",
                    @"suicide",
                    @"self-harm"
                };

                foreach (var indicator in suicideIndicators)
                {
                    if (Regex.IsMatch(content, indicator, RegexOptions.IgnoreCase))
                        riskScore += 0.5;
                }

                return DetermineRiskLevel(riskScore);
            });
        }

        private double CalculateOverallRiskScore(ParentalAnalysisResult result)
        {
            var weights = new Dictionary<RiskLevel, double>
            {
                { RiskLevel.Safe, 0.0 },
                { RiskLevel.Low, 0.2 },
                { RiskLevel.Medium, 0.5 },
                { RiskLevel.High, 0.8 },
                { RiskLevel.Critical, 1.0 }
            };

            var scores = new[]
            {
                weights[result.PredatorRisk],
                weights[result.InappropriateRisk],
                weights[result.ViolenceRisk],
                weights[result.BullyingRisk],
                weights[result.SuicideRisk]
            };

            return scores.Max(); // Use highest risk as overall score
        }

        private bool ShouldBlockContent(ParentalAnalysisResult result, UserProfile profile)
        {
            // Always block critical content
            if (result.OverallRiskScore >= 0.8) return true;

            // Check user profile restrictions
            if (profile.Age < 13 && result.OverallRiskScore >= 0.3) return true;
            if (profile.Age < 16 && result.OverallRiskScore >= 0.5) return true;

            // Block based on specific risk types
            if (result.SuicideRisk >= RiskLevel.High) return true;
            if (result.PredatorRisk >= RiskLevel.Medium) return true;

            return false;
        }

        private List<string> GenerateParentalAlerts(ParentalAnalysisResult result)
        {
            var alerts = new List<string>();

            if (result.PredatorRisk >= RiskLevel.Medium)
                alerts.Add("Potential online predator activity detected");

            if (result.SuicideRisk >= RiskLevel.High)
                alerts.Add("URGENT: Suicide indicators detected - immediate attention required");

            if (result.BullyingRisk >= RiskLevel.Medium)
                alerts.Add("Cyberbullying content detected");

            if (result.InappropriateRisk >= RiskLevel.Medium)
                alerts.Add("Inappropriate content for age group");

            return alerts;
        }

        private RiskLevel DetermineRiskLevel(double score)
        {
            if (score >= 0.8) return RiskLevel.Critical;
            if (score >= 0.6) return RiskLevel.High;
            if (score >= 0.4) return RiskLevel.Medium;
            if (score >= 0.2) return RiskLevel.Low;
            return RiskLevel.Safe;
        }

        private Dictionary<string, ContentFilter> InitializeContentFilters()
        {
            return new Dictionary<string, ContentFilter>
            {
                ["predator"] = new ContentFilter { Id = "predator", Name = "Predator Detection", Enabled = true },
                ["inappropriate"] = new ContentFilter { Id = "inappropriate", Name = "Inappropriate Content", Enabled = true },
                ["violence"] = new ContentFilter { Id = "violence", Name = "Violence Filter", Enabled = true },
                ["bullying"] = new ContentFilter { Id = "bullying", Name = "Bullying Detection", Enabled = true },
                ["suicide"] = new ContentFilter { Id = "suicide", Name = "Suicide Prevention", Enabled = true }
            };
        }

        private UserProfile CreateDefaultProfile(string userId)
        {
            return new UserProfile
            {
                UserId = userId,
                Age = 13, // Default to conservative age
                Restrictions = new ParentalRestrictions
                {
                    BlockPredators = true,
                    BlockInappropriate = true,
                    BlockViolence = true,
                    BlockBullying = true,
                    MonitorSuicide = true
                }
            };
        }

        private Dictionary<string, UserProfile> LoadUserProfiles()
        {
            if (!File.Exists(_parentalDbPath))
                return new Dictionary<string, UserProfile>();

            try
            {
                var json = File.ReadAllText(_parentalDbPath);
                return JsonSerializer.Deserialize<Dictionary<string, UserProfile>>(json) ?? new Dictionary<string, UserProfile>();
            }
            catch
            {
                return new Dictionary<string, UserProfile>();
            }
        }

        private void SaveUserProfiles()
        {
            try
            {
                var json = JsonSerializer.Serialize(_userProfiles, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_parentalDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving user profiles: {ex.Message}");
            }
        }

        private void SaveContentFilters()
        {
            try
            {
                var json = JsonSerializer.Serialize(_contentFilters, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(Path.Combine(Path.GetDirectoryName(_parentalDbPath)!, "content_filters.json"), json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving content filters: {ex.Message}");
            }
        }

        private async Task SaveContentFiltersAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_contentFilters, new JsonSerializerOptions { WriteIndented = true });
                var path = Path.Combine(Path.GetDirectoryName(_parentalDbPath)!, "content_filters.json");
                await File.WriteAllTextAsync(path, json).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving content filters: {ex.Message}");
            }
        }
    }

    // Supporting classes
    public class ParentalAnalysisResult
    {
        public string Content { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public UserProfile UserProfile { get; set; } = new();
        public DateTime AnalysisTime { get; set; }
        public RiskLevel PredatorRisk { get; set; }
        public RiskLevel InappropriateRisk { get; set; }
        public RiskLevel ViolenceRisk { get; set; }
        public RiskLevel BullyingRisk { get; set; }
        public RiskLevel SuicideRisk { get; set; }
        public double OverallRiskScore { get; set; }
        public bool ShouldBlock { get; set; }
        public List<string> Alerts { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class UserProfile
    {
        public string UserId { get; set; } = string.Empty;
        public int Age { get; set; }
        public ParentalRestrictions Restrictions { get; set; } = new();
        public List<string> TrustedSites { get; set; } = new();
        public List<string> BlockedSites { get; set; } = new();
        public DateTime Created { get; set; } = DateTime.UtcNow;
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;
    }

    public class ParentalRestrictions
    {
        public bool BlockPredators { get; set; } = true;
        public bool BlockInappropriate { get; set; } = true;
        public bool BlockViolence { get; set; } = true;
        public bool BlockBullying { get; set; } = true;
        public bool MonitorSuicide { get; set; } = true;
        public TimeSpan DailyScreenTimeLimit { get; set; } = TimeSpan.FromHours(2);
        public List<string> BlockedCategories { get; set; } = new();
    }

    public class ContentFilter
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public bool Enabled { get; set; }
        public List<string> Keywords { get; set; } = new();
        public List<string> Patterns { get; set; } = new();
    }

    public class BehaviorAnalysisResult
    {
        public string UserId { get; set; } = string.Empty;
        public RiskLevel BehaviorRisk { get; set; }
        public List<string> RiskIndicators { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public DateTime AnalysisTime { get; set; } = DateTime.UtcNow;
    }

    public class BehaviorAnalyzer
    {
        private readonly Dictionary<string, UserBehaviorPattern> _behaviorPatterns = new();

        public async Task UpdateBehaviorPatternsAsync(UserProfile profile, ParentalAnalysisResult result)
        {
            if (!_behaviorPatterns.ContainsKey(profile.UserId))
            {
                _behaviorPatterns[profile.UserId] = new UserBehaviorPattern { UserId = profile.UserId };
            }

            var pattern = _behaviorPatterns[profile.UserId];
            pattern.LastActivity = DateTime.UtcNow;
            pattern.TotalAnalyses++;

            if (result.ShouldBlock) pattern.BlockedContentCount++;
            if (result.SuicideRisk >= RiskLevel.Medium) pattern.SuicideIndicators++;
            if (result.PredatorRisk >= RiskLevel.Medium) pattern.PredatorEncounters++;
        }

        public async Task<BehaviorAnalysisResult> AnalyzeBehaviorAsync(UserProfile profile, List<string> recentActivities)
        {
            var result = new BehaviorAnalysisResult { UserId = profile.UserId };

            if (_behaviorPatterns.TryGetValue(profile.UserId, out var pattern))
            {
                // Analyze behavior patterns
                var blockRatio = pattern.TotalAnalyses > 0 ? (double)pattern.BlockedContentCount / pattern.TotalAnalyses : 0;

                if (blockRatio > 0.5) result.RiskIndicators.Add("High content blocking rate");
                if (pattern.SuicideIndicators > 3) result.RiskIndicators.Add("Multiple suicide indicators");
                if (pattern.PredatorEncounters > 2) result.RiskIndicators.Add("Multiple predator encounters");

                result.BehaviorRisk = DetermineBehaviorRisk(result.RiskIndicators.Count);
            }

            return result;
        }

        private RiskLevel DetermineBehaviorRisk(int riskIndicatorCount)
        {
            if (riskIndicatorCount >= 3) return RiskLevel.High;
            if (riskIndicatorCount >= 2) return RiskLevel.Medium;
            if (riskIndicatorCount >= 1) return RiskLevel.Low;
            return RiskLevel.Safe;
        }


    }

    /// <summary>
    /// Network-level blocking service for parental controls
    /// </summary>
    public class NetworkBlockingService
    {
        public async Task<bool> EnableProxyFilteringAsync()
        {
            // TODO: Implement proxy-based filtering if needed
            await Task.Delay(1);
            return true;
        }

        public async Task<bool> DisableProxyFilteringAsync()
        {
            // TODO: Implement proxy disabling if needed
            await Task.Delay(1);
            return true;
        }
    }

    public class UserBehaviorPattern
    {
        public string UserId { get; set; } = string.Empty;
        public int TotalAnalyses { get; set; }
        public int BlockedContentCount { get; set; }
        public int SuicideIndicators { get; set; }
        public int PredatorEncounters { get; set; }
        public DateTime LastActivity { get; set; }
    }

    public enum RiskLevel
    {
        Safe,
        Low,
        Medium,
        High,
        Critical
    }
}
