using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Security.Principal;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class AdminOverrideViewModel : BaseViewModel
    {
        private readonly AdminOverrideService _adminOverrideService;

        private bool _isOverrideActive;
        private ObservableCollection<OverrideSession> _activeSessions;
        private ObservableCollection<OverrideRequest> _pendingRequests;
        private ObservableCollection<AdminLogEntry> _recentActivity;
        private string _currentAdminUser = WindowsIdentity.GetCurrent().Name;
        private TimeSpan _maxOverrideDuration = TimeSpan.FromHours(1);
        private bool _requireApproval = true;
        private bool _enableLogging = true;
        private string _overrideReason = string.Empty;

        public AdminOverrideViewModel(AdminOverrideService adminOverrideService)
        {
            _adminOverrideService = adminOverrideService;
            _activeSessions = new ObservableCollection<OverrideSession>();
            _pendingRequests = new ObservableCollection<OverrideRequest>();
            _recentActivity = new ObservableCollection<AdminLogEntry>();

            RequestOverrideCommand = new RelayCommand(async () => await RequestOverride());
            ApproveRequestCommand = new RelayCommand<OverrideRequest>(async (request) => await ApproveRequest(request));
            DenyRequestCommand = new RelayCommand<OverrideRequest>(async (request) => await DenyRequest(request));
            EndOverrideCommand = new RelayCommand(async () => await EndOverride());
            ViewAdminLogsCommand = new RelayCommand(ViewAdminLogs);

            LoadAdminOverrideData();
        }

        public bool IsOverrideActive
        {
            get => _isOverrideActive;
            set => SetProperty(ref _isOverrideActive, value);
        }

        public ObservableCollection<OverrideSession> ActiveSessions
        {
            get => _activeSessions;
            set => SetProperty(ref _activeSessions, value);
        }

        public ObservableCollection<OverrideRequest> PendingRequests
        {
            get => _pendingRequests;
            set => SetProperty(ref _pendingRequests, value);
        }

        public ObservableCollection<AdminLogEntry> RecentActivity
        {
            get => _recentActivity;
            set => SetProperty(ref _recentActivity, value);
        }

        public string CurrentAdminUser
        {
            get => _currentAdminUser;
            set => SetProperty(ref _currentAdminUser, value);
        }

        public TimeSpan MaxOverrideDuration
        {
            get => _maxOverrideDuration;
            set => SetProperty(ref _maxOverrideDuration, value);
        }

        // Adapter property for XAML binding: minutes as an integer for simpler two-way binding
        public int MaxOverrideDurationMinutes
        {
            get => (int)_maxOverrideDuration.TotalMinutes;
            set => MaxOverrideDuration = TimeSpan.FromMinutes(Math.Max(0, value));
        }

        public bool RequireApproval
        {
            get => _requireApproval;
            set => SetProperty(ref _requireApproval, value);
        }

        public bool EnableLogging
        {
            get => _enableLogging;
            set => SetProperty(ref _enableLogging, value);
        }

        public string OverrideReason
        {
            get => _overrideReason;
            set => SetProperty(ref _overrideReason, value);
        }

        // Adapter to expose the current admin user under the name expected by the XAML (UserName)
        public string UserName
        {
            get => CurrentAdminUser;
            set => CurrentAdminUser = value;
        }

        public ICommand RequestOverrideCommand { get; }
        public ICommand ApproveRequestCommand { get; }
        public ICommand DenyRequestCommand { get; }
        public ICommand EndOverrideCommand { get; }
        public ICommand ViewAdminLogsCommand { get; }

        private void LoadAdminOverrideData()
        {
            try
            {
                // Initialize with sample data
                ActiveSessions.Clear();
                PendingRequests.Clear();
                RecentActivity.Clear();

                // Load actual active sessions
                var activeSessions = _adminOverrideService.GetActiveSessions();
                foreach (var session in activeSessions)
                {
                    ActiveSessions.Add(new OverrideSession
                    {
                        SessionId = session.SessionId,
                        AdminUser = WindowsIdentity.GetCurrent().Name,
                        StartTime = session.CreatedAt,
                        Duration = session.TimeRemaining,
                        Reason = session.Reason,
                        IsActive = session.IsActive
                    });
                }
                IsOverrideActive = ActiveSessions.Any(s => s.IsActive);

                // Load actual pending requests
                var pendingRequests = _adminOverrideService.GetPendingRequests();
                foreach (var request in pendingRequests)
                {
                    PendingRequests.Add(new OverrideRequest
                    {
                        RequestId = request.Id,
                        RequestedBy = request.RequestedBy,
                        RequestTime = request.RequestedAt,
                        Reason = request.Reason,
                        RequestedDuration = request.RequestedDuration,
                        Status = request.Status.ToString()
                    });
                }

                // Add sample recent activity (would need actual logging)
                RecentActivity.Add(new AdminLogEntry
                {
                    Timestamp = DateTime.Now.AddMinutes(-10),
                    Action = "Session created",
                    User = WindowsIdentity.GetCurrent().Name,
                    Details = "Temporary admin session created"
                });
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Data Load Error", $"Error loading admin override data: {ex.Message}"); } catch { MessageBox.Show($"Error loading admin override data: {ex.Message}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task RequestOverride()
        {
            if (string.IsNullOrWhiteSpace(OverrideReason))
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowWarning("Reason Required", "Please provide a reason for the override request."); } catch { MessageBox.Show("Please provide a reason for the override request.", "Reason Required", MessageBoxButton.OK, MessageBoxImage.Warning); }
                return;
            }

            try
            {
                var result = await _adminOverrideService.RequestAdminAccessAsync(OverrideReason, MaxOverrideDuration);
                if (result.Granted)
                {
                    IsOverrideActive = true;
                    LoadAdminOverrideData(); // Refresh data
                    try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Access Granted", "Admin access granted successfully."); } catch { MessageBox.Show("Admin access granted successfully.", "Access Granted", MessageBoxButton.OK, MessageBoxImage.Information); }
                }
                else
                {
                    try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Request Submitted", $"Admin access request submitted: {result.Message}"); } catch { MessageBox.Show($"Admin access request submitted: {result.Message}", "Request Submitted", MessageBoxButton.OK, MessageBoxImage.Information); }
                }
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Request Error", $"Override request failed: {ex.Message}"); } catch { MessageBox.Show($"Override request failed: {ex.Message}", "Request Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task ApproveRequest(OverrideRequest request)
        {
            if (request == null) return;

            try
            {
                await _adminOverrideService.ApproveAdminRequestAsync(request.RequestId, CurrentAdminUser);
                PendingRequests.Remove(request);
                LoadAdminOverrideData(); // Refresh data
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Request Approved", "Admin request approved."); } catch { MessageBox.Show("Admin request approved.", "Request Approved", MessageBoxButton.OK, MessageBoxImage.Information); }
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Approval Error", $"Approval failed: {ex.Message}"); } catch { MessageBox.Show($"Approval failed: {ex.Message}", "Approval Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task DenyRequest(OverrideRequest request)
        {
            if (request == null) return;

            try
            {
                await _adminOverrideService.DenyAdminRequestAsync(request.RequestId, CurrentAdminUser, "Denied by administrator");
                PendingRequests.Remove(request);
                LoadAdminOverrideData(); // Refresh data
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Request Denied", "Admin request denied."); } catch { MessageBox.Show("Admin request denied.", "Request Denied", MessageBoxButton.OK, MessageBoxImage.Information); }
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Deny Error", $"Deny failed: {ex.Message}"); } catch { MessageBox.Show($"Deny failed: {ex.Message}", "Deny Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task EndOverride()
        {
            try
            {
                var activeSessions = _adminOverrideService.GetActiveSessions();
                foreach (var session in activeSessions)
                {
                    try
                    {
                        await _adminOverrideService.EndAdminSessionAsync(session.SessionId).ConfigureAwait(false);
                    }
                    catch { /* swallow individual failures to continue cleanup */ }
                }

                // Update UI state after cleanup (marshal back to UI thread)
                await System.Windows.Application.Current?.Dispatcher.InvokeAsync(() =>
                {
                    IsOverrideActive = false;
                    LoadAdminOverrideData(); // Refresh data
                    try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Sessions Ended", "All admin sessions ended successfully."); } catch { MessageBox.Show("All admin sessions ended successfully.", "Sessions Ended", MessageBoxButton.OK, MessageBoxImage.Information); }
                });
            }
            catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("End Error", $"Failed to end sessions: {ex.Message}"); } catch { MessageBox.Show($"Failed to end sessions: {ex.Message}", "End Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private void ViewAdminLogs()
        {
            var logs = RecentActivity;
            if (logs.Any())
            {
                var logText = string.Join("\n", logs.Select(l => $"{l.Timestamp:yyyy-MM-dd HH:mm}: {l.Action} by {l.User} - {l.Details}"));
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Admin Logs", $"Admin Activity Logs:\n{logText}"); } catch { MessageBox.Show($"Admin Activity Logs:\n{logText}", "Admin Logs", MessageBoxButton.OK, MessageBoxImage.Information); }
            }
            else
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Admin Logs", "No recent admin activity."); } catch { MessageBox.Show("No recent admin activity.", "Admin Logs", MessageBoxButton.OK, MessageBoxImage.Information); }
            }
        }
    }

    public class OverrideSession
    {
        public string SessionId { get; set; } = string.Empty;
        public string AdminUser { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public TimeSpan Duration { get; set; }
        public string Reason { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class OverrideRequest
    {
        public string RequestId { get; set; } = string.Empty;
        public string RequestedBy { get; set; } = string.Empty;
        public DateTime RequestTime { get; set; }
        public string Reason { get; set; } = string.Empty;
        public TimeSpan RequestedDuration { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    public class AdminLogEntry
    {
        public DateTime Timestamp { get; set; }
        public string Action { get; set; } = string.Empty;
        public string User { get; set; } = string.Empty;
        public string Details { get; set; } = string.Empty;
    }
}
