﻿using System;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using PcFutureShield.UI.Services;
using PcFutureShield.Common.Services;
// Add the correct namespace for MainViewModel if it exists, for example:
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : System.Windows.Window
    {
        private readonly LoggingService _logger = LoggingService.Instance;

        public MainWindow()
        {
            try
            {
                _logger.LogInfo("MainWindow", "Application starting up");
                InitializeComponent();
                // Use the MainViewModel from the ViewModels namespace so DataTemplates in XAML match
                var vm = new PcFutureShield.UI.ViewModels.MainViewModel();
                // Subscribe to the correct event name exposed by that ViewModel
                vm.RequestThemeChange += OnRequestThemeChange;
                DataContext = vm;
                _logger.LogInfo("MainWindow", "MainWindow initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogFatal("MainWindow", "Failed to initialize MainWindow", ex);
                MessageBox.Show($"Application startup error: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Application.Current.Shutdown();
            }
        }

        private void OnRequestThemeChange(string themeName)
        {
            _logger.LogInfo("ThemeSwitching", $"Attempting to switch to theme: {themeName}");

            try
            {
                string themePath = themeName switch
                {
                    "GlossyBlue" => "Themes/GlossyBlue.xaml",
                    "GlossyGreen" => "Themes/GlossyGreen.xaml",
                    "GlossyMidnightBlue" => "Themes/GlossyMidnightBlue.xaml",
                    "GlossyPurple" => "Themes/GlossyPurple.xaml",
                    "GlossyRed" => "Themes/GlossyRed.xaml",
                    _ => "Themes/GlossyBlue.xaml"
                };

                _logger.LogDebug("ThemeSwitching", $"Loading theme from path: {themePath}");

                var dict = new ResourceDictionary { Source = new Uri(themePath, UriKind.Relative) };
                var appResources = Application.Current.Resources;

                if (appResources.MergedDictionaries.Count > 0)
                {
                    appResources.MergedDictionaries[0] = dict;
                    _logger.LogInfo("ThemeSwitching", $"Successfully replaced theme with {themeName}");
                }
                else
                {
                    appResources.MergedDictionaries.Add(dict);
                    _logger.LogInfo("ThemeSwitching", $"Successfully added theme {themeName} as first dictionary");
                }

                // Verify theme was loaded by checking for key resources
                if (dict.Contains("PrimaryColorBrush") && dict.Contains("PrimaryFontBrush"))
                {
                    _logger.LogInfo("ThemeSwitching", $"Theme {themeName} loaded successfully with required resources");
                }
                else
                {
                    _logger.LogWarning("ThemeSwitching", $"Theme {themeName} loaded but missing some expected resources");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("ThemeSwitching", $"Failed to switch to theme {themeName}", ex);

                // Try to show a more user-friendly error message
                string errorMessage = $"Failed to load theme '{themeName}':\n{ex.Message}\n\nThe application will continue with the current theme.";
                MessageBox.Show(errorMessage, "Theme Loading Error", MessageBoxButton.OK, MessageBoxImage.Warning);

                // Don't rethrow - let the app continue with current theme
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.LogInfo("MainWindow", "Opening log viewer");
                var logViewer = new PcFutureShield.UI.Views.LogViewerWindow
                {
                    Owner = this
                };
                logViewer.ShowDialog();
            }
            catch (Exception ex)
            {
                _logger.LogError("MainWindow", "Failed to open log viewer", ex);
                MessageBox.Show($"Failed to open log viewer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
