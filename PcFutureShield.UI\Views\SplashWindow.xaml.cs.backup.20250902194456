using System.Windows;
using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class SplashWindow : Window
    {
        // Backing controls in case the XAML does not declare x:Name for them.
        private TextBlock _messageText;
        private ProgressBar _progressBar;

        public SplashWindow()
        {
            InitializeComponent();

            // Ensure controls exist in case the XAML doesn't define them with x:Name
            if (_messageText == null) _messageText = new TextBlock();
            if (_progressBar == null) _progressBar = new ProgressBar();
        }

        public void SetMessage(string message)
        {
            _messageText.Text = message;
        }

        public void SetProgress(double percent)
        {
            _progressBar.Value = percent;
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void None(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
