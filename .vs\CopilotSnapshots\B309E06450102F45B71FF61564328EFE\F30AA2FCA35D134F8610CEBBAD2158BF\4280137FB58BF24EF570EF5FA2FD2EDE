﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace PcFutureShield.UI.ViewModels
{
    public class QuarantineViewModel : INotifyPropertyChanged
    {
        public ObservableCollection<string> QuarantinedFiles { get; } = new ObservableCollection<string>();

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private PcFutureShield.UI.Helpers.RelayCommand? quarantineCommand;
        public ICommand QuarantineCommand => quarantineCommand ??= new PcFutureShield.UI.Helpers.RelayCommand(Quarantine);

        private void Quarantine(object? commandParameter)
        {
            // TODO: implement quarantine action
        }

        private PcFutureShield.UI.Helpers.RelayCommand? pcOptimizationCommand;
        public ICommand PcOptimizationCommand => pcOptimizationCommand ??= new PcFutureShield.UI.Helpers.RelayCommand(PcOptimization);

        private void PcOptimization(object? commandParameter)
        {
            // TODO: implement PC optimization action
        }
    }
}
