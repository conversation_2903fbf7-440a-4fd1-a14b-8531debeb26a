﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class ThemeSelectorViewModel : BaseViewModel
    {
        private string _selectedTheme = "GlossyBlue";
        private ObservableCollection<ThemeInfo> _availableThemes;
        private bool _enableAnimations = true;
        private bool _enableGlassEffect = true;
        private double _themeOpacity = 0.9;
        private bool _autoSwitchThemes = false;
        private TimeSpan _themeSwitchInterval = TimeSpan.FromHours(1);
        private DispatcherTimer _autoSwitchTimer;
        private int _currentThemeIndex = 0;
        private readonly string[] _themeNames = { "GlossyBlue", "GlossyGreen", "GlossyMidnightBlue", "GlossyPurple", "GlossyRed", "Glossy3D" };

        public ThemeSelectorViewModel()
        {
            _availableThemes = new ObservableCollection<ThemeInfo>();
            _autoSwitchTimer = new DispatcherTimer();
            _autoSwitchTimer.Tick += AutoSwitchTimer_Tick;

            ApplyThemeCommand = new RelayCommand<string>(ApplyTheme);
            PreviewThemeCommand = new RelayCommand<string>(PreviewTheme);
            ResetToDefaultCommand = new RelayCommand(ResetToDefault);
            SaveThemeSettingsCommand = new RelayCommand(SaveThemeSettings);

            LoadAvailableThemes();
            LoadSettings();
        }

        public string SelectedTheme
        {
            get => _selectedTheme;
            set => SetProperty(ref _selectedTheme, value);
        }

        public ObservableCollection<ThemeInfo> AvailableThemes
        {
            get => _availableThemes;
            set => SetProperty(ref _availableThemes, value);
        }

        public bool EnableAnimations
        {
            get => _enableAnimations;
            set => SetProperty(ref _enableAnimations, value);
        }

        public bool EnableGlassEffect
        {
            get => _enableGlassEffect;
            set => SetProperty(ref _enableGlassEffect, value);
        }

        public double ThemeOpacity
        {
            get => _themeOpacity;
            set => SetProperty(ref _themeOpacity, value);
        }

        public bool AutoSwitchThemes
        {
            get => _autoSwitchThemes;
            set
            {
                if (SetProperty(ref _autoSwitchThemes, value))
                {
                    if (value)
                    {
                        StartAutoSwitchTimer();
                    }
                    else
                    {
                        StopAutoSwitchTimer();
                    }
                }
            }
        }

        public TimeSpan ThemeSwitchInterval
        {
            get => _themeSwitchInterval;
            set => SetProperty(ref _themeSwitchInterval, value);
        }

        public ICommand ApplyThemeCommand { get; }
        public ICommand PreviewThemeCommand { get; }
        public ICommand ResetToDefaultCommand { get; }
        public ICommand SaveThemeSettingsCommand { get; }

        private void LoadAvailableThemes()
        {
            AvailableThemes.Clear();

            // Add available themes
            AvailableThemes.Add(new ThemeInfo
            {
                Name = "GlossyBlue",
                DisplayName = "Glossy Blue",
                Description = "Modern blue theme with glossy effects",
                PreviewImage = "/Resources/Themes/GlossyBlue.png",
                IsDefault = true
            });

            AvailableThemes.Add(new ThemeInfo
            {
                Name = "GlossyGreen",
                DisplayName = "Glossy Green",
                Description = "Fresh green theme with glass effects",
                PreviewImage = "/Resources/Themes/GlossyGreen.png",
                IsDefault = false
            });

            AvailableThemes.Add(new ThemeInfo
            {
                Name = "GlossyMidnightBlue",
                DisplayName = "Midnight Blue",
                Description = "Dark blue theme for low-light environments",
                PreviewImage = "/Resources/Themes/GlossyMidnightBlue.png",
                IsDefault = false
            });

            AvailableThemes.Add(new ThemeInfo
            {
                Name = "GlossyPurple",
                DisplayName = "Glossy Purple",
                Description = "Elegant purple theme with mirror effects",
                PreviewImage = "/Resources/Themes/GlossyPurple.png",
                IsDefault = false
            });

            AvailableThemes.Add(new ThemeInfo
            {
                Name = "GlossyRed",
                DisplayName = "Glossy Red",
                Description = "Bold red theme with chrome accents",
                PreviewImage = "/Resources/Themes/GlossyRed.png",
                IsDefault = false
            });

            AvailableThemes.Add(new ThemeInfo
            {
                Name = "Glossy3D",
                DisplayName = "Glossy 3D",
                Description = "Red theme with blue 3D text shadows and effects",
                PreviewImage = "/Resources/Themes/Glossy3D.png",
                IsDefault = false
            });
        }

        private void LoadSettings()
        {
            try
            {
                // Load settings from application properties or configuration
                // For now, use default values - in a real app, you'd load from a config file or database
                // EnableAnimations = loadedValue;
                // EnableGlassEffect = loadedValue;
                // ThemeOpacity = loadedValue;
                // AutoSwitchThemes = loadedValue;
                // ThemeSwitchInterval = loadedValue;
            }
            catch (Exception ex)
            {
                // Use default values if settings can't be loaded
                System.Diagnostics.Debug.WriteLine($"Failed to load theme settings: {ex.Message}");
            }
        }

        private void ApplyTheme(string themeName)
        {
            if (string.IsNullOrEmpty(themeName)) return;

            try
            {
                SelectedTheme = themeName;

                // Apply the theme to the application using the existing theme switching mechanism
                var themeUri = new Uri($"/PcFutureShield.UI;component/Themes/{themeName}.xaml", UriKind.Relative);
                var themeResource = Application.LoadComponent(themeUri) as ResourceDictionary;

                if (themeResource != null)
                {
                    // Clear existing theme dictionaries and add the new one
                    var existingThemeDicts = Application.Current.Resources.MergedDictionaries
                        .Where(d => d.Source != null && d.Source.OriginalString.Contains("/Themes/"))
                        .ToList();

                    foreach (var dict in existingThemeDicts)
                    {
                        Application.Current.Resources.MergedDictionaries.Remove(dict);
                    }

                    Application.Current.Resources.MergedDictionaries.Add(themeResource);

                    // Update current theme index for auto-switch
                    _currentThemeIndex = Array.IndexOf(_themeNames, themeName);

                    MessageBox.Show($"Theme '{themeName}' applied successfully.", "Theme Applied", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"Failed to load theme '{themeName}'.", "Theme Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to apply theme: {ex.Message}", "Theme Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviewTheme(string themeName)
        {
            if (string.IsNullOrEmpty(themeName)) return;

            // Simple preview: apply the theme temporarily
            ApplyTheme(themeName);
            MessageBox.Show($"Theme '{themeName}' is now active. If you like it, keep it; otherwise, select another theme.", "Theme Preview", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetToDefault()
        {
            var defaultTheme = AvailableThemes.FirstOrDefault(t => t.IsDefault);
            if (defaultTheme != null)
            {
                ApplyTheme(defaultTheme.Name);
            }

            // Reset settings to defaults
            EnableAnimations = true;
            EnableGlassEffect = true;
            ThemeOpacity = 0.9;
            AutoSwitchThemes = false;
            ThemeSwitchInterval = TimeSpan.FromHours(1);
        }

        private void SaveThemeSettings()
        {
            try
            {
                // Save settings to application properties
                // For now, just show success message - in a real app, you'd save to a config file or database
                // Properties.Settings.Default.EnableAnimations = EnableAnimations;
                // Properties.Settings.Default.EnableGlassEffect = EnableGlassEffect;
                // Properties.Settings.Default.ThemeOpacity = ThemeOpacity;
                // Properties.Settings.Default.AutoSwitchThemes = AutoSwitchThemes;
                // Properties.Settings.Default.ThemeSwitchInterval = ThemeSwitchInterval;
                // Properties.Settings.Default.Save();

                MessageBox.Show("Theme settings saved successfully.", "Settings Saved", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to save theme settings: {ex.Message}", "Save Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void StartAutoSwitchTimer()
        {
            _autoSwitchTimer.Interval = ThemeSwitchInterval;
            _autoSwitchTimer.Start();
        }

        private void StopAutoSwitchTimer()
        {
            _autoSwitchTimer.Stop();
        }

        private void AutoSwitchTimer_Tick(object sender, EventArgs e)
        {
            // Switch to next theme
            _currentThemeIndex = (_currentThemeIndex + 1) % _themeNames.Length;
            var nextTheme = _themeNames[_currentThemeIndex];
            ApplyTheme(nextTheme);
        }
        public class ThemeInfo
        {
            public string Name { get; set; } = string.Empty;
            public string DisplayName { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string PreviewImage { get; set; } = string.Empty;
            public bool IsDefault { get; set; }
            public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
            protected void OnPropertyChanged(string name) =>
                PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(name));

        }
    }
}



