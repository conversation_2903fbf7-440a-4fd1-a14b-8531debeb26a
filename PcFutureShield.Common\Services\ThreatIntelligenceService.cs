#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Security.Cryptography;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced threat intelligence service with multiple threat feeds
    /// </summary>
    public class ThreatIntelligenceService
    {
        private readonly HttpClient _httpClient;
        private readonly string _threatDbPath;
        private readonly Dictionary<string, ThreatIndicator> _threatIndicators;
        private readonly List<ThreatFeed> _threatFeeds;

        public ThreatIntelligenceService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(30);

            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var threatPath = Path.Combine(appData, "PcFutureShield", "ThreatIntel");
            Directory.CreateDirectory(threatPath);
            _threatDbPath = Path.Combine(threatPath, "threat_indicators.json");

            _threatIndicators = LoadThreatIndicators();
            _threatFeeds = InitializeThreatFeeds();
        }

        public async Task<ThreatIntelligenceResult> CheckHashAsync(string hash)
        {
            var result = new ThreatIntelligenceResult { Hash = hash };

            try
            {
                // Check local database first
                if (_threatIndicators.TryGetValue(hash, out var indicator))
                {
                    result.IsMalicious = true;
                    result.ThreatName = indicator.ThreatName;
                    result.Confidence = indicator.Confidence;
                    result.Source = "Local Database";
                    result.LastSeen = indicator.LastSeen;
                    result.ThreatType = indicator.ThreatType;
                }

                // Check external feeds if not found locally
                if (!result.IsMalicious)
                {
                    foreach (var feed in _threatFeeds)
                    {
                        try
                        {
                            var feedResult = await CheckFeedAsync(feed, hash);
                            if (feedResult.IsMalicious)
                            {
                                result.IsMalicious = true;
                                result.ThreatName = feedResult.ThreatName;
                                result.Confidence = feedResult.Confidence;
                                result.Source = feed.Name;
                                result.LastSeen = feedResult.LastSeen ?? DateTime.UtcNow;
                                result.ThreatType = feedResult.ThreatType;

                                // Cache the result locally
                                _threatIndicators[hash] = new ThreatIndicator
                                {
                                    Hash = hash,
                                    ThreatName = feedResult.ThreatName,
                                    ThreatType = feedResult.ThreatType,
                                    Confidence = feedResult.Confidence,
                                    Source = feed.Name,
                                    FirstSeen = DateTime.UtcNow,
                                    LastSeen = feedResult.LastSeen ?? DateTime.UtcNow
                                };
                                SaveThreatIndicators();

                                break; // Found a match, no need to check other feeds
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error checking feed {feed.Name}: {ex.Message}");
                        }
                    }
                }

                result.CheckTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        public async Task<ThreatIntelligenceResult> CheckDomainAsync(string domain)
        {
            var result = new ThreatIntelligenceResult { Domain = domain };

            try
            {
                // Check domain reputation
                foreach (var feed in _threatFeeds.Where(f => f.SupportsDomains))
                {
                    try
                    {
                        var feedResult = await CheckDomainFeedAsync(feed, domain);
                        if (feedResult.IsMalicious)
                        {
                            result.IsMalicious = true;
                            result.ThreatName = feedResult.ThreatName;
                            result.Confidence = feedResult.Confidence;
                            result.Source = feed.Name;
                            result.ThreatType = feedResult.ThreatType;
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error checking domain feed {feed.Name}: {ex.Message}");
                    }
                }

                result.CheckTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        public async Task<ThreatIntelligenceResult> CheckIPAddressAsync(string ipAddress)
        {
            var result = new ThreatIntelligenceResult { IPAddress = ipAddress };

            try
            {
                // Check IP reputation
                foreach (var feed in _threatFeeds.Where(f => f.SupportsIPs))
                {
                    try
                    {
                        var feedResult = await CheckIPFeedAsync(feed, ipAddress);
                        if (feedResult.IsMalicious)
                        {
                            result.IsMalicious = true;
                            result.ThreatName = feedResult.ThreatName;
                            result.Confidence = feedResult.Confidence;
                            result.Source = feed.Name;
                            result.ThreatType = feedResult.ThreatType;
                            break;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error checking IP feed {feed.Name}: {ex.Message}");
                    }
                }

                result.CheckTime = DateTime.UtcNow;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        public async Task UpdateThreatFeedsAsync()
        {
            Console.WriteLine("Updating threat intelligence feeds...");

            foreach (var feed in _threatFeeds)
            {
                try
                {
                    var indicators = await DownloadFeedAsync(feed);
                    foreach (var indicator in indicators)
                    {
                        _threatIndicators[indicator.Hash] = indicator;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error updating feed {feed.Name}: {ex.Message}");
                }
            }

            SaveThreatIndicators();
            Console.WriteLine($"Updated {_threatIndicators.Count} threat indicators");
        }

        public ThreatStatistics GetStatistics()
        {
            var stats = new ThreatStatistics
            {
                TotalIndicators = _threatIndicators.Count,
                LastUpdate = GetLastUpdateTime(),
                Sources = _threatIndicators.Values
                    .GroupBy(i => i.Source)
                    .ToDictionary(g => g.Key, g => g.Count()),
                ThreatTypes = _threatIndicators.Values
                    .GroupBy(i => i.ThreatType)
                    .ToDictionary(g => g.Key, g => g.Count())
            };

            return stats;
        }

        private List<ThreatFeed> InitializeThreatFeeds()
        {
            return new List<ThreatFeed>
            {
                new ThreatFeed
                {
                    Name = "MalwareBazaar",
                    Url = "https://mb-api.abuse.ch/api/v1/",
                    SupportsHashes = true,
                    SupportsDomains = false,
                    SupportsIPs = false,
                    ApiKey = null // Public API
                },
                new ThreatFeed
                {
                    Name = "VirusTotal",
                    Url = "https://www.virustotal.com/api/v3/",
                    SupportsHashes = true,
                    SupportsDomains = true,
                    SupportsIPs = true,
                    ApiKey = Environment.GetEnvironmentVariable("VIRUSTOTAL_API_KEY")
                },
                new ThreatFeed
                {
                    Name = "AbuseIPDB",
                    Url = "https://api.abuseipdb.com/api/v2/",
                    SupportsHashes = false,
                    SupportsDomains = false,
                    SupportsIPs = true,
                    ApiKey = Environment.GetEnvironmentVariable("ABUSEIPDB_API_KEY")
                },
                new ThreatFeed
                {
                    Name = "PhishTank",
                    Url = "https://phishtank.com/api/",
                    SupportsHashes = false,
                    SupportsDomains = true,
                    SupportsIPs = false,
                    ApiKey = null // Public API
                }
            };
        }

        private async Task<ThreatIntelligenceResult> CheckFeedAsync(ThreatFeed feed, string hash)
        {
            var result = new ThreatIntelligenceResult { Hash = hash };

            // This is a simplified implementation
            // In a real system, you would implement the actual API calls for each feed

            switch (feed.Name)
            {
                case "MalwareBazaar":
                    result = await CheckMalwareBazaarAsync(hash);
                    break;
                case "VirusTotal":
                    result = await CheckVirusTotalAsync(hash);
                    break;
                default:
                    result.IsMalicious = false;
                    break;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckDomainFeedAsync(ThreatFeed feed, string domain)
        {
            var result = new ThreatIntelligenceResult { Domain = domain };

            switch (feed.Name)
            {
                case "VirusTotal":
                    result = await CheckVirusTotalDomainAsync(domain);
                    break;
                case "PhishTank":
                    result = await CheckPhishTankAsync(domain);
                    break;
                default:
                    result.IsMalicious = false;
                    break;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckIPFeedAsync(ThreatFeed feed, string ipAddress)
        {
            var result = new ThreatIntelligenceResult { IPAddress = ipAddress };

            switch (feed.Name)
            {
                case "AbuseIPDB":
                    result = await CheckAbuseIPDBAsync(ipAddress);
                    break;
                case "VirusTotal":
                    result = await CheckVirusTotalIPAsync(ipAddress);
                    break;
                default:
                    result.IsMalicious = false;
                    break;
            }

            return result;
        }

        // Simplified feed checking implementations
        private async Task<ThreatIntelligenceResult> CheckMalwareBazaarAsync(string hash)
        {
            var result = new ThreatIntelligenceResult { Hash = hash, Source = "MalwareBazaar" };

            try
            {
                // MalwareBazaar supports a POST API; we perform a best-effort query and parse presence of results.
                var uri = "https://mb-api.abuse.ch/api/v1/";
                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("query", "get_info"),
                    new KeyValuePair<string, string>("hash", hash)
                });

                using var resp = await _httpClient.PostAsync(uri, content).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode)
                {
                    return result; // couldn't reach feed; conservatively return non-malicious
                }

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                if (string.IsNullOrWhiteSpace(body)) return result;

                try
                {
                    using var doc = JsonDocument.Parse(body);
                    if (doc.RootElement.TryGetProperty("data", out var data) && data.ValueKind == JsonValueKind.Array && data.GetArrayLength() > 0)
                    {
                        // MalwareBazaar returned entries for the hash -> treat as malicious
                        result.IsMalicious = true;
                        result.ThreatName = hash;
                        result.Confidence = 0.9;
                        result.LastSeen = DateTime.UtcNow;
                    }
                }
                catch
                {
                    // non-JSON or unexpected shape - ignore parsing errors
                }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckVirusTotalAsync(string hash)
        {
            var result = new ThreatIntelligenceResult { Hash = hash, Source = "VirusTotal" };

            try
            {
                var apiKey = _threatFeeds.FirstOrDefault(f => f.Name == "VirusTotal")?.ApiKey;
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    result.Error = "VirusTotal API key not configured";
                    return result;
                }

                var requestUri = $"https://www.virustotal.com/api/v3/files/{Uri.EscapeDataString(hash)}";
                using var req = new HttpRequestMessage(HttpMethod.Get, requestUri);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);

                using var resp = await _httpClient.SendAsync(req).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode)
                {
                    result.Error = $"VT API returned {(int)resp.StatusCode}";
                    return result;
                }

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                if (string.IsNullOrWhiteSpace(body)) return result;

                try
                {
                    using var doc = JsonDocument.Parse(body);
                    if (doc.RootElement.TryGetProperty("data", out var data) && data.TryGetProperty("attributes", out var attrs))
                    {
                        if (attrs.TryGetProperty("last_analysis_stats", out var stats) && stats.TryGetProperty("malicious", out var maliciousCount))
                        {
                            if (maliciousCount.GetInt32() > 0)
                            {
                                result.IsMalicious = true;
                                result.Confidence = Math.Min(0.9, maliciousCount.GetInt32() / 100.0);
                            }
                        }
                    }
                }
                catch
                {
                    // parsing error; ignore and return conservative result
                }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckVirusTotalDomainAsync(string domain)
        {
            var result = new ThreatIntelligenceResult { Domain = domain, Source = "VirusTotal" };

            try
            {
                var apiKey = _threatFeeds.FirstOrDefault(f => f.Name == "VirusTotal")?.ApiKey;
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    result.Error = "VirusTotal API key not configured";
                    return result;
                }

                var requestUri = $"https://www.virustotal.com/api/v3/domains/{Uri.EscapeDataString(domain)}";
                using var req = new HttpRequestMessage(HttpMethod.Get, requestUri);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);

                using var resp = await _httpClient.SendAsync(req).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode)
                {
                    result.Error = $"VT API returned {(int)resp.StatusCode}";
                    return result;
                }

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                if (string.IsNullOrWhiteSpace(body)) return result;

                try
                {
                    using var doc = JsonDocument.Parse(body);
                    if (doc.RootElement.TryGetProperty("data", out var data) && data.TryGetProperty("attributes", out var attrs))
                    {
                        if (attrs.TryGetProperty("last_analysis_stats", out var stats) && stats.TryGetProperty("malicious", out var maliciousCount))
                        {
                            if (maliciousCount.GetInt32() > 0)
                            {
                                result.IsMalicious = true;
                                result.Confidence = Math.Min(0.9, maliciousCount.GetInt32() / 100.0);
                            }
                        }
                    }
                }
                catch { }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckPhishTankAsync(string domain)
        {
            var result = new ThreatIntelligenceResult { Domain = domain, Source = "PhishTank" };

            try
            {
                // PhishTank requires API access for authoritative checks. We perform a best-effort HTTP GET against the site
                var requestUri = $"https://phishtank.org/check/{Uri.EscapeDataString(domain)}";
                using var resp = await _httpClient.GetAsync(requestUri).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode) return result;

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                if (body.Contains("phish", StringComparison.OrdinalIgnoreCase) || body.Contains("verified", StringComparison.OrdinalIgnoreCase))
                {
                    result.IsMalicious = true;
                    result.Confidence = 0.7;
                }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckAbuseIPDBAsync(string ipAddress)
        {
            var result = new ThreatIntelligenceResult { IPAddress = ipAddress, Source = "AbuseIPDB" };

            try
            {
                var apiKey = _threatFeeds.FirstOrDefault(f => f.Name == "AbuseIPDB")?.ApiKey;
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    result.Error = "AbuseIPDB API key not configured";
                    return result;
                }

                var requestUri = $"https://api.abuseipdb.com/api/v2/check?ipAddress={Uri.EscapeDataString(ipAddress)}&maxAgeInDays=90";
                using var req = new HttpRequestMessage(HttpMethod.Get, requestUri);
                req.Headers.Add("Key", apiKey);
                req.Headers.Add("Accept", "application/json");

                using var resp = await _httpClient.SendAsync(req).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode)
                {
                    result.Error = $"AbuseIPDB returned {(int)resp.StatusCode}";
                    return result;
                }

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    using var doc = JsonDocument.Parse(body);
                    if (doc.RootElement.TryGetProperty("data", out var data) && data.TryGetProperty("abuseConfidenceScore", out var scoreEl))
                    {
                        var score = scoreEl.GetInt32();
                        result.Confidence = score / 100.0;
                        result.IsMalicious = score >= 50;
                    }
                }
                catch { }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckVirusTotalIPAsync(string ipAddress)
        {
            var result = new ThreatIntelligenceResult { IPAddress = ipAddress, Source = "VirusTotal" };

            try
            {
                var apiKey = _threatFeeds.FirstOrDefault(f => f.Name == "VirusTotal")?.ApiKey;
                if (string.IsNullOrWhiteSpace(apiKey))
                {
                    result.Error = "VirusTotal API key not configured";
                    return result;
                }

                var requestUri = $"https://www.virustotal.com/api/v3/ip_addresses/{Uri.EscapeDataString(ipAddress)}";
                using var req = new HttpRequestMessage(HttpMethod.Get, requestUri);
                req.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", apiKey);

                using var resp = await _httpClient.SendAsync(req).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode)
                {
                    result.Error = $"VT API returned {(int)resp.StatusCode}";
                    return result;
                }

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                try
                {
                    using var doc = JsonDocument.Parse(body);
                    if (doc.RootElement.TryGetProperty("data", out var data) && data.TryGetProperty("attributes", out var attrs))
                    {
                        if (attrs.TryGetProperty("last_analysis_stats", out var stats) && stats.TryGetProperty("malicious", out var maliciousCount))
                        {
                            if (maliciousCount.GetInt32() > 0)
                            {
                                result.IsMalicious = true;
                                result.Confidence = Math.Min(0.9, maliciousCount.GetInt32() / 100.0);
                            }
                        }
                    }
                }
                catch { }
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<List<ThreatIndicator>> DownloadFeedAsync(ThreatFeed feed)
        {
            var results = new List<ThreatIndicator>();

            try
            {
                // Attempt a best-effort fetch of the feed URL. Many feeds require API-specific parsing; we keep this generic.
                using var resp = await _httpClient.GetAsync(feed.Url).ConfigureAwait(false);
                if (!resp.IsSuccessStatusCode) return results;

                var body = await resp.Content.ReadAsStringAsync().ConfigureAwait(false);
                if (string.IsNullOrWhiteSpace(body)) return results;

                // Very conservative parsing: look for common hash-like strings and add as indicators with low confidence.
                foreach (System.Text.RegularExpressions.Match m in System.Text.RegularExpressions.Regex.Matches(body, "\b[a-fA-F0-9]{32,64}\b"))
                {
                    var h = m.Value;
                    results.Add(new ThreatIndicator { Hash = h, ThreatName = "feed-import", Confidence = 0.3, FirstSeen = DateTime.UtcNow, LastSeen = DateTime.UtcNow, ThreatType = "Unknown", Source = feed.Name });
                }
            }
            catch
            {
                // ignore feed parsing errors
            }

            return results;
        }

        private Dictionary<string, ThreatIndicator> LoadThreatIndicators()
        {
            if (!File.Exists(_threatDbPath))
            {
                return new Dictionary<string, ThreatIndicator>();
            }

            try
            {
                var json = File.ReadAllText(_threatDbPath);
                var indicators = JsonSerializer.Deserialize<List<ThreatIndicator>>(json) ?? new List<ThreatIndicator>();
                return indicators.ToDictionary(i => i.Hash);
            }
            catch
            {
                return new Dictionary<string, ThreatIndicator>();
            }
        }

        private void SaveThreatIndicators()
        {
            try
            {
                var indicators = _threatIndicators.Values.ToList();
                var json = JsonSerializer.Serialize(indicators, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_threatDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving threat indicators: {ex.Message}");
            }
        }

        private DateTime GetLastUpdateTime()
        {
            try
            {
                if (_threatIndicators.Count == 0)
                    return DateTime.MinValue;

                return _threatIndicators.Values.Max(i => i.LastSeen);
            }
            catch
            {
                return DateTime.MinValue;
            }
        }
    }

    // Supporting classes
    public class ThreatIntelligenceResult
    {
        public string Hash { get; set; } = string.Empty;
        public string Domain { get; set; } = string.Empty;
        public string IPAddress { get; set; } = string.Empty;
        public bool IsMalicious { get; set; }
        public string ThreatName { get; set; } = string.Empty;
        public string ThreatType { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public string Source { get; set; } = string.Empty;
        public DateTime? LastSeen { get; set; }
        public DateTime CheckTime { get; set; }
        public string Error { get; set; } = string.Empty;
    }

    public class ThreatIndicator
    {
        public string Hash { get; set; } = string.Empty;
        public string ThreatName { get; set; } = string.Empty;
        public string ThreatType { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public string Source { get; set; } = string.Empty;
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
    }

    public class ThreatFeed
    {
        public string Name { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public bool SupportsHashes { get; set; }
        public bool SupportsDomains { get; set; }
        public bool SupportsIPs { get; set; }
        public string ApiKey { get; set; } = string.Empty;
    }

    public class ThreatStatistics
    {
        public int TotalIndicators { get; set; }
        public DateTime LastUpdate { get; set; }
        public Dictionary<string, int> Sources { get; set; } = new();
        public Dictionary<string, int> ThreatTypes { get; set; } = new();
    }
}
