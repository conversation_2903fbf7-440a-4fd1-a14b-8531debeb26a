using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Input;
using PcFutureShield.UI.Services;
using PcFutureShield.UI.Helpers;

namespace PcFutureShield.UI.Views
{
    public partial class LogViewerWindow : Window
    {
        private readonly LoggingService _loggingService = LoggingService.Instance;

        public LogViewerWindow()
        {
            InitializeComponent();
            DataContext = new LogViewerViewModel(_loggingService);
            _loggingService.LogInfo("LogViewer", "Log viewer window opened");
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            _loggingService.LogInfo("LogViewer", "Log viewer window closed");
            Close();
        }
    }

    public class LogViewerViewModel : INotifyPropertyChanged
    {
        private readonly LoggingService _loggingService;
        private string _logContent;

        public event PropertyChangedEventHandler? PropertyChanged;

        public LogViewerViewModel(LoggingService loggingService)
        {
            _loggingService = loggingService;
            LogFilePath = _loggingService.GetLogFilePath();
            RefreshLogContent();

            RefreshCommand = new RelayCommand(_ => RefreshCommand_Execute());
            ClearLogCommand = new RelayCommand(_ => ClearLogCommand_Execute());
        }

        public string LogFilePath { get; }
        public string LogContent
        {
            get => _logContent;
            private set
            {
                _logContent = value;
                OnPropertyChanged();
            }
        }

        public ICommand RefreshCommand { get; }
        public ICommand ClearLogCommand { get; }

        public void RefreshLogContent()
        {
            LogContent = _loggingService.GetLogContent();
        }

        private void RefreshCommand_Execute()
        {
            RefreshLogContent();
            _loggingService.LogInfo("LogViewer", "Log content refreshed");
        }

        private void ClearLogCommand_Execute()
        {
            _loggingService.ClearLog();
            RefreshLogContent();
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
