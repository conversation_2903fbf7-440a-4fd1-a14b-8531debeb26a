using Microsoft.Data.Sqlite;
using Microsoft.Extensions.Logging;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace PcFutureShield.Engine.Scanning
{
    public class SqliteSignatureDatabase : ISignatureDatabase
    {
        private readonly string _dbPath;
        private readonly ILogger<SqliteSignatureDatabase> _logger;
        private SqliteConnection? _connection;
        private bool _disposed;

        public SqliteSignatureDatabase(
            ILogger<SqliteSignatureDatabase> logger,
            string? dbPath = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dbPath = dbPath ?? Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData),
                "PcFutureShield",
                "Signatures",
                "signatures.db");

            // Ensure directory exists
            var directory = Path.GetDirectoryName(_dbPath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }

        public async Task<bool> InitializeAsync(CancellationToken ct = default)
        {
            try
            {
                _connection = new SqliteConnection($"Data Source={_dbPath}");
                await _connection.OpenAsync(ct);

                await CreateTablesIfNotExistAsync(ct);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize signature database");
                return false;
            }
        }

        public async Task<bool> UpdateAsync(CancellationToken ct = default)
        {
            try
            {
                _logger.LogInformation("Updating virus signatures...");
                using var httpClient = new System.Net.Http.HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(30);

                // Real update: download latest signatures from trusted source
                var response = await httpClient.GetAsync("https://api.pcfutureShield.com/signatures/latest", ct);
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync(ct);
                var signatures = System.Text.Json.JsonSerializer.Deserialize<List<SignatureUpdate>>(json);

                if (signatures != null && signatures.Count > 0)
                {
                    await InsertSignaturesAsync(signatures, ct);
                    _logger.LogInformation("Updated {Count} signatures", signatures.Count);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update signature database");
                return false;
            }
        }

        public async Task<ThreatSignature?> LookupSignatureAsync(string signature, CancellationToken ct = default)
        {
            if (_connection == null)
                throw new InvalidOperationException("Database not initialized");

            try
            {
                using var command = _connection.CreateCommand();
                command.CommandText = @"
                    SELECT Name, Type, Severity
                    FROM Signatures
                    WHERE Hash = @hash AND IsActive = 1";
                command.Parameters.AddWithValue("@hash", signature);

                using var reader = await command.ExecuteReaderAsync(ct);
                if (await reader.ReadAsync(ct))
                {
                    return new ThreatSignature
                    {
                        Name = reader.GetString(0),
                        Type = reader.GetString(1),
                        Severity = (ThreatSeverity)reader.GetInt32(2)
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error looking up signature");
                return null;
            }
        }

        private async Task CreateTablesIfNotExistAsync(CancellationToken ct)
        {
            if (_connection == null) return;

            using var command = _connection.CreateCommand();
            command.CommandText = @"
                CREATE TABLE IF NOT EXISTS Signatures (
                    Id TEXT PRIMARY KEY,
                    Hash TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    Type TEXT NOT NULL,
                    Severity INTEGER NOT NULL,
                    FirstSeen TEXT NOT NULL,
                    LastUpdated TEXT NOT NULL,
                    IsActive INTEGER DEFAULT 1,
                    UNIQUE(Hash)
                );

                CREATE INDEX IF NOT EXISTS IX_Signatures_Hash ON Signatures(Hash);";

            await command.ExecuteNonQueryAsync(ct);
        }

        private async Task InsertSignaturesAsync(List<SignatureUpdate> signatures, CancellationToken ct)
        {
            if (_connection == null) return;

            using var transaction = _connection.BeginTransaction();
            try
            {
                foreach (var sig in signatures)
                {
                    using var command = _connection.CreateCommand();
                    command.CommandText = @"
                        INSERT OR REPLACE INTO Signatures (Id, Hash, Name, Type, Severity, FirstSeen, LastUpdated, IsActive)
                        VALUES (@id, @hash, @name, @type, @severity, @firstSeen, @lastUpdated, 1)";
                    command.Parameters.AddWithValue("@id", Guid.NewGuid().ToString());
                    command.Parameters.AddWithValue("@hash", sig.Hash);
                    command.Parameters.AddWithValue("@name", sig.Name);
                    command.Parameters.AddWithValue("@type", sig.Type);
                    command.Parameters.AddWithValue("@severity", (int)sig.Severity);
                    command.Parameters.AddWithValue("@firstSeen", sig.FirstSeen.ToString("O"));
                    command.Parameters.AddWithValue("@lastUpdated", DateTime.UtcNow.ToString("O"));

                    await command.ExecuteNonQueryAsync(ct);
                }
                transaction.Commit();
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        public class SignatureUpdate
        {
            public string Hash { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public ThreatSeverity Severity { get; set; }
            public DateTime FirstSeen { get; set; }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _connection?.Dispose();
                _disposed = true;
            }
        }
    }
}
