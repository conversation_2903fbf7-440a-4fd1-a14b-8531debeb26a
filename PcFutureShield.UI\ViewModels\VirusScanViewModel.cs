﻿using System;
using System.Collections.ObjectModel;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;

using PcFutureShield.Engine.VirusScanner;
using System.Linq;

namespace PcFutureShield.UI.ViewModels
{
    // Define the IVirusScannerService interface if not already defined elsewhere
    public interface IVirusScannerService
    {
        Task<ScanResult> ScanAsync(string[] paths, CancellationToken cancellationToken);
    }

    // Adapter to wrap Engine.VirusScannerService for use in UI
    public class VirusScannerServiceAdapter : IVirusScannerService
    {
        private readonly VirusScannerService _engineService;

        public VirusScannerServiceAdapter(VirusScannerService engineService)
        {
            _engineService = engineService ?? throw new ArgumentNullException(nameof(engineService));
        }

        public async Task<ScanResult> ScanAsync(string[] paths, CancellationToken cancellationToken)
        {
            var result = await _engineService.ScanAsync(paths, cancellationToken);
            return new ScanResult
            {
                FilesScanned = result.FilesScanned,
                Duration = result.Duration,
                Threats = result.Threats.Select(t => new ThreatInfo
                {
                    FilePath = t.FilePath,
                    ThreatName = t.ThreatName,
                    DetectedAt = t.DetectedAt,
                    IsQuarantined = t.IsQuarantined
                }).ToList()
            };
        }
    }

    // Define the ScanResult and ThreatInfo classes if not already defined elsewhere
    public class ScanResult
    {
        public int FilesScanned { get; set; }
        public TimeSpan Duration { get; set; }
        public List<ThreatInfo> Threats { get; set; } = new();
    }

    public class ThreatInfo
    {
        public string FilePath { get; set; } = string.Empty;
        public string ThreatName { get; set; } = string.Empty;
        public DateTime DetectedAt { get; set; } = DateTime.Now;
        public bool IsQuarantined { get; set; } = false;
    }

    public class VirusScanViewModel : INotifyPropertyChanged
    {
        private readonly IVirusScannerService _scannerService;
        private CancellationTokenSource? _cts;

        public ObservableCollection<ThreatInfo> Threats { get; } = new();
        public int FilesScanned { get => _filesScanned; set { _filesScanned = value; OnPropertyChanged(); } }
        private int _filesScanned;
        public TimeSpan Duration { get => _duration; set { _duration = value; OnPropertyChanged(); } }
        private TimeSpan _duration;
        public bool IsScanning { get => _isScanning; set { _isScanning = value; OnPropertyChanged(); } }
        private bool _isScanning;
        public string Status { get => _status; set { _status = value; OnPropertyChanged(); } }
        private string _status = "Ready";

        public ICommand StartScanCommand { get; }
        public ICommand CancelScanCommand { get; }

        public VirusScanViewModel(IVirusScannerService scannerService)
        {
            _scannerService = scannerService;
            StartScanCommand = new AsyncRelayCommand(StartScanAsync, () => !IsScanning);
            CancelScanCommand = new RelayCommand(CancelScan, () => IsScanning);
        }

        public async Task StartScanAsync()
        {
            IsScanning = true;
            Status = "Scanning...";
            Threats.Clear();
            FilesScanned = 0;
            Duration = TimeSpan.Zero;
            _cts = new CancellationTokenSource();
            try
            {
                var result = await _scannerService.ScanAsync(new[] { Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) }, _cts.Token);
                foreach (var t in result.Threats)
                    Threats.Add(t);
                FilesScanned = result.FilesScanned;
                Duration = result.Duration;
                Status = $"Scan complete. {Threats.Count} threats found.";
            }
            catch (OperationCanceledException)
            {
                Status = "Scan cancelled.";
            }
            catch (Exception ex)
            {
                Status = $"Error: {ex.Message}";
            }
            finally
            {
                IsScanning = false;
            }
        }

        public void CancelScan()
        {
            _cts?.Cancel();
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? name = null) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }



}

