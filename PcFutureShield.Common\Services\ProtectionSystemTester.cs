#nullable enable

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Net.NetworkInformation;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Comprehensive testing service to verify all protection systems are working correctly
    /// </summary>
    public class ProtectionSystemTester
    {
        private readonly ParentalControlService _parentalControl;
        private readonly GamingProtectionService _gamingProtection;
        private readonly DnsFilteringService _dnsFilter;
        private readonly HttpClient _httpClient;

        public ProtectionSystemTester()
        {
            _parentalControl = new ParentalControlService();
            _gamingProtection = new GamingProtectionService();
            _dnsFilter = new DnsFilteringService();
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
        }

        /// <summary>
        /// Run comprehensive tests on all protection systems
        /// </summary>
        public async Task<ProtectionTestResults> RunAllTestsAsync()
        {
            var results = new ProtectionTestResults();

            Console.WriteLine("=== PcFutureShield Protection System Tests ===");
            Console.WriteLine();

            // Test 1: DNS Filtering
            Console.WriteLine("Testing DNS Filtering...");
            results.DnsFilteringResults = await TestDnsFilteringAsync();
            PrintTestResults("DNS Filtering", results.DnsFilteringResults);

            // Test 2: Hosts File Blocking
            Console.WriteLine("Testing Hosts File Blocking...");
            results.HostsFileResults = await TestHostsFileBlockingAsync();
            PrintTestResults("Hosts File Blocking", results.HostsFileResults);

            // Test 3: Network Blocking
            Console.WriteLine("Testing Network-Level Blocking...");
            results.NetworkBlockingResults = await TestNetworkBlockingAsync();
            PrintTestResults("Network Blocking", results.NetworkBlockingResults);

            // Test 4: Gaming Protection
            Console.WriteLine("Testing Gaming Protection...");
            results.GamingProtectionResults = await TestGamingProtectionAsync();
            PrintTestResults("Gaming Protection", results.GamingProtectionResults);

            // Test 5: Real Website Blocking
            Console.WriteLine("Testing Real Website Blocking...");
            results.WebsiteBlockingResults = await TestRealWebsiteBlockingAsync();
            PrintTestResults("Website Blocking", results.WebsiteBlockingResults);

            // Overall Results
            Console.WriteLine();
            Console.WriteLine("=== OVERALL RESULTS ===");
            var totalTests = results.GetTotalTests();
            var passedTests = results.GetPassedTests();
            Console.WriteLine($"Total Tests: {totalTests}");
            Console.WriteLine($"Passed: {passedTests}");
            Console.WriteLine($"Failed: {totalTests - passedTests}");
            Console.WriteLine($"Success Rate: {(double)passedTests / totalTests * 100:F1}%");

            if (passedTests == totalTests)
            {
                Console.WriteLine("✅ ALL PROTECTION SYSTEMS ARE WORKING CORRECTLY!");
            }
            else
            {
                Console.WriteLine("❌ SOME PROTECTION SYSTEMS NEED ATTENTION!");
            }

            return results;
        }

        private async Task<TestResult> TestDnsFilteringAsync()
        {
            var result = new TestResult { TestName = "DNS Filtering" };

            try
            {
                // Check if DNS filtering is active
                var isActive = await _dnsFilter.IsFilteringActiveAsync();
                result.AddTest("DNS Filtering Active", isActive, "DNS filtering should be active");

                // Check DNS servers
                var dnsServers = GetCurrentDnsServers();
                var hasFamilySafeDns = dnsServers.Any(dns => 
                    dns.Contains("208.67.222.123") || // OpenDNS FamilyShield
                    dns.Contains("185.228.168.168") || // CleanBrowsing
                    dns.Contains("9.9.9.10")); // Quad9 Filtered
                
                result.AddTest("Family-Safe DNS Configured", hasFamilySafeDns, 
                    $"Current DNS: {string.Join(", ", dnsServers)}");

                // Test blocking categories
                await _dnsFilter.BlockCategoryAsync("adult");
                await _dnsFilter.BlockCategoryAsync("gambling");
                
                var blockedDomains = _dnsFilter.GetBlockedDomains();
                result.AddTest("Adult Content Domains Blocked", blockedDomains.Count > 0, 
                    $"Blocked domains: {blockedDomains.Count}");
            }
            catch (Exception ex)
            {
                result.AddTest("DNS Filtering Exception", false, ex.Message);
            }

            return result;
        }

        private async Task<TestResult> TestHostsFileBlockingAsync()
        {
            var result = new TestResult { TestName = "Hosts File Blocking" };

            try
            {
                var hostsPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), 
                    "drivers", "etc", "hosts");

                if (File.Exists(hostsPath))
                {
                    var hostsContent = await File.ReadAllTextAsync(hostsPath);
                    
                    result.AddTest("Hosts File Exists", true, "Hosts file found");
                    result.AddTest("PcFutureShield Entries Present", 
                        hostsContent.Contains("# PcFutureShield DNS Filter"),
                        "PcFutureShield entries in hosts file");

                    // Count blocked domains
                    var blockedCount = hostsContent.Split('\n')
                        .Count(line => line.Contains("# PcFutureShield DNS Filter") && 
                                      line.Contains("127.0.0.1"));
                    
                    result.AddTest("Domains Blocked in Hosts", blockedCount > 0, 
                        $"Blocked entries: {blockedCount}");
                }
                else
                {
                    result.AddTest("Hosts File Exists", false, "Hosts file not found");
                }
            }
            catch (Exception ex)
            {
                result.AddTest("Hosts File Exception", false, ex.Message);
            }

            return result;
        }

        private async Task<TestResult> TestNetworkBlockingAsync()
        {
            var result = new TestResult { TestName = "Network Blocking" };

            try
            {
                // Enable network blocking
                var enabled = await _parentalControl.EnableNetworkBlockingAsync();
                result.AddTest("Network Blocking Enabled", enabled, "Network blocking activation");

                // Test domain blocking
                var testDomain = "example-adult-site.com";
                var blocked = await _parentalControl.BlockDomainAsync(testDomain);
                result.AddTest("Domain Blocking Works", blocked, $"Blocking {testDomain}");

                // Test category blocking
                var categoryBlocked = await _parentalControl.BlockCategoryAsync("adult");
                result.AddTest("Category Blocking Works", categoryBlocked, "Blocking adult category");
            }
            catch (Exception ex)
            {
                result.AddTest("Network Blocking Exception", false, ex.Message);
            }

            return result;
        }

        private async Task<TestResult> TestGamingProtectionAsync()
        {
            var result = new TestResult { TestName = "Gaming Protection" };

            try
            {
                // Start gaming protection monitoring
                await _gamingProtection.StartMonitoringAsync();
                result.AddTest("Gaming Monitoring Started", true, "Gaming protection monitoring active");

                // Test process detection (simulate)
                var currentProcesses = Process.GetProcesses();
                var gamingProcesses = currentProcesses.Where(p => 
                    p.ProcessName.ToLowerInvariant().Contains("steam") ||
                    p.ProcessName.ToLowerInvariant().Contains("game") ||
                    p.ProcessName.ToLowerInvariant().Contains("epic")).ToList();

                result.AddTest("Gaming Process Detection", true, 
                    $"Detected {gamingProcesses.Count} potential gaming processes");

                // Test gambling detection
                var testGambling = await _gamingProtection.AnalyzeCasinoAsync("https://example-casino.com", "Test Casino");
                result.AddTest("Gambling Detection Works", testGambling != null, "Casino analysis functional");
            }
            catch (Exception ex)
            {
                result.AddTest("Gaming Protection Exception", false, ex.Message);
            }

            return result;
        }

        private async Task<TestResult> TestRealWebsiteBlockingAsync()
        {
            var result = new TestResult { TestName = "Real Website Blocking" };

            // Test actual adult sites (these should be blocked)
            var testSites = new[]
            {
                "pornhub.com",
                "xvideos.com", 
                "bet365.com",
                "pokerstars.com"
            };

            foreach (var site in testSites)
            {
                try
                {
                    var response = await _httpClient.GetAsync($"http://{site}");
                    
                    // If we get here, the site wasn't blocked at DNS level
                    // Check if it's a blocked response
                    var isBlocked = response.StatusCode == System.Net.HttpStatusCode.NotFound ||
                                   response.RequestMessage?.RequestUri?.Host == "127.0.0.1";
                    
                    result.AddTest($"Block {site}", isBlocked, 
                        $"Status: {response.StatusCode}, Host: {response.RequestMessage?.RequestUri?.Host}");
                }
                catch (HttpRequestException)
                {
                    // Connection failed - likely blocked at DNS level (good!)
                    result.AddTest($"Block {site}", true, "DNS blocking successful");
                }
                catch (TaskCanceledException)
                {
                    // Timeout - likely blocked (good!)
                    result.AddTest($"Block {site}", true, "Request timeout (likely blocked)");
                }
                catch (Exception ex)
                {
                    result.AddTest($"Block {site}", false, $"Unexpected error: {ex.Message}");
                }
            }

            return result;
        }

        private List<string> GetCurrentDnsServers()
        {
            var dnsServers = new List<string>();

            try
            {
                var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                    .Where(ni => ni.OperationalStatus == OperationalStatus.Up);

                foreach (var ni in networkInterfaces)
                {
                    var ipProps = ni.GetIPProperties();
                    foreach (var dns in ipProps.DnsAddresses)
                    {
                        dnsServers.Add(dns.ToString());
                    }
                }
            }
            catch (Exception ex)
            {
                dnsServers.Add($"Error getting DNS: {ex.Message}");
            }

            return dnsServers.Distinct().ToList();
        }

        private void PrintTestResults(string category, TestResult result)
        {
            Console.WriteLine($"--- {category} ---");
            foreach (var test in result.Tests)
            {
                var status = test.Passed ? "✅ PASS" : "❌ FAIL";
                Console.WriteLine($"{status} - {test.Name}: {test.Details}");
            }
            Console.WriteLine();
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    public class ProtectionTestResults
    {
        public TestResult DnsFilteringResults { get; set; } = new();
        public TestResult HostsFileResults { get; set; } = new();
        public TestResult NetworkBlockingResults { get; set; } = new();
        public TestResult GamingProtectionResults { get; set; } = new();
        public TestResult WebsiteBlockingResults { get; set; } = new();

        public int GetTotalTests()
        {
            return DnsFilteringResults.Tests.Count + HostsFileResults.Tests.Count + 
                   NetworkBlockingResults.Tests.Count + GamingProtectionResults.Tests.Count +
                   WebsiteBlockingResults.Tests.Count;
        }

        public int GetPassedTests()
        {
            return DnsFilteringResults.Tests.Count(t => t.Passed) + 
                   HostsFileResults.Tests.Count(t => t.Passed) +
                   NetworkBlockingResults.Tests.Count(t => t.Passed) + 
                   GamingProtectionResults.Tests.Count(t => t.Passed) +
                   WebsiteBlockingResults.Tests.Count(t => t.Passed);
        }
    }

    public class TestResult
    {
        public string TestName { get; set; } = "";
        public List<TestCase> Tests { get; set; } = new();

        public void AddTest(string name, bool passed, string details)
        {
            Tests.Add(new TestCase { Name = name, Passed = passed, Details = details });
        }
    }

    public class TestCase
    {
        public string Name { get; set; } = "";
        public bool Passed { get; set; }
        public string Details { get; set; } = "";
    }
}
