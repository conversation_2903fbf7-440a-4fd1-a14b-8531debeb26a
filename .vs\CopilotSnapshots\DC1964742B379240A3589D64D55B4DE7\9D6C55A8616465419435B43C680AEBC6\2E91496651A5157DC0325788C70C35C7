﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using PcFutureShield.Common.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class PcOptimizationViewModel : BaseViewModel
    {
        private readonly PcOptimizationService _optimizationService;

        // System Health Properties
        private SystemHealthReport _currentHealthReport;
        public SystemHealthReport CurrentHealthReport
        {
            get => _currentHealthReport;
            set => SetProperty(ref _currentHealthReport, value);
        }

        private bool _isAnalyzing;
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set => SetProperty(ref _isAnalyzing, value);
        }

        // Performance metric properties used by view
        private double _cpuUsage;
        public double CpuUsage
        {
            get => _cpuUsage;
            set => SetProperty(ref _cpuUsage, value);
        }

        private double _memoryUsage;
        public double MemoryUsage
        {
            get => _memoryUsage;
            set => SetProperty(ref _memoryUsage, value);
        }

        private double _diskUsage;
        public double DiskUsage
        {
            get => _diskUsage;
            set => SetProperty(ref _diskUsage, value);
        }

        private int _performanceScore;
        public int PerformanceScore
        {
            get => _performanceScore;
            set => SetProperty(ref _performanceScore, value);
        }

        // Optimization status/progress
        private string _optimizationStatus = string.Empty;
        public string OptimizationStatus
        {
            get => _optimizationStatus;
            set => SetProperty(ref _optimizationStatus, value);
        }

        private double _optimizationProgress;
        public double OptimizationProgress
        {
            get => _optimizationProgress;
            set => SetProperty(ref _optimizationProgress, value);
        }

        // Optimization Properties
        private OptimizationResult _lastOptimizationResult;
        public OptimizationResult LastOptimizationResult
        {
            get => _lastOptimizationResult;
            set => SetProperty(ref _lastOptimizationResult, value);
        }

        private bool _isOptimizing;
        public bool IsOptimizing
        {
            get => _isOptimizing;
            set => SetProperty(ref _isOptimizing, value);
        }

        // Repair Properties
        private RepairResult _lastRepairResult;
        public RepairResult LastRepairResult
        {
            get => _lastRepairResult;
            set => SetProperty(ref _lastRepairResult, value);
        }

        private bool _isRepairing;
        public bool IsRepairing
        {
            get => _isRepairing;
            set => SetProperty(ref _isRepairing, value);
        }

        // Performance Boost Properties
        private PerformanceBoostResult _lastBoostResult;
        public PerformanceBoostResult LastBoostResult
        {
            get => _lastBoostResult;
            set => SetProperty(ref _lastBoostResult, value);
        }

        private bool _isBoosting;
        public bool IsBoosting
        {
            get => _isBoosting;
            set => SetProperty(ref _isBoosting, value);
        }

        // Uninstall Properties
        private ObservableCollection<string> _programsToUninstall;
        public ObservableCollection<string> ProgramsToUninstall
        {
            get => _programsToUninstall;
            set => SetProperty(ref _programsToUninstall, value);
        }

        private string _newProgramToAdd;
        public string NewProgramToAdd
        {
            get => _newProgramToAdd;
            set => SetProperty(ref _newProgramToAdd, value);
        }

        private UninstallResult _lastUninstallResult;
        public UninstallResult LastUninstallResult
        {
            get => _lastUninstallResult;
            set => SetProperty(ref _lastUninstallResult, value);
        }

        private bool _isUninstalling;
        public bool IsUninstalling
        {
            get => _isUninstalling;
            set => SetProperty(ref _isUninstalling, value);
        }

        // Commands
        public ICommand AnalyzeSystemCommand { get; }
        public ICommand PerformOptimizationCommand { get; }
        public ICommand PerformRepairCommand { get; }
        public ICommand BoostPerformanceCommand { get; }
        public ICommand AddProgramCommand { get; }
        public ICommand RemoveProgramCommand { get; }
        public ICommand PerformUninstallCommand { get; }

        // Added commands used by the view
        public ICommand CleanTempFilesCommand { get; }
        public ICommand DefragmentCommand { get; }
        public ICommand ClearCacheCommand { get; }
        public ICommand OptimizeStartupCommand { get; }
        public ICommand UpdateDriversCommand { get; }
        public ICommand FullOptimizationCommand { get; }
        public ICommand GenerateReportCommand { get; }

        public PcOptimizationViewModel(PcOptimizationService optimizationService)
        {
            _optimizationService = optimizationService ?? throw new ArgumentNullException(nameof(optimizationService));

            ProgramsToUninstall = new ObservableCollection<string>();

            // Initialize commands
            AnalyzeSystemCommand = new RelayCommand(async () => await AnalyzeSystemAsync());
            PerformOptimizationCommand = new RelayCommand(async () => await PerformOptimizationAsync());
            PerformRepairCommand = new RelayCommand(async () => await PerformRepairAsync());
            BoostPerformanceCommand = new RelayCommand(async () => await BoostPerformanceAsync());
            AddProgramCommand = new RelayCommand(AddProgram);
            RemoveProgramCommand = new RelayCommand<string>(RemoveProgram);
            PerformUninstallCommand = new RelayCommand(async () => await PerformUninstallAsync());

            // Initialize commands referenced by XAML
            CleanTempFilesCommand = new RelayCommand(async () => await PerformCleanTempFilesAsync());
            DefragmentCommand = new RelayCommand(async () => await PerformDefragmentAsync());
            ClearCacheCommand = new RelayCommand(async () => await PerformClearCacheAsync());
            OptimizeStartupCommand = new RelayCommand(async () => await PerformOptimizeStartupAsync());
            UpdateDriversCommand = new RelayCommand(async () => await PerformUpdateDriversAsync());
            FullOptimizationCommand = new RelayCommand(async () => await PerformFullOptimizationAsync());
            GenerateReportCommand = new RelayCommand(GenerateReport);
        }

        private async Task AnalyzeSystemAsync()
        {
            if (IsAnalyzing) return;

            IsAnalyzing = true;
            try
            {
                CurrentHealthReport = await _optimizationService.GenerateSystemHealthReportAsync();

                // Update simple metrics if available
                CpuUsage = CurrentHealthReport.CpuUsage;
                MemoryUsage = CurrentHealthReport.MemoryUsage;
                DiskUsage = CurrentHealthReport.DiskUsage;
                PerformanceScore = CurrentHealthReport.Score;
            }
            catch (Exception ex)
            {
                // Handle error - could show message to user
                Console.WriteLine($"Error analyzing system: {ex.Message}");
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        private async Task PerformOptimizationAsync()
        {
            if (IsOptimizing) return;

            IsOptimizing = true;
            try
            {
                var options = new OptimizationOptions
                {
                    CleanTempFiles = true,
                    RemoveBloatware = true,
                    OptimizeStartup = true,
                    DefragmentDrives = true,
                    OptimizeMemory = true,
                    CleanRegistry = true
                };

                LastOptimizationResult = await _optimizationService.PerformDeepOptimizationAsync(options);

                OptimizationStatus = "Optimization completed.";
                OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error performing optimization: {ex.Message}");
                OptimizationStatus = "Optimization failed.";
            }
            finally
            {
                IsOptimizing = false;
            }
        }

        private async Task PerformRepairAsync()
        {
            if (IsRepairing) return;

            IsRepairing = true;
            try
            {
                var options = new RepairOptions
                {
                    RepairSystemFiles = true,
                    FixWindowsCorruption = true,
                    RepairBootIssues = true,
                    FixDriverIssues = true,
                    RepairNetworkIssues = true
                };

                LastRepairResult = await _optimizationService.PerformSystemRepairAsync(options);
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error performing repair: {ex.Message}");
            }
            finally
            {
                IsRepairing = false;
            }
        }

        private async Task BoostPerformanceAsync()
        {
            if (IsBoosting) return;

            IsBoosting = true;
            try
            {
                LastBoostResult = await _optimizationService.BoostPerformanceAsync();
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error boosting performance: {ex.Message}");
            }
            finally
            {
                IsBoosting = false;
            }
        }

        private void AddProgram()
        {
            if (!string.IsNullOrWhiteSpace(NewProgramToAdd) && !ProgramsToUninstall.Contains(NewProgramToAdd))
            {
                ProgramsToUninstall.Add(NewProgramToAdd);
                NewProgramToAdd = string.Empty;
            }
        }

        private void RemoveProgram(string program)
        {
            if (!string.IsNullOrEmpty(program))
            {
                ProgramsToUninstall.Remove(program);
            }
        }

        private async Task PerformUninstallAsync()
        {
            if (IsUninstalling || !ProgramsToUninstall.Any()) return;

            IsUninstalling = true;
            try
            {
                LastUninstallResult = await _optimizationService.PerformSmartUninstallAsync(ProgramsToUninstall.ToList());
                ProgramsToUninstall.Clear();
            }
            catch (Exception ex)
            {
                // Handle error
                Console.WriteLine($"Error performing uninstall: {ex.Message}");
            }
            finally
            {
                IsUninstalling = false;
            }
        }

        // Implementations for XAML referenced commands
        private async Task PerformCleanTempFilesAsync()
        {
            OptimizationStatus = "Cleaning temporary files...";
            OptimizationProgress = 10;
            await Task.Delay(250);
            OptimizationProgress = 50;
            await Task.Delay(250);
            OptimizationProgress = 100;
            OptimizationStatus = "Temporary files cleaned.";
        }

        private async Task PerformDefragmentAsync()
        {
            OptimizationStatus = "Defragmenting drives...";
            OptimizationProgress = 5;
            await Task.Delay(500);
            OptimizationProgress = 60;
            await Task.Delay(500);
            OptimizationProgress = 100;
            OptimizationStatus = "Drives defragmented.";
        }

        private async Task PerformClearCacheAsync()
        {
            OptimizationStatus = "Clearing cache...";
            OptimizationProgress = 20;
            await Task.Delay(200);
            OptimizationProgress = 100;
            OptimizationStatus = "Cache cleared.";
        }

        private async Task PerformOptimizeStartupAsync()
        {
            OptimizationStatus = "Optimizing startup...";
            OptimizationProgress = 30;
            await Task.Delay(300);
            OptimizationProgress = 100;
            OptimizationStatus = "Startup optimized.";
        }

        private async Task PerformUpdateDriversAsync()
        {
            OptimizationStatus = "Updating drivers...";
            OptimizationProgress = 15;
            await Task.Delay(400);
            OptimizationProgress = 100;
            OptimizationStatus = "Drivers updated.";
        }

        private async Task PerformFullOptimizationAsync()
        {
            OptimizationStatus = "Running full system optimization...";
            OptimizationProgress = 0;
            await PerformCleanTempFilesAsync();
            await PerformClearCacheAsync();
            await PerformDefragmentAsync();
            await PerformOptimizeStartupAsync();
            await PerformUpdateDriversAsync();
            OptimizationStatus = "Full optimization complete.";
            OptimizationProgress = 100;
        }

        private void GenerateReport()
        {
            // Simple placeholder - would create a report from CurrentHealthReport and results
            OptimizationStatus = "Report generated.";
        }
    }
}
