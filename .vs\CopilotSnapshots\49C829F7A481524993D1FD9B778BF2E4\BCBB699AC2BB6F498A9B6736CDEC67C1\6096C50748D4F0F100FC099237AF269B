﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="/PcFutureShield.UI;component/Styles/SharedStyles.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Define Color resources so GradientStop.Color and Effect.Color can reference actual Color values -->
    <Color x:Key="PrimaryColor">#FF00BFFF</Color>
    <Color x:Key="SecondaryColor">#FF1E90FF</Color>
    <Color x:Key="AccentColor">#FF00FFFF</Color>
    <Color x:Key="BackgroundColor">#FF0A0A0A</Color>
    <Color x:Key="SidebarBackgroundColor">#FF1A1A2E</Color>
    <Color x:Key="CardBackgroundColor">#FF16213E</Color>
    <Color x:Key="PrimaryFontColor">#FF00FFFF</Color>
    <Color x:Key="SecondaryFontColor">#FF87CEEB</Color>
    <Color x:Key="SuccessColor">#FF00FF7F</Color>
    <Color x:Key="WarningColor">#FFFFD700</Color>
    <Color x:Key="ErrorColor">#FFFF4444</Color>
    <Color x:Key="BorderColor">#FF00BFFF</Color>

    <!-- Keep existing SolidColorBrush resources but reference the Color resources -->
    <SolidColorBrush x:Key="PrimaryColorBrush" Color="{StaticResource PrimaryColor}"/>
    <SolidColorBrush x:Key="SecondaryColorBrush" Color="{StaticResource SecondaryColor}"/>
    <SolidColorBrush x:Key="AccentColorBrush" Color="{StaticResource AccentColor}"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
    <SolidColorBrush x:Key="SidebarBackgroundBrush" Color="{StaticResource SidebarBackgroundColor}"/>
    <SolidColorBrush x:Key="CardBackgroundBrush" Color="{StaticResource CardBackgroundColor}"/>
    <SolidColorBrush x:Key="PrimaryFontBrush" Color="{StaticResource PrimaryFontColor}"/>
    <SolidColorBrush x:Key="SecondaryFontBrush" Color="{StaticResource SecondaryFontColor}"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="{StaticResource SuccessColor}"/>
    <SolidColorBrush x:Key="WarningBrush" Color="{StaticResource WarningColor}"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="{StaticResource ErrorColor}"/>
    <SolidColorBrush x:Key="BorderBrush" Color="{StaticResource BorderColor}"/>

    <!-- Futuristic Chrome Button Style -->
    <Style x:Key="ChromeButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Glow Background -->
                        <Border x:Name="GlowBorder" CornerRadius="8" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="{StaticResource PrimaryColor}" Offset="0"/>
                                    <GradientStop Color="{StaticResource PrimaryColor}" Offset="0.5"/>
                                    <GradientStop Color="{StaticResource PrimaryColor}" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.Effect>
                                <DropShadowEffect Color="{StaticResource PrimaryColor}" Direction="270" ShadowDepth="0" BlurRadius="15" Opacity="0.6"/>
                            </Border.Effect>
                        </Border>

                        <!-- Inner Chrome Layer -->
                        <Border CornerRadius="7" Margin="1" Background="{DynamicResource CardBackgroundBrush}"/>

                        <!-- Holographic Overlay -->
                        <Border CornerRadius="7" Margin="1">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#40FFFFFF" Offset="0"/>
                                    <GradientStop Color="#20FFFFFF" Offset="0.3"/>
                                    <GradientStop Color="#10FFFFFF" Offset="0.7"/>
                                    <GradientStop Color="#00FFFFFF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Cyber Grid Pattern -->
                        <Border CornerRadius="7" Margin="1" Opacity="0.1">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,20,20" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="#FF00FFFF">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <LineGeometry StartPoint="0,0" EndPoint="20,0"/>
                                                    <LineGeometry StartPoint="0,0" EndPoint="0,20"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True"/>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="GlowBorder" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#FF00FFFF" Direction="270" ShadowDepth="0" BlurRadius="25" Opacity="0.9"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="GlowBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#5500FFFF" Offset="0"/>
                                        <GradientStop Color="#4400FFFF" Offset="0.5"/>
                                        <GradientStop Color="#3300FFFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="GlowBorder" Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#FF00BFFF" Direction="270" ShadowDepth="2" BlurRadius="10" Opacity="0.8"/>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="GlowBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#6600BFFF" Offset="0"/>
                                        <GradientStop Color="#5500BFFF" Offset="0.5"/>
                                        <GradientStop Color="#4400BFFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Futuristic Mirror Button Style -->
    <Style x:Key="MirrorButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Mirror Base -->
                        <Border x:Name="MirrorBorder" CornerRadius="10" BorderThickness="2">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#FF00BFFF" Offset="0"/>
                                    <GradientStop Color="#FF0080FF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                            <Border.Background>
                                <RadialGradientBrush Center="0.5,0.3" RadiusX="1.2" RadiusY="1.2">
                                    <GradientStop Color="#FF1A1A2E" Offset="0"/>
                                    <GradientStop Color="#FF16213E" Offset="0.4"/>
                                    <GradientStop Color="#FF0F3460" Offset="0.7"/>
                                    <GradientStop Color="#FF0A0A0A" Offset="1"/>
                                </RadialGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Holographic Reflection -->
                        <Border CornerRadius="10" Margin="2">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#4000FFFF" Offset="0"/>
                                    <GradientStop Color="#2000FFFF" Offset="0.3"/>
                                    <GradientStop Color="#1000FFFF" Offset="0.7"/>
                                    <GradientStop Color="#0000FFFF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Inner Glow -->
                        <Border x:Name="InnerGlow" CornerRadius="8" Margin="3">
                            <Border.Background>
                                <RadialGradientBrush Center="0.5,0.2" RadiusX="0.8" RadiusY="0.8">
                                    <GradientStop Color="#3000BFFF" Offset="0"/>
                                    <GradientStop Color="#2000BFFF" Offset="0.5"/>
                                    <GradientStop Color="#0000BFFF" Offset="1"/>
                                </RadialGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Cyber Circuit Pattern -->
                        <Border CornerRadius="8" Margin="4" Opacity="0.3">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,15,15" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="#FF00FFFF">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <RectangleGeometry Rect="2,2,11,11"/>
                                                    <LineGeometry StartPoint="7,2" EndPoint="7,13"/>
                                                    <LineGeometry StartPoint="2,7" EndPoint="13,7"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True"/>

                        <!-- Outer Glow Effect -->
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="#FF00BFFF" Direction="270" ShadowDepth="5" BlurRadius="12" Opacity="0.5"/>
                            </Grid.Effect>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="MirrorBorder" Property="Background">
                                <Setter.Value>
                                    <RadialGradientBrush Center="0.5,0.3" RadiusX="1.2" RadiusY="1.2">
                                        <GradientStop Color="#FF1E1E3E" Offset="0"/>
                                        <GradientStop Color="#FF1A2E4E" Offset="0.4"/>
                                        <GradientStop Color="#FF0F4A80" Offset="0.7"/>
                                        <GradientStop Color="#FF0A1A2A" Offset="1"/>
                                    </RadialGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="InnerGlow" Property="Background">
                                <Setter.Value>
                                    <RadialGradientBrush Center="0.5,0.2" RadiusX="0.8" RadiusY="0.8">
                                        <GradientStop Color="#5000FFFF" Offset="0"/>
                                        <GradientStop Color="#3000FFFF" Offset="0.5"/>
                                        <GradientStop Color="#1000FFFF" Offset="1"/>
                                    </RadialGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#FF00FFFF" Direction="270" ShadowDepth="8" BlurRadius="20" Opacity="0.8"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="MirrorBorder" Property="Background">
                                <Setter.Value>
                                    <RadialGradientBrush Center="0.5,0.7" RadiusX="1.2" RadiusY="1.2">
                                        <GradientStop Color="#FF0A0A0A" Offset="0"/>
                                        <GradientStop Color="#FF0F3460" Offset="0.4"/>
                                        <GradientStop Color="#FF16213E" Offset="0.7"/>
                                        <GradientStop Color="#FF1A1A2E" Offset="1"/>
                                    </RadialGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#FF00BFFF" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.7"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Futuristic Glass Button Style -->
    <Style x:Key="GlassButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="150"/>
        <Setter Property="Height" Value="45"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Grid>
                        <!-- Cyber Glass Background -->
                        <Border x:Name="GlassBorder" CornerRadius="12" BorderThickness="1" BorderBrush="{DynamicResource BorderBrush}">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#4000FFFF" Offset="0"/>
                                    <GradientStop Color="#3000BFFF" Offset="0.1"/>
                                    <GradientStop Color="#2000BFFF" Offset="0.5"/>
                                    <GradientStop Color="#1000BFFF" Offset="0.9"/>
                                    <GradientStop Color="#3000FFFF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Holographic Top Highlight -->
                        <Border CornerRadius="12,12,0,0" Height="18" VerticalAlignment="Top" Margin="1,1,1,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <GradientStop Color="#6000FFFF" Offset="0"/>
                                    <GradientStop Color="#2000FFFF" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                        </Border>

                        <!-- Inner Glass Layer -->
                        <Border CornerRadius="11" Margin="1" Background="{DynamicResource CardBackgroundBrush}" Opacity="0.8"/>

                        <!-- Cyber Grid Overlay -->
                        <Border CornerRadius="11" Margin="1" Opacity="0.2">
                            <Border.Background>
                                <DrawingBrush Viewport="0,0,10,10" ViewportUnits="Absolute" TileMode="Tile">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing Brush="#FF00BFFF">
                                            <GeometryDrawing.Geometry>
                                                <GeometryGroup>
                                                    <LineGeometry StartPoint="0,0" EndPoint="10,0"/>
                                                    <LineGeometry StartPoint="0,0" EndPoint="0,10"/>
                                                </GeometryGroup>
                                            </GeometryDrawing.Geometry>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Border.Background>
                        </Border>

                        <!-- Content with Glow -->
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True">
                            <ContentPresenter.Effect>
                                <DropShadowEffect Color="#FF00FFFF" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.8"/>
                            </ContentPresenter.Effect>
                        </ContentPresenter>

                        <!-- Outer Glow -->
                        <Grid>
                            <Grid.Effect>
                                <DropShadowEffect Color="#FF00BFFF" Direction="270" ShadowDepth="0" BlurRadius="15" Opacity="0.6"/>
                            </Grid.Effect>
                        </Grid>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#5000FFFF" Offset="0"/>
                                        <GradientStop Color="#4000FFFF" Offset="0.1"/>
                                        <GradientStop Color="#3000FFFF" Offset="0.5"/>
                                        <GradientStop Color="#2000FFFF" Offset="0.9"/>
                                        <GradientStop Color="#4000FFFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                            <Setter Property="Effect">
                                <Setter.Value>
                                    <DropShadowEffect Color="#FF00FFFF" Direction="270" ShadowDepth="0" BlurRadius="25" Opacity="0.9"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>

                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="GlassBorder" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <GradientStop Color="#3000BFFF" Offset="0"/>
                                        <GradientStop Color="#2000BFFF" Offset="0.1"/>
                                        <GradientStop Color="#4000BFFF" Offset="0.5"/>
                                        <GradientStop Color="#5000BFFF" Offset="0.9"/>
                                        <GradientStop Color="#6000BFFF" Offset="1"/>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Futuristic Chrome Background Style -->
    <Style x:Key="ChromeBackgroundStyle" TargetType="Border">
        <Setter Property="CornerRadius" Value="16"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#FF1A1A2E" Offset="0"/>
                    <GradientStop Color="#FF16213E" Offset="0.2"/>
                    <GradientStop Color="#FF0F3460" Offset="0.5"/>
                    <GradientStop Color="#FF0A0A0A" Offset="0.8"/>
                    <GradientStop Color="#FF0A0A0A" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="#FF00BFFF" Direction="270" ShadowDepth="8" BlurRadius="20" Opacity="0.4"/>
            </Setter.Value>
        </Setter>
    </Style>
    <!-- Default Styles for Theme Consistency -->
    <Style TargetType="Button">
        <Setter Property="Background" Value="{DynamicResource PrimaryColorBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="ButtonBorder" CornerRadius="6" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource SecondaryColorBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="ButtonBorder" Property="Background" Value="{DynamicResource AccentColorBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontFamily" Value="Segoe UI"/>
    </Style>

    <Style TargetType="TextBox">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="5"/>
    </Style>

    <Style TargetType="Window">
        <Setter Property="Background" Value="{DynamicResource BackgroundBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource PrimaryFontBrush}"/>
    </Style>

    <Style TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource CardBackgroundBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource BorderBrush}"/>
    </Style>
</ResourceDictionary>
