using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;

namespace PcFutureShield.Engine.VirusScanner
{
    /// <summary>
    /// Manages a simple SQLite signature database with an import path for JSON signature packs.
    /// Schema: Signatures(Hash TEXT PRIMARY KEY, ThreatName TEXT, Metadata TEXT)
    /// </summary>
    public class SignatureDatabase
    {
        private readonly string _dbPath;

        public SignatureDatabase(string dbPath)
        {
            _dbPath = dbPath ?? throw new ArgumentNullException(nameof(dbPath));
            Directory.CreateDirectory(Path.GetDirectoryName(_dbPath) ?? ".");
            EnsureSchema();
        }

        private void EnsureSchema()
        {
            using var conn = new SqliteConnection($"Data Source={_dbPath}");
            conn.Open();
            using var cmd = conn.CreateCommand();
            cmd.CommandText = @"CREATE TABLE IF NOT EXISTS Signatures(
                                    Hash TEXT PRIMARY KEY,
                                    ThreatName TEXT NOT NULL,
                                    Metadata TEXT
                                );
                                CREATE INDEX IF NOT EXISTS idx_signatures_hash ON Signatures(Hash);";
            cmd.ExecuteNonQuery();
        }

        public async Task ImportFromJsonAsync(string jsonFilePath, CancellationToken cancellationToken = default)
        {
            if (!File.Exists(jsonFilePath)) throw new FileNotFoundException("Signature pack not found", jsonFilePath);

            await using var fs = File.OpenRead(jsonFilePath);
            using var doc = await JsonDocument.ParseAsync(fs, cancellationToken: cancellationToken);

            var entries = new List<(string Hash, string ThreatName, string? Metadata)>();
            if (doc.RootElement.ValueKind == JsonValueKind.Array)
            {
                foreach (var el in doc.RootElement.EnumerateArray())
                {
                    try
                    {
                        var hash = el.GetProperty("hash").GetString() ?? string.Empty;
                        var name = el.GetProperty("threat_name").GetString() ?? string.Empty;
                        var meta = el.TryGetProperty("metadata", out var m) ? m.GetRawText() : null;
                        if (!string.IsNullOrWhiteSpace(hash) && !string.IsNullOrWhiteSpace(name))
                            entries.Add((hash.ToLowerInvariant(), name, meta));
                    }
                    catch { /* skip malformed entries */ }
                }
            }

            // Bulk insert within a transaction
            await using var conn = new SqliteConnection($"Data Source={_dbPath}");
            await conn.OpenAsync(cancellationToken);
            await using var tx = await conn.BeginTransactionAsync(cancellationToken);
            var cmd = conn.CreateCommand();
            cmd.CommandText = "INSERT OR REPLACE INTO Signatures(Hash, ThreatName, Metadata) VALUES(@h, @n, @m);";
            var pH = cmd.CreateParameter(); pH.ParameterName = "@h"; cmd.Parameters.Add(pH);
            var pN = cmd.CreateParameter(); pN.ParameterName = "@n"; cmd.Parameters.Add(pN);
            var pM = cmd.CreateParameter(); pM.ParameterName = "@m"; cmd.Parameters.Add(pM);

            foreach (var e in entries)
            {
                cancellationToken.ThrowIfCancellationRequested();
                pH.Value = e.Hash;
                pN.Value = e.ThreatName;
                pM.Value = (object?)e.Metadata ?? DBNull.Value;
                await cmd.ExecuteNonQueryAsync(cancellationToken);
            }

            await tx.CommitAsync(cancellationToken);
        }

        public async Task<string?> GetThreatNameAsync(string hash, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(hash)) return null;
            hash = hash.ToLowerInvariant();
            await using var conn = new SqliteConnection($"Data Source={_dbPath}");
            await conn.OpenAsync(cancellationToken);
            var cmd = conn.CreateCommand();
            cmd.CommandText = "SELECT ThreatName FROM Signatures WHERE Hash = @h LIMIT 1";
            cmd.Parameters.AddWithValue("@h", hash);
            var result = await cmd.ExecuteScalarAsync(cancellationToken);
            return result?.ToString();
        }
    }
}
