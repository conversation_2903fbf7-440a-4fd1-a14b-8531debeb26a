using System;
using System.Threading.Tasks;
using PcFutureShield.Common.Services;

class DirectTest
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("=== DIRECT PARENTAL CONTROL TEST ===");
        Console.WriteLine("Testing real blocking functionality...\n");
        
        try
        {
            var parentalService = new ParentalControlService();
            
            Console.WriteLine("1. Enabling network blocking...");
            var enableResult = await parentalService.EnableNetworkBlockingAsync();
            Console.WriteLine($"   Result: {(enableResult ? "SUCCESS" : "FAILED")}");
            
            if (enableResult)
            {
                Console.WriteLine("\n2. Blocking adult content category...");
                var blockAdult = await parentalService.BlockCategoryAsync("adult");
                Console.WriteLine($"   Result: {(blockAdult ? "SUCCESS" : "FAILED")}");
                
                Console.WriteLine("\n3. Blocking gambling category...");
                var blockGambling = await parentalService.BlockCategoryAsync("gambling");
                Console.WriteLine($"   Result: {(blockGambling ? "SUCCESS" : "FAILED")}");
                
                Console.WriteLine("\n4. Checking hosts file...");
                var hostsPath = System.IO.Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers", "etc", "hosts");
                if (System.IO.File.Exists(hostsPath))
                {
                    var content = await System.IO.File.ReadAllTextAsync(hostsPath);
                    var hasBlocking = content.Contains("# PcFutureShield DNS Filter");
                    Console.WriteLine($"   Hosts file modified: {(hasBlocking ? "YES" : "NO")}");
                    
                    if (hasBlocking)
                    {
                        Console.WriteLine("   Blocked domains found in hosts file:");
                        var lines = content.Split('\n');
                        foreach (var line in lines)
                        {
                            if (line.Contains("# PcFutureShield DNS Filter") && line.Contains("127.0.0.1"))
                            {
                                Console.WriteLine($"     {line.Trim()}");
                            }
                        }
                    }
                }
                else
                {
                    Console.WriteLine("   ERROR: Hosts file not found!");
                }
            }
            
            Console.WriteLine("\n=== REAL CONNECTIVITY TEST ===");
            Console.WriteLine("Testing if blocked sites are actually blocked...\n");
            
            var testDomains = new[] { "pornhub.com", "bet365.com", "xvideos.com" };
            
            using (var httpClient = new System.Net.Http.HttpClient())
            {
                httpClient.Timeout = TimeSpan.FromSeconds(5);
                
                foreach (var domain in testDomains)
                {
                    Console.Write($"Testing {domain}... ");
                    try
                    {
                        var response = await httpClient.GetAsync($"http://{domain}");
                        Console.WriteLine($"ACCESSIBLE (Status: {response.StatusCode}) - BLOCKING FAILED!");
                    }
                    catch (System.Net.Http.HttpRequestException ex)
                    {
                        Console.WriteLine($"BLOCKED (Connection failed) - SUCCESS!");
                    }
                    catch (TaskCanceledException)
                    {
                        Console.WriteLine($"BLOCKED (Timeout) - SUCCESS!");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"ERROR: {ex.Message}");
                    }
                }
            }
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"ERROR: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
