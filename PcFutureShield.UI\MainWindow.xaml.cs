﻿using System;
using System.Windows;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainViewModel();
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logWindow = new Views.LogViewerWindow();
                logWindow.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to open log viewer: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
