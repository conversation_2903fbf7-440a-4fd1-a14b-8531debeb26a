#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using Microsoft.Win32;
using System.Security.Cryptography;
using System.Text.Json;
using System.Threading.Tasks;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced behavioral analysis service for threat detection
    /// </summary>
    public class BehavioralAnalysisService
    {
        private readonly string _behaviorDbPath;
        private readonly Dictionary<string, ProcessBehavior> _behaviorDatabase;

        public BehavioralAnalysisService()
        {
            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var behaviorPath = Path.Combine(appData, "PcFutureShield", "Behavior");
            Directory.CreateDirectory(behaviorPath);
            _behaviorDbPath = Path.Combine(behaviorPath, "behavior_db.json");
            _behaviorDatabase = LoadBehaviorDatabase();
        }

        public async Task<BehavioralAnalysisResult> AnalyzeProcessAsync(Process process)
        {
            var result = new BehavioralAnalysisResult
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Analyze process behavior patterns
                result.MemoryUsage = await AnalyzeMemoryUsageAsync(process);
                result.NetworkActivity = await AnalyzeNetworkActivityAsync(process);
                result.FileSystemActivity = await AnalyzeFileSystemActivityAsync(process);
                result.RegistryActivity = await AnalyzeRegistryActivityAsync(process);
                result.ApiCalls = await AnalyzeApiCallsAsync(process);

                // Calculate behavioral score
                result.BehavioralScore = CalculateBehavioralScore(result);

                // Determine risk level
                result.RiskLevel = DetermineRiskLevel(result.BehavioralScore);

                // Detect suspicious patterns
                result.SuspiciousPatterns = DetectSuspiciousPatterns(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.RiskLevel = ThreatRisk.Unknown;
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        private async Task<MemoryAnalysis> AnalyzeMemoryUsageAsync(Process process)
        {
            var analysis = new MemoryAnalysis();

            try
            {
                analysis.WorkingSet = process.WorkingSet64;
                analysis.PrivateMemory = process.PrivateMemorySize64;
                analysis.VirtualMemory = process.VirtualMemorySize64;

                // Check for memory anomalies
                if (analysis.WorkingSet > 500 * 1024 * 1024) // 500MB
                    analysis.Anomalies.Add("High memory usage");

                if (analysis.PrivateMemory > analysis.WorkingSet * 2)
                    analysis.Anomalies.Add("High private memory ratio");

                // Check for memory allocation patterns
                var memoryInfo = await GetMemoryInfoAsync(process);
                analysis.AllocationPattern = memoryInfo.AllocationPattern;
                analysis.ExecutableRegions = memoryInfo.ExecutableRegions;

            }
            catch (Exception ex)
            {
                analysis.Anomalies.Add($"Memory analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<NetworkAnalysis> AnalyzeNetworkActivityAsync(Process process)
        {
            var analysis = new NetworkAnalysis();

            try
            {
                // Analyze network connections (simplified)
                analysis.HasNetworkAccess = await CheckNetworkAccessAsync(process);
                analysis.ConnectionCount = await GetConnectionCountAsync(process);
                analysis.SuspiciousPorts = await DetectSuspiciousPortsAsync(process);

                if (analysis.ConnectionCount > 10)
                    analysis.Anomalies.Add("High connection count");

                if (analysis.SuspiciousPorts.Count > 0)
                    analysis.Anomalies.Add($"Suspicious ports: {string.Join(", ", analysis.SuspiciousPorts)}");

            }
            catch (Exception ex)
            {
                analysis.Anomalies.Add($"Network analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<FileSystemAnalysis> AnalyzeFileSystemActivityAsync(Process process)
        {
            var analysis = new FileSystemAnalysis();

            try
            {
                // Analyze file system access patterns
                analysis.AccessedFiles = await GetAccessedFilesAsync(process);
                analysis.ModifiedFiles = await GetModifiedFilesAsync(process);
                analysis.CreatedFiles = await GetCreatedFilesAsync(process);

                // Check for suspicious file access
                var suspiciousPaths = new[] { "system32", "config", "windows", "program files" };
                foreach (var file in analysis.AccessedFiles)
                {
                    var filePath = file.ToLowerInvariant();
                    if (suspiciousPaths.Any(path => filePath.Contains(path)))
                    {
                        analysis.Anomalies.Add($"Access to sensitive path: {file}");
                    }
                }

                if (analysis.CreatedFiles.Count > 5)
                    analysis.Anomalies.Add("High file creation rate");

            }
            catch (Exception ex)
            {
                analysis.Anomalies.Add($"File system analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<RegistryAnalysis> AnalyzeRegistryActivityAsync(Process process)
        {
            var analysis = new RegistryAnalysis();

            try
            {
                // Analyze registry access patterns
                analysis.AccessedKeys = await GetAccessedRegistryKeysAsync(process);
                analysis.ModifiedKeys = await GetModifiedRegistryKeysAsync(process);

                // Check for suspicious registry access
                var suspiciousKeys = new[] {
                    @"HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                    @"HKLM\SYSTEM\CurrentControlSet\Services",
                    @"HKCU\Software\Microsoft\Windows\CurrentVersion\Run"
                };

                foreach (var key in analysis.AccessedKeys)
                {
                    if (suspiciousKeys.Any(suspicious => key.Contains(suspicious)))
                    {
                        analysis.Anomalies.Add($"Access to sensitive registry key: {key}");
                    }
                }

            }
            catch (Exception ex)
            {
                analysis.Anomalies.Add($"Registry analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<ApiAnalysis> AnalyzeApiCallsAsync(Process process)
        {
            var analysis = new ApiAnalysis();

            try
            {
                // Analyze API call patterns (simplified)
                analysis.SuspiciousCalls = await DetectSuspiciousApiCallsAsync(process);
                analysis.FrequencyAnalysis = await AnalyzeApiFrequencyAsync(process);

                if (analysis.SuspiciousCalls.Count > 0)
                    analysis.Anomalies.Add($"Suspicious API calls: {string.Join(", ", analysis.SuspiciousCalls)}");

            }
            catch (Exception ex)
            {
                analysis.Anomalies.Add($"API analysis error: {ex.Message}");
            }

            return analysis;
        }

        private double CalculateBehavioralScore(BehavioralAnalysisResult result)
        {
            var score = 0.0;

            // Memory anomalies
            score += result.MemoryUsage.Anomalies.Count * 0.2;

            // Network anomalies
            score += result.NetworkActivity.Anomalies.Count * 0.3;

            // File system anomalies
            score += result.FileSystemActivity.Anomalies.Count * 0.25;

            // Registry anomalies
            score += result.RegistryActivity.Anomalies.Count * 0.15;

            // API anomalies
            score += result.ApiCalls.Anomalies.Count * 0.1;

            return Math.Min(score, 1.0);
        }

        private ThreatRisk DetermineRiskLevel(double score)
        {
            if (score > 0.8) return ThreatRisk.Critical;
            if (score > 0.6) return ThreatRisk.High;
            if (score > 0.4) return ThreatRisk.Medium;
            if (score > 0.2) return ThreatRisk.Low;
            return ThreatRisk.Safe;
        }

        private List<string> DetectSuspiciousPatterns(BehavioralAnalysisResult result)
        {
            var patterns = new List<string>();

            // Pattern 1: High memory usage + network access + file creation
            if (result.MemoryUsage.WorkingSet > 100 * 1024 * 1024 &&
                result.NetworkActivity.HasNetworkAccess &&
                result.FileSystemActivity.CreatedFiles.Count > 0)
            {
                patterns.Add("Memory resident with network and file creation - possible downloader");
            }

            // Pattern 2: Registry modification + suspicious API calls
            if (result.RegistryActivity.ModifiedKeys.Count > 0 &&
                result.ApiCalls.SuspiciousCalls.Count > 0)
            {
                patterns.Add("Registry modification with suspicious API calls - possible persistence mechanism");
            }

            // Pattern 3: Access to sensitive files + high memory usage
            if (result.FileSystemActivity.AccessedFiles.Any(f =>
                f.Contains("system32") || f.Contains("config")) &&
                result.MemoryUsage.WorkingSet > 200 * 1024 * 1024)
            {
                patterns.Add("Access to sensitive system files with high memory usage - possible credential dumper");
            }

            return patterns;
        }

        // Helper methods (simplified implementations)
        private async Task<MemoryInfo> GetMemoryInfoAsync(Process process)
        {
            await Task.Yield();
            // Simplified memory analysis
            return new MemoryInfo
            {
                AllocationPattern = "Normal",
                ExecutableRegions = 4
            };
        }

        private async Task<bool> CheckNetworkAccessAsync(Process process)
        {
            // Heuristic: run `netstat -ano` and look for any connection lines that list this process's PID
            try
            {
                var psi = new ProcessStartInfo("netstat", "-ano")
                {
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var proc = Process.Start(psi);
                if (proc == null)
                    return false;

                var output = await proc.StandardOutput.ReadToEndAsync().ConfigureAwait(false);
                await proc.WaitForExitAsync().ConfigureAwait(false);

                var pidToken = process.Id.ToString();
                foreach (var line in output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length < 1) continue;
                    // pid is usually the last token
                    if (parts[^1].Equals(pidToken, StringComparison.Ordinal))
                    {
                        return true;
                    }
                }
            }
            catch
            {
                // swallow - heuristic only
            }

            return false;
        }

        private async Task<int> GetConnectionCountAsync(Process process)
        {
            try
            {
                var psi = new ProcessStartInfo("netstat", "-ano")
                {
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var proc = Process.Start(psi);
                if (proc == null) return 0;

                var output = await proc.StandardOutput.ReadToEndAsync().ConfigureAwait(false);
                await proc.WaitForExitAsync().ConfigureAwait(false);

                var pidToken = process.Id.ToString();
                var count = 0;
                foreach (var line in output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length < 1) continue;
                    if (parts[^1].Equals(pidToken, StringComparison.Ordinal)) count++;
                }

                return count;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<List<int>> DetectSuspiciousPortsAsync(Process process)
        {
            var ports = new List<int>();
            try
            {
                var psi = new ProcessStartInfo("netstat", "-ano")
                {
                    CreateNoWindow = true,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var proc = Process.Start(psi);
                if (proc == null) return ports;

                var output = await proc.StandardOutput.ReadToEndAsync().ConfigureAwait(false);
                await proc.WaitForExitAsync().ConfigureAwait(false);

                var pidToken = process.Id.ToString();
                foreach (var line in output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                    if (parts.Length < 2) continue;
                    if (!parts[^1].Equals(pidToken, StringComparison.Ordinal)) continue;

                    // local endpoint is usually parts[1]
                    var local = parts[1];
                    var idx = local.LastIndexOf(':');
                    if (idx > 0 && int.TryParse(local[(idx + 1)..], out var port))
                    {
                        if (!ports.Contains(port)) ports.Add(port);
                    }
                }
            }
            catch
            {
                // ignore
            }

            return ports;
        }

        private async Task<List<string>> GetAccessedFilesAsync(Process process)
        {
            // Best-effort: enumerate loaded modules and return their file names
            await Task.Yield();
            var files = new List<string>();
            try
            {
                foreach (ProcessModule? m in process.Modules)
                {
                    try
                    {
                        if (m?.FileName != null && !files.Contains(m.FileName))
                            files.Add(m.FileName);
                    }
                    catch { /* best-effort */ }
                }
            }
            catch { }

            return files;
        }

        private async Task<List<string>> GetModifiedFilesAsync(Process process)
        {
            // Heuristic: return files in temp locations modified after process start time (best-effort)
            await Task.Yield();
            var files = new List<string>();
            try
            {
                var roots = new[]
                {
                    Path.GetTempPath(),
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData)
                };

                foreach (var root in roots)
                {
                    if (string.IsNullOrEmpty(root) || !Directory.Exists(root)) continue;
                    try
                    {
                        var recent = Directory.EnumerateFiles(root, "*", SearchOption.AllDirectories)
                            .Where(p =>
                            {
                                try
                                {
                                    var fi = new FileInfo(p);
                                    return fi.LastWriteTime > process.StartTime.AddMinutes(-1);
                                }
                                catch { return false; }
                            })
                            .Take(20);

                        files.AddRange(recent);
                    }
                    catch { }
                }
            }
            catch { }

            return files.Distinct().ToList();
        }

        private async Task<List<string>> GetCreatedFilesAsync(Process process)
        {
            // Heuristic: files created in temp locations since process start
            await Task.Yield();
            var files = new List<string>();
            try
            {
                var roots = new[]
                {
                    Path.GetTempPath(),
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData)
                };

                foreach (var root in roots)
                {
                    if (string.IsNullOrEmpty(root) || !Directory.Exists(root)) continue;
                    try
                    {
                        var recent = Directory.EnumerateFiles(root, "*", SearchOption.AllDirectories)
                            .Where(p =>
                            {
                                try
                                {
                                    var fi = new FileInfo(p);
                                    return fi.CreationTime > process.StartTime.AddMinutes(-1);
                                }
                                catch { return false; }
                            })
                            .Take(20);

                        files.AddRange(recent);
                    }
                    catch { }
                }
            }
            catch { }

            return files.Distinct().ToList();
        }

        private async Task<List<string>> GetAccessedRegistryKeysAsync(Process process)
        {
            // Heuristic: check common autorun registry keys for values that reference the process executable
            await Task.Yield();
            var keys = new List<string>();
            try
            {
                string? exePath = null;
                try { exePath = process.MainModule?.FileName; } catch { }

                var runKeys = new[]
                {
                    Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run"),
                    Registry.CurrentUser.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run")
                };

                foreach (var rk in runKeys)
                {
                    try
                    {
                        if (rk == null) continue;
                        foreach (var valueName in rk.GetValueNames())
                        {
                            try
                            {
                                var val = rk.GetValue(valueName)?.ToString() ?? string.Empty;
                                if (!string.IsNullOrEmpty(exePath) && val.IndexOf(Path.GetFileName(exePath), StringComparison.OrdinalIgnoreCase) >= 0)
                                {
                                    keys.Add($"{rk.Name}:{valueName}");
                                }
                                else if (!string.IsNullOrEmpty(process.ProcessName) && val.IndexOf(process.ProcessName, StringComparison.OrdinalIgnoreCase) >= 0)
                                {
                                    keys.Add($"{rk.Name}:{valueName}");
                                }
                            }
                            catch { }
                        }
                    }
                    catch { }
                }
            }
            catch { }

            return keys.Distinct().ToList();
        }

        private async Task<List<string>> GetModifiedRegistryKeysAsync(Process process)
        {
            // Best-effort: reuse accessed registry keys as a proxy for modified keys (accurate attribution requires tracing)
            var accessed = await GetAccessedRegistryKeysAsync(process).ConfigureAwait(false);
            return accessed;
        }

        private async Task<List<string>> DetectSuspiciousApiCallsAsync(Process process)
        {
            // Best-effort: check loaded module names for known suspicious indicators
            await Task.Yield();
            var suspicious = new List<string>();
            try
            {
                var candidates = new[] { "dbghelp", "winhttp", "winsock", "ntdll", "kernel32" };
                foreach (ProcessModule? m in process.Modules)
                {
                    try
                    {
                        var name = Path.GetFileName(m?.FileName ?? string.Empty).ToLowerInvariant();
                        foreach (var c in candidates)
                        {
                            if (name.Contains(c) && !suspicious.Contains(name))
                                suspicious.Add(name);
                        }
                    }
                    catch { }
                }
            }
            catch { }

            return suspicious;
        }

        private async Task<Dictionary<string, int>> AnalyzeApiFrequencyAsync(Process process)
        {
            // Basic frequency: count loaded module names as a proxy for API surface
            await Task.Yield();
            var dict = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
            try
            {
                foreach (ProcessModule? m in process.Modules)
                {
                    try
                    {
                        var name = Path.GetFileName(m?.FileName ?? string.Empty).ToLowerInvariant();
                        if (string.IsNullOrEmpty(name)) continue;
                        if (!dict.ContainsKey(name)) dict[name] = 0;
                        dict[name]++;
                    }
                    catch { }
                }
            }
            catch { }

            return dict;
        }

        private Dictionary<string, ProcessBehavior> LoadBehaviorDatabase()
        {
            if (!File.Exists(_behaviorDbPath))
            {
                return new Dictionary<string, ProcessBehavior>();
            }

            try
            {
                var json = File.ReadAllText(_behaviorDbPath);
                return JsonSerializer.Deserialize<Dictionary<string, ProcessBehavior>>(json) ?? new Dictionary<string, ProcessBehavior>();
            }
            catch
            {
                return new Dictionary<string, ProcessBehavior>();
            }
        }

        private void SaveBehaviorDatabase()
        {
            try
            {
                var json = JsonSerializer.Serialize(_behaviorDatabase, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_behaviorDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving behavior database: {ex.Message}");
            }
        }
    }

    // Supporting classes
    public class BehavioralAnalysisResult
    {
        public int ProcessId { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public MemoryAnalysis MemoryUsage { get; set; } = new();
        public NetworkAnalysis NetworkActivity { get; set; } = new();
        public FileSystemAnalysis FileSystemActivity { get; set; } = new();
        public RegistryAnalysis RegistryActivity { get; set; } = new();
        public ApiAnalysis ApiCalls { get; set; } = new();
        public double BehavioralScore { get; set; }
        public ThreatRisk RiskLevel { get; set; }
        public List<string> SuspiciousPatterns { get; set; } = new();
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class MemoryAnalysis
    {
        public long WorkingSet { get; set; }
        public long PrivateMemory { get; set; }
        public long VirtualMemory { get; set; }
        public string AllocationPattern { get; set; } = string.Empty;
        public int ExecutableRegions { get; set; }
        public List<string> Anomalies { get; set; } = new();
    }

    public class NetworkAnalysis
    {
        public bool HasNetworkAccess { get; set; }
        public int ConnectionCount { get; set; }
        public List<int> SuspiciousPorts { get; set; } = new();
        public List<string> Anomalies { get; set; } = new();
    }

    public class FileSystemAnalysis
    {
        public List<string> AccessedFiles { get; set; } = new();
        public List<string> ModifiedFiles { get; set; } = new();
        public List<string> CreatedFiles { get; set; } = new();
        public List<string> Anomalies { get; set; } = new();
    }

    public class RegistryAnalysis
    {
        public List<string> AccessedKeys { get; set; } = new();
        public List<string> ModifiedKeys { get; set; } = new();
        public List<string> Anomalies { get; set; } = new();
    }

    public class ApiAnalysis
    {
        public List<string> SuspiciousCalls { get; set; } = new();
        public Dictionary<string, int> FrequencyAnalysis { get; set; } = new();
        public List<string> Anomalies { get; set; } = new();
    }

    public class MemoryInfo
    {
        public string AllocationPattern { get; set; } = string.Empty;
        public int ExecutableRegions { get; set; }
    }

    public class ProcessBehavior
    {
        public string ProcessName { get; set; } = string.Empty;
        public double AverageMemoryUsage { get; set; }
        public int NormalConnectionCount { get; set; }
        public List<string> NormalApiCalls { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }
}
