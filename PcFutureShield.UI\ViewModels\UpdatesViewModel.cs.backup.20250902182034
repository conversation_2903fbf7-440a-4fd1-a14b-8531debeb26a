using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using PcFutureShield.UI.Services;
using PcFutureShield.Engine.VirusScanner;

namespace PcFutureShield.UI.ViewModels
{
    public class UpdatesViewModel : BaseViewModel
    {
        private readonly SettingsService _settingsService;
        private readonly UpdateService _updateService;

        // Update Status Properties
        private string _currentVersion = "1.0.0";
        public string CurrentVersion
        {
            get => _currentVersion;
            set => SetProperty(ref _currentVersion, value);
        }

        private string _latestVersion = "1.0.0";
        public string LatestVersion
        {
            get => _latestVersion;
            set => SetProperty(ref _latestVersion, value);
        }

        private int _updatesAvailable;
        public int UpdatesAvailable
        {
            get => _updatesAvailable;
            set => SetProperty(ref _updatesAvailable, value);
        }

        // Available Updates
        private ObservableCollection<UpdateInfo> _availableUpdates;
        public ObservableCollection<UpdateInfo> AvailableUpdates
        {
            get => _availableUpdates;
            set => SetProperty(ref _availableUpdates, value);
        }

        // Update Settings
        private bool _automaticUpdatesEnabled = true;
        public bool AutomaticUpdatesEnabled
        {
            get => _automaticUpdatesEnabled;
            set
            {
                if (SetProperty(ref _automaticUpdatesEnabled, value))
                {
                    _settingsService.Set("AutomaticUpdates", value);
                }
            }
        }

        private bool _backgroundDownloadEnabled = true;
        public bool BackgroundDownloadEnabled
        {
            get => _backgroundDownloadEnabled;
            set
            {
                if (SetProperty(ref _backgroundDownloadEnabled, value))
                {
                    _settingsService.Set("BackgroundDownload", value);
                }
            }
        }

        private bool _notifyBeforeInstall = true;
        public bool NotifyBeforeInstall
        {
            get => _notifyBeforeInstall;
            set
            {
                if (SetProperty(ref _notifyBeforeInstall, value))
                {
                    _settingsService.Set("NotifyBeforeInstall", value);
                }
            }
        }

        private bool _includeBetaReleases;
        public bool IncludeBetaReleases
        {
            get => _includeBetaReleases;
            set
            {
                if (SetProperty(ref _includeBetaReleases, value))
                {
                    _settingsService.Set("IncludeBetaReleases", value);
                }
            }
        }

        // Update Progress Properties
        private string _currentUpdateOperation = "Ready";
        public string CurrentUpdateOperation
        {
            get => _currentUpdateOperation;
            set => SetProperty(ref _currentUpdateOperation, value);
        }

        private double _updateProgress;
        public double UpdateProgress
        {
            get => _updateProgress;
            set => SetProperty(ref _updateProgress, value);
        }

        private string _updateStatus = "Up to date";
        public string UpdateStatus
        {
            get => _updateStatus;
            set => SetProperty(ref _updateStatus, value);
        }

        // Commands
        public ICommand CheckForUpdatesCommand { get; }
        public ICommand DownloadUpdatesCommand { get; }
        public ICommand InstallUpdatesCommand { get; }

        public UpdatesViewModel()
        {
            _settingsService = ServiceLocator.Get<SettingsService>();
            _updateService = new UpdateService(_settingsService);

            AvailableUpdates = new ObservableCollection<UpdateInfo>();

            // Load settings
            AutomaticUpdatesEnabled = _settingsService.Get("AutomaticUpdates", true);
            BackgroundDownloadEnabled = _settingsService.Get("BackgroundDownload", true);
            NotifyBeforeInstall = _settingsService.Get("NotifyBeforeInstall", true);
            IncludeBetaReleases = _settingsService.Get("IncludeBetaReleases", false);

            // Initialize commands
            CheckForUpdatesCommand = new RelayCommand(async () => await CheckForUpdatesAsync());
            DownloadUpdatesCommand = new RelayCommand(async () => await DownloadUpdatesAsync());
            InstallUpdatesCommand = new RelayCommand(async () => await InstallUpdatesAsync());

            // Load initial values (sync-safe)
            LoadUpdateData();
        }

        private void LoadUpdateData()
        {
            try
            {
                // Load current version from settings or assembly
                CurrentVersion = _settingsService.Get("CurrentVersion", "1.0.0");
                LatestVersion = CurrentVersion; // Assume up to date initially
                UpdatesAvailable = 0;

                // Clear and load available updates
                AvailableUpdates.Clear();

                UpdateStatus = "Up to date";
            }
            catch (Exception ex)
            {
                UpdateStatus = $"Error loading update data: {ex.Message}";
            }
        }

        private async Task CheckForUpdatesAsync()
        {
            try
            {
                CurrentUpdateOperation = "Checking for updates...";
                UpdateProgress = 0;
                UpdateStatus = "Contacting update server...";

                // Simulate network delay
                await Task.Delay(2000);
                UpdateProgress = 50;

                // Query update feed via UpdateService
                var updates = await _updateService.GetAvailableUpdatesAsync();
                AvailableUpdates.Clear();
                foreach (var u in updates)
                {
                    // Filter beta releases if required
                    if (!IncludeBetaReleases && u.Version.Contains("-beta", StringComparison.OrdinalIgnoreCase)) continue;
                    AvailableUpdates.Add(new UpdateInfo
                    {
                        UpdateName = u.Version,
                        UpdateDescription = u.Notes ?? string.Empty,
                        UpdateSize = "Unknown",
                        ReleaseDate = DateTime.UtcNow,
                        IsCritical = u.IsCritical
                    });
                }

                UpdatesAvailable = AvailableUpdates.Count;
                LatestVersion = updates.Count > 0 ? updates[0].Version : CurrentVersion;
                UpdateStatus = UpdatesAvailable > 0 ? $"{UpdatesAvailable} update(s) available" : "No updates available";

                UpdateProgress = 100;
                CurrentUpdateOperation = "Update check complete";
            }
            catch (Exception ex)
            {
                UpdateStatus = $"Update check failed: {ex.Message}";
                CurrentUpdateOperation = "Update check failed";
            }
        }

        private async Task DownloadUpdatesAsync()
        {
            if (UpdatesAvailable == 0)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("No Updates", "No updates available to download."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show("No updates available to download.", "No Updates", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
                return;
            }

            var cts = new System.Threading.CancellationTokenSource();
            try
            {
                CurrentUpdateOperation = "Downloading updates...";
                UpdateProgress = 0;
                UpdateStatus = "Downloading update files...";

                var feed = await _updateService.GetAvailableUpdatesAsync(cts.Token);
                var toDownload = new System.Collections.Generic.List<UpdateService.UpdateFeedEntry>();
                foreach (var e in feed)
                {
                    if (!IncludeBetaReleases && e.Version.Contains("-beta", StringComparison.OrdinalIgnoreCase)) continue;
                    toDownload.Add(e);
                }

                if (toDownload.Count == 0)
                {
                    UpdateStatus = "No applicable updates to download.";
                    CurrentUpdateOperation = "Idle";
                    return;
                }

                int total = toDownload.Count;
                for (int i = 0; i < total; i++)
                {
                    var entry = toDownload[i];
                    UpdateStatus = $"Downloading {entry.Version} ({i + 1}/{total})";

                    var progress = new System.Progress<double>(p =>
                    {
                        // Overall progress: (completed items + current percent) / total
                        UpdateProgress = ((double)i / total) * 100.0 + (p / total);
                    });

                    var (success, localPath, error) = await _updateService.DownloadUpdateAsync(entry, progress, cts.Token);
                    if (!success)
                    {
                        UpdateStatus = $"Download failed for {entry.Version}: {error}";
                        continue;
                    }

                    // If the package looks like a signature JSON, attempt to apply to engine
                    if (!string.IsNullOrWhiteSpace(localPath) && localPath.EndsWith(".json", StringComparison.OrdinalIgnoreCase))
                    {
                        try
                        {
                            var scanner = ServiceLocator.Get<PcFutureShield.Engine.VirusScanner.VirusScannerService>();
                            var (applied, applyError) = await scanner.ApplySignaturePackageAsync(localPath, System.Threading.CancellationToken.None);
                            if (applied)
                            {
                                _settingsService.Set("LastDefinitionUpdate", DateTime.UtcNow);
                                UpdateStatus = $"Signature package {entry.Version} applied";
                            }
                            else
                            {
                                UpdateStatus = $"Failed to apply signature package {entry.Version}: {applyError}";
                            }
                        }
                        catch (Exception ex)
                        {
                            UpdateStatus = $"Error applying signature package: {ex.Message}";
                        }
                    }
                    else
                    {
                        // Non-signature package handling (e.g., application update) - save for install
                        UpdateStatus = $"Downloaded update {entry.Version} to {localPath}";
                    }
                }

                UpdateProgress = 100;
                CurrentUpdateOperation = "Updates downloaded";
                UpdateStatus = "All downloads completed";
            }
            catch (OperationCanceledException)
            {
                UpdateStatus = "Download cancelled";
                CurrentUpdateOperation = "Cancelled";
            }
            catch (Exception ex)
            {
                UpdateStatus = $"Download failed: {ex.Message}";
                CurrentUpdateOperation = "Download failed";
            }
            finally
            {
                cts.Dispose();
            }
        }

        private async Task InstallUpdatesAsync()
        {
            if (UpdatesAvailable == 0)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("No Updates", "No updates available to install."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show("No updates available to install.", "No Updates", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
                return;
            }

            var result = System.Windows.MessageBoxResult.No;
            try
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    result = System.Windows.MessageBox.Show("Install updates now? The application may need to restart.", "Confirm Installation", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);
                });
            }
            catch
            {
                result = System.Windows.MessageBoxResult.No;
            }

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    CurrentUpdateOperation = "Installing updates...";
                    UpdateProgress = 0;
                    UpdateStatus = "Installing update files...";

                    // Simulate installation progress
                    for (int i = 0; i <= 100; i += 5)
                    {
                        UpdateProgress = i;
                        await Task.Delay(150);
                    }

                    // Update version after successful installation
                    CurrentVersion = LatestVersion;
                    UpdatesAvailable = 0;
                    AvailableUpdates.Clear();

                    UpdateStatus = "Installation complete";
                    CurrentUpdateOperation = "Updates installed successfully";

                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Installation Complete", "Updates installed successfully! Please restart the application to apply changes."); } catch { System.Windows.Application.Current?.Dispatcher.Invoke(() => System.Windows.MessageBox.Show("Updates installed successfully! Please restart the application to apply changes.", "Installation Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information)); }
                }
                catch (Exception ex)
                {
                    UpdateStatus = $"Installation failed: {ex.Message}";
                    CurrentUpdateOperation = "Installation failed";
                }
            }
        }
    }

    public class UpdateInfo
    {
        public string UpdateName { get; set; } = string.Empty;
        public string UpdateDescription { get; set; } = string.Empty;
        public string UpdateSize { get; set; } = string.Empty;
        public DateTime ReleaseDate { get; set; }
        public bool IsCritical { get; set; }
    }
}
