using System.Threading.Tasks;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using PcFutureShield.Common.Models;

namespace PcFutureShield.ExtensionHost.Hubs
{
    public sealed class DetectionHub : Hub
    {
        private readonly ILogger<DetectionHub> _logger;

        public DetectionHub(ILogger<DetectionHub> logger) => _logger = logger;

        /// <summary>
        /// Called by remote RealtimeScanner clients to publish a detection.
        /// Server will re-broadcast to all connected clients as ReceiveDetection.
        /// </summary>
        public async Task PublishDetection(DetectionEvent detection)
        {
            _logger.LogInformation("PublishDetection from {ConnectionId} => {File} (malicious:{IsMalicious})",
                Context.ConnectionId, detection.FilePath, detection.IsMalicious);
            await Clients.All.SendAsync("ReceiveDetection", detection);
        }
    }
}
