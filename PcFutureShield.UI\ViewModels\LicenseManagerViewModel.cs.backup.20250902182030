using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class LicenseManagerViewModel : BaseViewModel
    {
        private readonly ILicenseManager _licenseManager;

        // License Information Properties
        private string _licenseType = "Professional";
        public string LicenseType
        {
            get => _licenseType;
            set => SetProperty(ref _licenseType, value);
        }

        private string _licenseStatus = "Active";
        public string LicenseStatus
        {
            get => _licenseStatus;
            set => SetProperty(ref _licenseStatus, value);
        }

        private DateTime _expirationDate = DateTime.Now.AddYears(1);
        public DateTime ExpirationDate
        {
            get => _expirationDate;
            set => SetProperty(ref _expirationDate, value);
        }

        private int _daysRemaining;
        public int DaysRemaining
        {
            get => _daysRemaining;
            set => SetProperty(ref _daysRemaining, value);
        }

        // License Activation Properties
        private string _productKey = string.Empty;
        public string ProductKey
        {
            get => _productKey;
            set => SetProperty(ref _productKey, value);
        }

        private string _licenseKey = string.Empty;
        public string LicenseKey
        {
            get => _licenseKey;
            set => SetProperty(ref _licenseKey, value);
        }

        // Licensed Features
        private ObservableCollection<LicensedFeature> _licensedFeatures;
        public ObservableCollection<LicensedFeature> LicensedFeatures
        {
            get => _licensedFeatures;
            set => SetProperty(ref _licensedFeatures, value);
        }

        // Commands
        public ICommand ActivateLicenseCommand { get; }
        public ICommand DeactivateLicenseCommand { get; }
        public ICommand RefreshLicenseCommand { get; }
        public ICommand TransferLicenseCommand { get; }
        public ICommand ContactSupportCommand { get; }

        public LicenseManagerViewModel()
        {
            _licenseManager = ServiceLocator.Get<ILicenseManager>();

            LicensedFeatures = new ObservableCollection<LicensedFeature>();

            // Initialize commands
            ActivateLicenseCommand = new RelayCommand(async () => await ActivateLicenseAsync());
            DeactivateLicenseCommand = new RelayCommand(async () => await DeactivateLicenseAsync());
            RefreshLicenseCommand = new RelayCommand(async () => await RefreshLicenseAsync());
            TransferLicenseCommand = new RelayCommand(async () => await TransferLicenseAsync());
            ContactSupportCommand = new RelayCommand(ContactSupport);

            LoadLicenseData();
        }

        private void LoadLicenseData()
        {
            try
            {
                // Load license status from service
                LicenseStatus = _licenseManager.GetLicenseStatus();

                // Set default/demo values for other properties
                LicenseType = "Professional";
                ExpirationDate = DateTime.Now.AddYears(1);
                DaysRemaining = (int)(ExpirationDate - DateTime.Now).TotalDays;
                LicenseKey = "DEMO-KEY-12345";

                // Load licensed features with default values
                LicensedFeatures.Clear();
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Real-time Protection", Status = "Enabled", Description = "Continuous malware protection" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Virus Scanning", Status = "Enabled", Description = "On-demand virus scanning" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "PC Optimization", Status = "Enabled", Description = "System performance optimization" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Parental Controls", Status = "Enabled", Description = "Content filtering and monitoring" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Browser Extension", Status = "Enabled", Description = "Safe browsing protection" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Gaming Protection", Status = "Enabled", Description = "Gaming-specific security" });
            }
            catch (Exception)
            {
                // Fallback to demo data
                LicenseType = "Professional";
                LicenseStatus = "Active";
                ExpirationDate = DateTime.Now.AddYears(1);
                DaysRemaining = 365;
                LicenseKey = "DEMO-KEY-12345";

                LicensedFeatures.Clear();
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Real-time Protection", Status = "Enabled", Description = "Continuous malware protection" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Virus Scanning", Status = "Enabled", Description = "On-demand virus scanning" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "PC Optimization", Status = "Enabled", Description = "System performance optimization" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Parental Controls", Status = "Enabled", Description = "Content filtering and monitoring" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Browser Extension", Status = "Enabled", Description = "Safe browsing protection" });
                LicensedFeatures.Add(new LicensedFeature { FeatureName = "Gaming Protection", Status = "Enabled", Description = "Gaming-specific security" });
            }
        }

        private async Task ActivateLicenseAsync()
        {
            if (string.IsNullOrWhiteSpace(ProductKey))
            {
                System.Windows.MessageBox.Show("Please enter a valid product key.", "Invalid Key", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            try
            {
                // Simulate license activation (in real implementation, this would call a service)
                await Task.Delay(1000); // Simulate network call

                // For demo purposes, accept any key that looks valid
                if (ProductKey.Length >= 10)
                {
                    LicenseStatus = "Active";
                    LicenseKey = ProductKey;
                    ProductKey = string.Empty;
                    LoadLicenseData(); // Refresh data

                    System.Windows.MessageBox.Show("License activated successfully!", "Activation Successful", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                else
                {
                    System.Windows.MessageBox.Show("Invalid product key format.", "Activation Failed", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"License activation error: {ex.Message}", "Activation Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task DeactivateLicenseAsync()
        {
            var result = System.Windows.MessageBox.Show("Are you sure you want to deactivate this license?", "Confirm Deactivation", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    // Simulate license deactivation
                    await Task.Delay(1000);

                    LicenseStatus = "Inactive";
                    LicenseKey = string.Empty;
                    LoadLicenseData(); // Refresh data

                    System.Windows.MessageBox.Show("License deactivated successfully.", "Deactivation Successful", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"License deactivation error: {ex.Message}", "Deactivation Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private async Task RefreshLicenseAsync()
        {
            try
            {
                // Simulate license refresh
                await Task.Delay(1000);

                LoadLicenseData(); // Refresh all data
                System.Windows.MessageBox.Show("License information refreshed successfully.", "Refresh Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"License refresh error: {ex.Message}", "Refresh Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        private async Task TransferLicenseAsync()
        {
            var result = System.Windows.MessageBox.Show("This will transfer the license to another computer. Continue?", "Confirm Transfer", System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    // Simulate license transfer
                    await Task.Delay(1000);

                    LicenseStatus = "Transferred";
                    System.Windows.MessageBox.Show("License transferred successfully. This computer will no longer have an active license.", "Transfer Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"License transfer error: {ex.Message}", "Transfer Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private void ContactSupport()
        {
            System.Windows.MessageBox.Show("Contacting support...\n\nEmail: <EMAIL>\nPhone: 1-800-PC-SECURITY\n\nPlease include your license key when contacting support.", "Contact Support", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    public class LicensedFeature
    {
        public string FeatureName { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
    }
}
