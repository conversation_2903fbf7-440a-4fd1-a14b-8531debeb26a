<UserControl x:Class="PcFutureShield.UI.Views.EnhancedScanView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d">
    <Grid Background="{DynamicResource BackgroundBrush}">
    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="Enhanced Deep Scan" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" Foreground="{DynamicResource PrimaryFontBrush}"/>
            <TextBlock Text="AI-powered, multi-engine, and rootkit detection. Choose scan type:" FontSize="18" Margin="0,0,0,12" Foreground="{DynamicResource PrimaryFontBrush}"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="Full Scan" Style="{DynamicResource GlassButtonStyle}" Command="{Binding FullScanCommand}" Width="160" Height="48" Margin="0,0,18,0" Foreground="{DynamicResource PrimaryFontBrush}"/>
                <Button Content="Critical Areas" Style="{DynamicResource GlassButtonStyle}" Command="{Binding CriticalScanCommand}" Width="160" Height="48" Margin="0,0,18,0"/>
                <Button Content="Custom Scan" Style="{DynamicResource GlassButtonStyle}" Command="{Binding CustomScanCommand}" Width="160" Height="48"/>
            </StackPanel>
            <ProgressBar Height="24" Width="480" Margin="0,24,0,0" Minimum="0" Maximum="100" Value="{Binding Progress}"/>
            <ListView ItemsSource="{Binding Results}" Margin="0,24,0,0" Height="220" Width="800" Foreground="{DynamicResource PrimaryFontBrush}">
                <ListView.View>
                    <GridView>
                        <GridViewColumn Header="File" Width="320" DisplayMemberBinding="{Binding FilePath}"/>
                        <GridViewColumn Header="Detection" Width="180" DisplayMemberBinding="{Binding Detection}"/>
                        <GridViewColumn Header="Engine" Width="120" DisplayMemberBinding="{Binding Engine}"/>
                        <GridViewColumn Header="Severity" Width="90" DisplayMemberBinding="{Binding Severity}"/>
                        <GridViewColumn Header="Timestamp" Width="120" DisplayMemberBinding="{Binding Timestamp}"/>
                    </GridView>
                </ListView.View>
            </ListView>
        </StackPanel>
    </Grid>
</UserControl>
