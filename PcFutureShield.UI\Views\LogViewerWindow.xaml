<Window x:Class="PcFutureShield.UI.Views.LogViewerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="PcFutureShield Log Viewer" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="{DynamicResource BackgroundBrush}"
        Foreground="{DynamicResource PrimaryFontBrush}">

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="Application Logs" FontSize="18" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBlock Text="{Binding LogFilePath}" FontSize="12" Foreground="{DynamicResource SecondaryFontBrush}"/>
        </StackPanel>

        <!-- Log Content -->
        <Border Grid.Row="1" Style="{DynamicResource ChromeBackgroundStyle}" Padding="10">
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                <TextBox x:Name="LogTextBox"
                         Text="{Binding LogContent, Mode=OneWay}"
                         IsReadOnly="True"
                         Background="Transparent"
                         Foreground="{DynamicResource PrimaryFontBrush}"
                         BorderThickness="0"
                         FontFamily="Consolas"
                         FontSize="11"
                         TextWrapping="NoWrap"
                         AcceptsReturn="True"/>
            </ScrollViewer>
        </Border>

        <!-- Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
            <Button Content="Refresh" Command="{Binding RefreshCommand}" Style="{DynamicResource GlassButtonStyle}" Width="80" Margin="0,0,10,0"/>
            <Button Content="Clear Log" Command="{Binding ClearLogCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="80" Margin="0,0,10,0"/>
            <Button Content="Close" Click="CloseButton_Click" Style="{DynamicResource ChromeButtonStyle}" Width="80"/>
        </StackPanel>
    </Grid>
</Window>
