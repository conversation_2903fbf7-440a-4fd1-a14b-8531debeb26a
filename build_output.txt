Build started 8/28/2025 12:54:41 PM.
     1>Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.sln" on node 1 (Restore target(s)).
     1>ValidateSolutionConfiguration:
         Building solution configuration "Debug|Any CPU".
       _GetAllRestoreProjectPathItems:
         Determining projects to restore...
       Restore:
         X.509 certificate chain validation will use the default trust store selected by .NET for code signing.
         X.509 certificate chain validation will use the default trust store selected by .NET for timestamping.
         Assets file has not changed. Skipping assets file writing. Path: D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\obj\project.assets.json
         Assets file has not changed. Skipping assets file writing. Path: D:\PcFS_Create\PcFutureShield\PcFutureShield.Common\obj\project.assets.json
         Assets file has not changed. Skipping assets file writing. Path: D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\obj\project.assets.json
         Restored D:\PcFS_Create\PcFutureShield\PcFutureShield.Common\PcFutureShield.Common.csproj (in 85 ms).
         Restored D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj (in 85 ms).
         Restored D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\PcFutureShield.UI.csproj (in 85 ms).
         
         NuGet Config files used:
             C:\Users\<USER>\AppData\Roaming\NuGet\NuGet.Config
             C:\Program Files (x86)\NuGet\Config\Microsoft.VisualStudio.FallbackLocation.config
             C:\Program Files (x86)\NuGet\Config\Microsoft.VisualStudio.Offline.config
         
         Feeds used:
             https://api.nuget.org/v3/index.json
             C:\Program Files (x86)\Microsoft SDKs\NuGetPackages\
             C:\Program Files\dotnet\library-packs
         All projects are up-to-date for restore.
     1>Done Building Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.sln" (Restore target(s)).
   1:2>Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.sln" on node 1 (default targets).
     1>ValidateSolutionConfiguration:
         Building solution configuration "Debug|Any CPU".
   1:2>Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.sln" (1:2) is building "D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\PcFutureShield.UI.csproj" (2:6) on node 2 (default targets).
   2:6>Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\PcFutureShield.UI.csproj" (2:6) is building "D:\PcFS_Create\PcFutureShield\PcFutureShield.Common\PcFutureShield.Common.csproj" (4:7) on node 2 (default targets).
     4>GenerateTargetFrameworkMonikerAttribute:
       Skipping target "GenerateTargetFrameworkMonikerAttribute" because all output files are up-to-date with respect to the input files.
       CoreGenerateAssemblyInfo:
       Skipping target "CoreGenerateAssemblyInfo" because all output files are up-to-date with respect to the input files.
       _GenerateSourceLinkFile:
         Source Link is empty, file 'obj\Debug\net8.0\PcFutureShield.Common.sourcelink.json' does not exist.
       CoreCompile:
       Skipping target "CoreCompile" because all output files are up-to-date with respect to the input files.
       GenerateBuildDependencyFile:
       Skipping target "GenerateBuildDependencyFile" because all output files are up-to-date with respect to the input files.
       CopyFilesToOutputDirectory:
         PcFutureShield.Common -> D:\PcFS_Create\PcFutureShield\PcFutureShield.Common\bin\Debug\net8.0\PcFutureShield.Common.dll
     4>Done Building Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.Common\PcFutureShield.Common.csproj" (default targets).
   2:6>Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\PcFutureShield.UI.csproj" (2:6) is building "D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj" (3:6) on node 1 (default targets).
     3>GenerateTargetFrameworkMonikerAttribute:
       Skipping target "GenerateTargetFrameworkMonikerAttribute" because all output files are up-to-date with respect to the input files.
       CoreGenerateAssemblyInfo:
       Skipping target "CoreGenerateAssemblyInfo" because all output files are up-to-date with respect to the input files.
       _GenerateSourceLinkFile:
         Source Link is empty, file 'obj\Debug\net8.0\PcFutureShield.Engine.sourcelink.json' does not exist.
       CoreCompile:
         C:\Program Files\dotnet\dotnet.exe exec "C:\Program Files\dotnet\sdk\9.0.304\Roslyn\bincore\csc.dll" /noconfig /unsafe- /checked- /nowarn:CS1998,CS8618,CS8625,CS8603,CS8601,CS8622,CS8612,CS8767,CS8604,CS8602,CS8600,CS0169,CS0649,CS0219,CS1591,CA1416,CS4014,CS0169,CS0649,CS0219,1701,1702 /fullpaths /nostdlib+ /errorreport:prompt /warn:8 /define:WINDOWS;WINDOWS_PLATFORM;TRACE;DEBUG;NET;NET8_0;NETCOREAPP;NET5_0_OR_GREATER;NET6_0_OR_GREATER;NET7_0_OR_GREATER;NET8_0_OR_GREATER;NETCOREAPP1_0_OR_GREATER;NETCOREAPP1_1_OR_GREATER;NETCOREAPP2_0_OR_GREATER;NETCOREAPP2_1_OR_GREATER;NETCOREAPP2_2_OR_GREATER;NETCOREAPP3_0_OR_GREATER;NETCOREAPP3_1_OR_GREATER /highentropyva+ /nullable:enable /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\Microsoft.CSharp.dll" /reference:C:\Users\<USER>\.nuget\packages\microsoft.data.sqlite.core\8.0.0\lib\net8.0\Microsoft.Data.Sqlite.dll /reference:C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll /reference:C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\Microsoft.VisualBasic.Core.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\Microsoft.VisualBasic.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\Microsoft.Win32.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\Microsoft.Win32.Registry.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\mscorlib.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\netstandard.dll" /reference:D:\PcFS_Create\PcFutureShield\PcFutureShield.Common\obj\Debug\net8.0\ref\PcFutureShield.Common.dll /reference:C:\Users\<USER>\.nuget\packages\sqlitepclraw.bundle_e_sqlite3\2.1.6\lib\netstandard2.0\SQLitePCLRaw.batteries_v2.dll /reference:C:\Users\<USER>\.nuget\packages\sqlitepclraw.core\2.1.6\lib\netstandard2.0\SQLitePCLRaw.core.dll /reference:C:\Users\<USER>\.nuget\packages\sqlitepclraw.provider.e_sqlite3\2.1.6\lib\net6.0\SQLitePCLRaw.provider.e_sqlite3.dll /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.AppContext.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Buffers.dll" /reference:C:\Users\<USER>\.nuget\packages\system.codedom\8.0.0\lib\net8.0\System.CodeDom.dll /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Collections.Concurrent.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Collections.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Collections.Immutable.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Collections.NonGeneric.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Collections.Specialized.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ComponentModel.Annotations.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ComponentModel.DataAnnotations.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ComponentModel.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ComponentModel.EventBasedAsync.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ComponentModel.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ComponentModel.TypeConverter.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Configuration.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Console.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Core.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Data.Common.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Data.DataSetExtensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Data.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.Contracts.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.Debug.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.DiagnosticSource.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.FileVersionInfo.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.Process.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.StackTrace.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.TextWriterTraceListener.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.Tools.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.TraceSource.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Diagnostics.Tracing.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Drawing.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Drawing.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Dynamic.Runtime.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Formats.Asn1.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Formats.Tar.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Globalization.Calendars.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Globalization.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Globalization.Extensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.Compression.Brotli.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.Compression.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.Compression.FileSystem.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.Compression.ZipFile.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.FileSystem.AccessControl.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.FileSystem.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.FileSystem.DriveInfo.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.FileSystem.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.FileSystem.Watcher.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.IsolatedStorage.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.MemoryMappedFiles.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.Pipes.AccessControl.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.Pipes.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.IO.UnmanagedMemoryStream.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Linq.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Linq.Expressions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Linq.Parallel.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Linq.Queryable.dll" /reference:C:\Users\<USER>\.nuget\packages\system.management\8.0.0\lib\net8.0\System.Management.dll /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Memory.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Http.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Http.Json.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.HttpListener.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Mail.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.NameResolution.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.NetworkInformation.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Ping.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Quic.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Requests.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Security.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.ServicePoint.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.Sockets.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.WebClient.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.WebHeaderCollection.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.WebProxy.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.WebSockets.Client.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Net.WebSockets.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Numerics.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Numerics.Vectors.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ObjectModel.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.DispatchProxy.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.Emit.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.Emit.ILGeneration.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.Emit.Lightweight.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.Extensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.Metadata.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Reflection.TypeExtensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Resources.Reader.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Resources.ResourceManager.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Resources.Writer.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.CompilerServices.Unsafe.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.CompilerServices.VisualC.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Extensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Handles.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.InteropServices.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.InteropServices.JavaScript.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.InteropServices.RuntimeInformation.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Intrinsics.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Loader.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Numerics.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Serialization.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Serialization.Formatters.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Serialization.Json.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Serialization.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Runtime.Serialization.Xml.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.AccessControl.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Claims.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.Algorithms.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.Cng.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.Csp.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.Encoding.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.OpenSsl.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.Primitives.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Cryptography.X509Certificates.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Principal.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.Principal.Windows.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Security.SecureString.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ServiceModel.Web.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ServiceProcess.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Text.Encoding.CodePages.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Text.Encoding.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Text.Encoding.Extensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Text.Encodings.Web.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Text.Json.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Text.RegularExpressions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Channels.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Overlapped.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Tasks.Dataflow.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Tasks.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Tasks.Extensions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Tasks.Parallel.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Thread.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.ThreadPool.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Threading.Timer.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Transactions.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Transactions.Local.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.ValueTuple.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Web.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Web.HttpUtility.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Windows.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.Linq.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.ReaderWriter.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.Serialization.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.XDocument.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.XmlDocument.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.XmlSerializer.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.XPath.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\System.Xml.XPath.XDocument.dll" /reference:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\ref\net8.0\WindowsBase.dll" /debug+ /debug:portable /filealign:512 /optimize- /out:obj\Debug\net8.0\PcFutureShield.Engine.dll /refout:obj\Debug\net8.0\refint\PcFutureShield.Engine.dll /target:library /warnaserror- /utf8output /deterministic+ /langversion:latest /analyzerconfig:D:\PcFS_Create\PcFutureShield\.editorconfig /analyzerconfig:obj\Debug\net8.0\PcFutureShield.Engine.GeneratedMSBuildEditorConfig.editorconfig /analyzerconfig:"C:\Program Files\dotnet\sdk\9.0.304\Sdks\Microsoft.NET.Sdk\analyzers\build\config\analysislevel_8_default.globalconfig" /analyzer:"C:\Program Files\dotnet\sdk\9.0.304\Sdks\Microsoft.NET.Sdk\targets\..\analyzers\Microsoft.CodeAnalysis.CSharp.NetAnalyzers.dll" /analyzer:"C:\Program Files\dotnet\sdk\9.0.304\Sdks\Microsoft.NET.Sdk\targets\..\analyzers\Microsoft.CodeAnalysis.NetAnalyzers.dll" /analyzer:C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\analyzers\dotnet\roslyn4.4\cs\Microsoft.Extensions.Logging.Generators.dll /analyzer:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\analyzers/dotnet/cs/Microsoft.Interop.ComInterfaceGenerator.dll" /analyzer:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\analyzers/dotnet/cs/Microsoft.Interop.JavaScript.JSImportGenerator.dll" /analyzer:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\analyzers/dotnet/cs/Microsoft.Interop.LibraryImportGenerator.dll" /analyzer:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\analyzers/dotnet/cs/Microsoft.Interop.SourceGeneration.dll" /analyzer:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\analyzers/dotnet/cs/System.Text.Json.SourceGeneration.dll" /analyzer:"C:\Program Files\dotnet\packs\Microsoft.NETCore.App.Ref\8.0.19\analyzers/dotnet/cs/System.Text.RegularExpressions.Generator.dll" Quarantine\QuarantineItem.cs Quarantine\QuarantineManager.cs Scanning\IScannerService.cs Scanning\ISignatureDatabase.cs Scanning\ScannerService.cs Scanning\ScanResult.cs Scanning\SqliteSignatureDatabase.cs VirusScanner\PcFutureShieldScanner.cs VirusScanner\SignatureDatabase.cs VirusScanner\VirusScannerService.cs obj\Debug\net8.0\PcFutureShield.Engine.GlobalUsings.g.cs "obj\Debug\net8.0\.NETCoreApp,Version=v8.0.AssemblyAttributes.cs" obj\Debug\net8.0\PcFutureShield.Engine.AssemblyInfo.cs /warnaserror+:true,SYSLIB0011
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(12,28): error CS0234: The type or namespace name 'Hosting' does not exist in the namespace 'Microsoft.Extensions' (are you missing an assembly reference?) [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScanResult.cs(5,25): error CS0101: The namespace 'PcFutureShield.Engine.Scanning' already contains a definition for 'ScanResult' [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(17,65): error CS0246: The type or namespace name 'IHostedService' could not be found (are you missing a using directive or an assembly reference?) [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs(13,40): error CS0738: 'VirusScannerService' does not implement interface member 'IVirusScannerService.GetQuarantineAsync(CancellationToken)'. 'VirusScannerService.GetQuarantineAsync(CancellationToken)' cannot implement 'IVirusScannerService.GetQuarantineAsync(CancellationToken)' because it does not have the matching return type of 'Task<IReadOnlyList<QuarantineItem>>'. [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1202,45): error CS0111: Type 'ScannerService' already defines a member called 'PerformHeuristicAnalysisAsync' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1260,14): error CS0111: Type 'ScannerService' already defines a member called 'IsSuspiciousExtension' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1270,14): error CS0111: Type 'ScannerService' already defines a member called 'IsExecutable' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1279,14): error CS0111: Type 'ScannerService' already defines a member called 'IsSuspiciousLocation' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1290,16): error CS0111: Type 'ScannerService' already defines a member called 'GetProtectionString' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
     3>D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1309,28): error CS0111: Type 'ScannerService' already defines a member called 'CalculateFileEntropyAsync' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         CompilerServer: server - server processed compilation - PcFutureShield.Engine (net8.0)
     3>Done Building Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj" (default targets) -- FAILED.
     2>Done Building Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\PcFutureShield.UI.csproj" (default targets) -- FAILED.
     1>Done Building Project "D:\PcFS_Create\PcFutureShield\PcFutureShield.sln" (default targets) -- FAILED.

Build FAILED.

       "D:\PcFS_Create\PcFutureShield\PcFutureShield.sln" (default target) (1:2) ->
       "D:\PcFS_Create\PcFutureShield\PcFutureShield.UI\PcFutureShield.UI.csproj" (default target) (2:6) ->
       "D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj" (default target) (3:6) ->
       (CoreCompile target) -> 
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(12,28): error CS0234: The type or namespace name 'Hosting' does not exist in the namespace 'Microsoft.Extensions' (are you missing an assembly reference?) [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScanResult.cs(5,25): error CS0101: The namespace 'PcFutureShield.Engine.Scanning' already contains a definition for 'ScanResult' [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(17,65): error CS0246: The type or namespace name 'IHostedService' could not be found (are you missing a using directive or an assembly reference?) [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs(13,40): error CS0738: 'VirusScannerService' does not implement interface member 'IVirusScannerService.GetQuarantineAsync(CancellationToken)'. 'VirusScannerService.GetQuarantineAsync(CancellationToken)' cannot implement 'IVirusScannerService.GetQuarantineAsync(CancellationToken)' because it does not have the matching return type of 'Task<IReadOnlyList<QuarantineItem>>'. [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1202,45): error CS0111: Type 'ScannerService' already defines a member called 'PerformHeuristicAnalysisAsync' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1260,14): error CS0111: Type 'ScannerService' already defines a member called 'IsSuspiciousExtension' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1270,14): error CS0111: Type 'ScannerService' already defines a member called 'IsExecutable' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1279,14): error CS0111: Type 'ScannerService' already defines a member called 'IsSuspiciousLocation' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1290,16): error CS0111: Type 'ScannerService' already defines a member called 'GetProtectionString' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]
         D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\Scanning\ScannerService.cs(1309,28): error CS0111: Type 'ScannerService' already defines a member called 'CalculateFileEntropyAsync' with the same parameter types [D:\PcFS_Create\PcFutureShield\PcFutureShield.Engine\PcFutureShield.Engine.csproj]

    0 Warning(s)
    10 Error(s)

Time Elapsed 00:00:02.48

Workload updates are available. Run `dotnet workload list` for more information.
