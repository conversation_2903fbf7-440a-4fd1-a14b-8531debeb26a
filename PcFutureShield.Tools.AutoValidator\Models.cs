using System.Text.RegularExpressions;

namespace PcFutureShield.Tools.AutoValidator;

internal class ScanReport
{
    public int FilesScanned { get; set; }
    public List<Finding> Findings { get; } = new();
    public IReadOnlyList<Rule>? Rules { get; set; }
}

internal class Finding
{
    public string File { get; set; } = string.Empty;
    public string RuleId { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public int Line { get; set; }
    public Severity Severity { get; set; }
    public string Preview { get; set; } = string.Empty;
}

internal record Rule(string Id, string Title, string Description, Severity Severity, Regex Pattern);

internal enum Severity
{
    Info,
    Warning,
    Error
}

public class ValidationResult
{
    public string FilePath { get; }
    public string Message { get; }
    public ValidationResult(string filePath, string message)
    {
        FilePath = filePath;
        Message = message;
    }
}
