<UserControl x:Class="PcFutureShield.UI.Views.GamingProtectionView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource PrimaryColorBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <ScrollViewer.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF115CF1" Offset="1"/>
                </LinearGradientBrush>
            </ScrollViewer.Background>
            <StackPanel Margin="20">
                <TextBlock Text="Gaming Protection Center" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- Main Controls -->
                <UniformGrid Columns="2" Rows="2" Margin="0,0,0,24">
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Gaming Protections" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <CheckBox Content="Block Casino Sites" IsChecked="{Binding BlockCasinoSites}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Detect Rigging" IsChecked="{Binding DetectRigging}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Monitor Fraud Patterns" IsChecked="{Binding MonitorFraudPatterns}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Enable Game Verification" IsChecked="{Binding EnableGameVerification}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Gaming Statistics" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Casinos Blocked:" FontSize="16" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="{Binding TotalCasinosBlocked}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,16"/>
                            <TextBlock Text="Rigs Detected:" FontSize="16" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="{Binding TotalRigsDetected}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource ErrorBrush}"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Recent Gaming Activity" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <ListBox ItemsSource="{Binding DetectedCasinos}" Height="120" Margin="0,0,0,12" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding SiteName}" FontSize="12" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                            <TextBlock Text="{Binding RiskLevel, StringFormat=Risk: {0}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                            <TextBlock Text="{Binding DetectionTime, StringFormat={}{0:g}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Fraud Reports" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <ListBox ItemsSource="{Binding RecentFraudAlerts}" Height="120" Margin="0,0,0,12" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding AlertType}" FontSize="12" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                            <TextBlock Text="{Binding Severity}" FontSize="10" Foreground="{DynamicResource ErrorBrush}"/>
                                            <TextBlock Text="{Binding Timestamp, StringFormat={}{0:g}}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            <Button Content="View Reports" Command="{Binding ViewFraudReportsCommand}" Style="{DynamicResource GlassButtonStyle}" Width="120" Height="32"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,24,0,0">
                    <Button Content="Scan for Casinos" Command="{Binding ScanForCasinosCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="180" Height="48" Margin="0,0,20,0"/>
                    <Button Content="Scan for Rigs" Command="{Binding ScanForRigsCommand}" Style="{DynamicResource GlassButtonStyle}" Width="180" Height="48" Margin="0,0,20,0"/>
                    <Button Content="Update Settings" Command="{Binding UpdateProtectionSettingsCommand}" Style="{DynamicResource GlassButtonStyle}" Width="180" Height="48"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
