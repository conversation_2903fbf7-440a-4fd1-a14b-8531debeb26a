using System.Collections.Generic;

namespace PcFutureShield.RealtimeScanner
{
    public sealed class RealtimeScannerOptions
    {
        public string? ExtensionHostSignalRUrl { get; set; } // e.g. "https://localhost:5001/hubs/detections"
        public List<string> PathsToWatch { get; set; } = new() { @"%USERPROFILE%\Downloads" };
        public int SignalRReconnectDelayMs { get; set; } = 2000;
    }
}
