<UserControl x:Class="PcFutureShield.UI.Views.ScannerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels="clr-namespace:PcFutureShield.UI.ViewModels"
             mc:Ignorable="d">


    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="Auto"/>
        </Grid.ColumnDefinitions>
        <TextBox Grid.Row="0" Grid.ColumnSpan="2" Margin="0,0,0,8"
                 Text="{Binding TargetFolder, UpdateSourceTrigger=PropertyChanged}" />
        <Button Grid.Row="1" Grid.Column="0" Margin="0,0,8,8"
                Content="Start Scan" Command="{Binding StartScanCommand}" Foreground="#FFECF9F9" />
    <ProgressBar Grid.Row="1" Grid.Column="1" Margin="0,0,0,8" Minimum="0" Maximum="100" Height="24"
             Value="{Binding Progress, Mode=OneWay}" />

        <ListView Grid.Row="2" Grid.ColumnSpan="2" ItemsSource="{Binding Results}" SelectedItem="{Binding Selected}">
            <ListView.View>
                <GridView>
                    <GridViewColumn Header="Malicious" Width="90" DisplayMemberBinding="{Binding IsMalicious}"/>
                    <GridViewColumn Header="File" Width="420" DisplayMemberBinding="{Binding FilePath}"/>
                    <GridViewColumn Header="Reason" Width="260" DisplayMemberBinding="{Binding Reason}"/>
                    <GridViewColumn Header="SHA256" Width="360" DisplayMemberBinding="{Binding Sha256}"/>
                    <GridViewColumn Header="Entropy" Width="90" DisplayMemberBinding="{Binding Entropy}"/>
                </GridView>
            </ListView.View>
        </ListView>

        <Button Grid.Row="3" Grid.Column="1" Content="Quarantine Selected"
                Command="{Binding QuarantineSelectedCommand}" />
    </Grid>
</UserControl>
