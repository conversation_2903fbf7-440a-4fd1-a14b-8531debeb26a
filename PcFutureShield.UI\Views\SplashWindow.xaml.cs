﻿using System;
using System.Windows;
using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class SplashWindow : Window
    {
        public SplashWindow()
        {
            InitializeComponent();
        }

        public void SetProgress(double progress)
        {
            // Update progress bar if it exists in XAML
            Dispatcher.Invoke(() =>
            {
                // Implementation would depend on actual XAML structure
            });
        }

        public void SetMessage(string message)
        {
            // Update status message if it exists in XAML
            Dispatcher.Invoke(() =>
            {
                // Implementation would depend on actual XAML structure
            });
        }
    }
}
