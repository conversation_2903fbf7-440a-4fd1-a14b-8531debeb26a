#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Text.Json;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced zero-day threat detection service
    /// </summary>
    public class ZeroDayDetectionService
    {
        private readonly BehavioralAnalysisService _behavioralAnalysis;
        private readonly ThreatIntelligenceService _threatIntelligence;
        private readonly string _zeroDayDbPath;
        private readonly Dictionary<string, ZeroDayPattern> _zeroDayPatterns;

        public ZeroDayDetectionService(
            BehavioralAnalysisService behavioralAnalysis,
            ThreatIntelligenceService threatIntelligence)
        {
            _behavioralAnalysis = behavioralAnalysis;
            _threatIntelligence = threatIntelligence;

            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var zeroDayPath = Path.Combine(appData, "PcFutureShield", "ZeroDay");
            Directory.CreateDirectory(zeroDayPath);
            _zeroDayDbPath = Path.Combine(zeroDayPath, "zero_day_patterns.json");

            _zeroDayPatterns = LoadZeroDayPatterns();
        }

        public async Task<ZeroDayAnalysisResult> AnalyzeFileAsync(string filePath)
        {
            var result = new ZeroDayAnalysisResult
            {
                FilePath = filePath,
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Multi-layered zero-day detection
                result.AnomalyDetection = await DetectAnomaliesAsync(filePath);
                result.CodeAnalysis = await AnalyzeCodePatternsAsync(filePath);
                result.BehaviorPrediction = await PredictMaliciousBehaviorAsync(filePath);
                result.SignatureEvasion = await DetectSignatureEvasionAsync(filePath);
                result.EntropyAnalysis = AnalyzeEntropyAnomalies(filePath);

                // Calculate zero-day confidence score
                result.ZeroDayScore = CalculateZeroDayScore(result);
                result.IsPotentialZeroDay = result.ZeroDayScore > 0.7;

                // Generate detection reasons
                result.DetectionReasons = GenerateDetectionReasons(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        public async Task<ZeroDayAnalysisResult> AnalyzeProcessAsync(Process process)
        {
            var result = new ZeroDayAnalysisResult
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                StartTime = DateTime.UtcNow
            };

            try
            {
                // Behavioral analysis for zero-day detection
                var behavioralResult = await _behavioralAnalysis.AnalyzeProcessAsync(process);

                // Convert behavioral analysis to zero-day indicators
                result.AnomalyDetection = new AnomalyDetectionResult
                {
                    Score = behavioralResult.BehavioralScore,
                    Anomalies = behavioralResult.SuspiciousPatterns
                };

                result.BehaviorPrediction = new BehaviorPredictionResult
                {
                    PredictedMalicious = behavioralResult.RiskLevel >= ThreatRisk.Medium,
                    Confidence = behavioralResult.BehavioralScore,
                    PredictedBehaviors = behavioralResult.SuspiciousPatterns
                };

                // Memory-based zero-day detection
                result.MemoryAnalysis = await AnalyzeProcessMemoryAsync(process);

                // Network behavior analysis
                result.NetworkAnalysis = await AnalyzeProcessNetworkAsync(process);

                // Calculate zero-day confidence score
                result.ZeroDayScore = CalculateProcessZeroDayScore(result);
                result.IsPotentialZeroDay = result.ZeroDayScore > 0.6;

                // Generate detection reasons
                result.DetectionReasons = GenerateProcessDetectionReasons(result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        private async Task<AnomalyDetectionResult> DetectAnomaliesAsync(string filePath)
        {
            // Perform file/stat and entropy computation; keep method async-friendly
            await Task.Yield();
            var result = new AnomalyDetectionResult();

            try
            {
                var fileInfo = new FileInfo(filePath);

                // File size anomalies
                if (fileInfo.Length < 100 || fileInfo.Length > 50 * 1024 * 1024) // < 100 bytes or > 50MB
                    result.Anomalies.Add("Unusual file size");

                // Entropy analysis
                var entropy = HashingService.ComputeFileEntropy(filePath);
                if (entropy > 7.5 || entropy < 1.0)
                    result.Anomalies.Add($"Abnormal entropy: {entropy:F2}");

                // Check for packed/encrypted content
                if (entropy > 7.0)
                    result.Anomalies.Add("Potential packed/encrypted content");

                // File location analysis
                var directory = Path.GetDirectoryName(filePath)?.ToLowerInvariant();
                if (directory?.Contains("temp") == true || directory?.Contains("tmp") == true)
                    result.Anomalies.Add("Located in temporary directory");

                // Age analysis
                var age = DateTime.Now - fileInfo.CreationTime;
                if (age.TotalMinutes < 1)
                    result.Anomalies.Add("Very recently created");

                result.Score = result.Anomalies.Count * 0.2;
            }
            catch (Exception ex)
            {
                result.Anomalies.Add($"Anomaly detection error: {ex.Message}");
            }

            return result;
        }

        private async Task<CodeAnalysisResult> AnalyzeCodePatternsAsync(string filePath)
        {
            // Use async file read and background parsing to provide real asynchronous behavior
            var result = new CodeAnalysisResult();

            try
            {
                var content = await File.ReadAllTextAsync(filePath).ConfigureAwait(false);

                // Pattern detection (CPU-bound); perform synchronously after yielding
                await Task.Yield();
                var r = new CodeAnalysisResult();
                r.ObfuscationIndicators = DetectObfuscation(content);
                r.SuspiciousAPIs = DetectSuspiciousAPIs(content);
                r.ShellcodePatterns = DetectShellcodePatterns(content);
                r.PolymorphicIndicators = DetectPolymorphicCode(content);
                r.Score = (r.ObfuscationIndicators.Count * 0.3) +
                          (r.SuspiciousAPIs.Count * 0.4) +
                          (r.ShellcodePatterns.Count * 0.2) +
                          (r.PolymorphicIndicators.Count * 0.1);
                return r;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<BehaviorPredictionResult> PredictMaliciousBehaviorAsync(string filePath)
        {
            var result = new BehaviorPredictionResult();

            try
            {
                var fileInfo = new FileInfo(filePath);
                var extension = Path.GetExtension(filePath).ToLowerInvariant();

                if (extension == ".exe" || extension == ".dll")
                {
                    result.PredictedBehaviors.Add("Executable file - potential code execution");

                    // Entropy computation is CPU-bound; yield and compute synchronously to keep method async-friendly
                    await Task.Yield();
                    var entropy = HashingService.ComputeFileEntropy(filePath);
                    if (entropy > 7.0)
                        result.PredictedBehaviors.Add("High entropy - possible packer/crypter");

                    // IsFileSignedAsync itself performs certificate loading; await it
                    if (!await IsFileSignedAsync(filePath).ConfigureAwait(false))
                        result.PredictedBehaviors.Add("Unsigned executable - higher risk");
                }

                var directory = Path.GetDirectoryName(filePath)?.ToLowerInvariant();
                if (directory?.Contains("downloads") == true)
                    result.PredictedBehaviors.Add("Downloaded file - potential malware source");

                if (fileInfo.Length < 1024)
                    result.PredictedBehaviors.Add("Very small file - possible dropper");

                result.Confidence = result.PredictedBehaviors.Count * 0.15;
                result.PredictedMalicious = result.Confidence > 0.5;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<SignatureEvasionResult> DetectSignatureEvasionAsync(string filePath)
        {
            // Read bytes asynchronously and run detection logic on the calling thread after yielding
            var result = new SignatureEvasionResult();

            try
            {
                var content = await File.ReadAllBytesAsync(filePath).ConfigureAwait(false);

                // Synchronously analyze bytes after yielding to keep async contract without spinning up a new thread
                await Task.Yield();
                var r = new SignatureEvasionResult();
                r.JunkCodeDetected = DetectJunkCode(content);
                r.ObfuscationDetected = DetectCodeObfuscation(content);
                r.PolymorphismDetected = DetectPolymorphism(content);
                r.EncryptionDetected = DetectEncryption(content);
                r.EvasionScore = (r.JunkCodeDetected ? 0.2 : 0) +
                                 (r.ObfuscationDetected ? 0.3 : 0) +
                                 (r.PolymorphismDetected ? 0.3 : 0) +
                                 (r.EncryptionDetected ? 0.2 : 0);

                return r;
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private EntropyAnalysisResult AnalyzeEntropyAnomalies(string filePath)
        {
            var result = new EntropyAnalysisResult();

            try
            {
                var entropy = HashingService.ComputeFileEntropy(filePath);
                result.OverallEntropy = entropy;

                // Analyze entropy distribution
                result.EntropyDistribution = CalculateEntropyDistribution(filePath);

                // Detect entropy anomalies
                if (entropy > 7.5)
                    result.Anomalies.Add("Extremely high entropy - likely encrypted/packed");

                if (entropy < 2.0)
                    result.Anomalies.Add("Very low entropy - possible compressed or repetitive data");

                // Check for entropy transitions (potential encryption boundaries)
                result.EntropyTransitions = DetectEntropyTransitions(filePath);

                result.AnomalyScore = result.Anomalies.Count * 0.25;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<MemoryAnalysisResult> AnalyzeProcessMemoryAsync(Process process)
        {
            // Perform memory checks on the calling thread after yielding; keep async contract
            await Task.Yield();
            var result = new MemoryAnalysisResult();

            try
            {
                result.SuspiciousAllocations = DetectSuspiciousMemoryAllocations(process);
                result.CodeInjectionIndicators = DetectCodeInjection(process);
                result.HookDetection = DetectHooks(process);

                result.Score = (result.SuspiciousAllocations.Count * 0.3) +
                              (result.CodeInjectionIndicators.Count * 0.4) +
                              (result.HookDetection.Count * 0.3);
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        private async Task<NetworkAnalysisResult> AnalyzeProcessNetworkAsync(Process process)
        {
            await Task.Yield();
            var result = new NetworkAnalysisResult();

            try
            {
                result.SuspiciousConnections = DetectSuspiciousConnections(process);
                result.DataExfiltration = DetectDataExfiltration(process);
                result.CommandAndControl = DetectC2Traffic(process);

                result.Score = (result.SuspiciousConnections.Count * 0.3) +
                              (result.DataExfiltration.Count * 0.4) +
                              (result.CommandAndControl.Count * 0.3);
            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
            }

            return result;
        }

        // Helper methods for detection
        private List<string> DetectObfuscation(string content)
        {
            var indicators = new List<string>();

            // Check for base64-like patterns
            if (Regex.IsMatch(content, @"[A-Za-z0-9+/=]{100,}"))
                indicators.Add("Base64 encoded content detected");

            // Check for hex-encoded content
            if (Regex.IsMatch(content, @"\\x[0-9a-fA-F]{2}"))
                indicators.Add("Hex-encoded content detected");

            // Check for string concatenation obfuscation
            if (Regex.IsMatch(content, @"[""']\+[^""']*\+"".*[""']"))
                indicators.Add("String concatenation obfuscation");

            return indicators;
        }

        private List<string> DetectSuspiciousAPIs(string content)
        {
            var suspiciousAPIs = new[]
            {
                "VirtualAlloc", "VirtualProtect", "WriteProcessMemory", "CreateRemoteThread",
                "LoadLibrary", "GetProcAddress", "CreateProcess", "ShellExecute",
                "WinExec", "system", "exec", "popen"
            };

            return suspiciousAPIs.Where(api => content.Contains(api)).ToList();
        }

        private List<string> DetectShellcodePatterns(string content)
        {
            var patterns = new List<string>();

            // Common shellcode patterns
            if (Regex.IsMatch(content, @"\xE8\x00\x00\x00\x00")) // CALL next instruction
                patterns.Add("Shellcode CALL pattern detected");

            if (Regex.IsMatch(content, @"\xFC\xE8\x82\x00\x00\x00")) // Common shellcode prologue
                patterns.Add("Shellcode prologue detected");

            return patterns;
        }

        private List<string> DetectPolymorphicCode(string content)
        {
            var indicators = new List<string>();

            // Check for code that changes itself
            if (content.Contains("self-modifying") || content.Contains("polymorphic"))
                indicators.Add("Self-modifying code detected");

            // Check for dynamic code generation
            if (Regex.IsMatch(content, @"eval\(|Function\(|setTimeout\(|setInterval\("))
                indicators.Add("Dynamic code generation detected");

            return indicators;
        }

        private bool DetectJunkCode(byte[] content)
        {
            // Simple junk code detection - look for repetitive patterns
            if (content.Length < 100) return false;

            int consecutiveSame = 0;
            for (int i = 1; i < content.Length; i++)
            {
                if (content[i] == content[i - 1])
                {
                    consecutiveSame++;
                    if (consecutiveSame > 50) // More than 50 consecutive same bytes
                        return true;
                }
                else
                {
                    consecutiveSame = 0;
                }
            }

            return false;
        }

        private bool DetectCodeObfuscation(byte[] content)
        {
            // Check for high ratio of non-printable characters
            int nonPrintable = 0;
            foreach (var b in content)
            {
                if (b < 32 || b > 126) nonPrintable++;
            }

            return (double)nonPrintable / content.Length > 0.8;
        }

        private bool DetectPolymorphism(byte[] content)
        {
            // Check for code that appears to be encrypted or compressed
            var entropy = CalculateByteEntropy(content);
            return entropy > 7.0;
        }

        private bool DetectEncryption(byte[] content)
        {
            // Simple encryption detection based on entropy
            var entropy = CalculateByteEntropy(content);
            return entropy > 7.5;
        }

        private async Task<bool> IsFileSignedAsync(string filePath)
        {
            // Keep method async-friendly but perform certificate load synchronously after yielding
            await Task.Yield();
            try
            {
                var cert = new System.Security.Cryptography.X509Certificates.X509Certificate2(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private double CalculateByteEntropy(byte[] data)
        {
            if (data.Length == 0) return 0;

            var frequencies = new int[256];
            foreach (var b in data) frequencies[b]++;

            double entropy = 0;
            foreach (var freq in frequencies)
            {
                if (freq > 0)
                {
                    var p = (double)freq / data.Length;
                    entropy -= p * Math.Log(p, 2);
                }
            }

            return entropy;
        }

        private Dictionary<int, double> CalculateEntropyDistribution(string filePath)
        {
            // Simplified entropy distribution analysis
            return new Dictionary<int, double>();
        }

        private List<int> DetectEntropyTransitions(string filePath)
        {
            // Simplified entropy transition detection
            return new List<int>();
        }

        private List<string> DetectSuspiciousMemoryAllocations(Process process)
        {
            // Simplified memory allocation analysis
            return new List<string>();
        }

        private List<string> DetectCodeInjection(Process process)
        {
            // Simplified code injection detection
            return new List<string>();
        }

        private List<string> DetectHooks(Process process)
        {
            // Simplified hook detection
            return new List<string>();
        }

        private List<string> DetectSuspiciousConnections(Process process)
        {
            // Simplified network connection analysis
            return new List<string>();
        }

        private List<string> DetectDataExfiltration(Process process)
        {
            // Simplified data exfiltration detection
            return new List<string>();
        }

        private List<string> DetectC2Traffic(Process process)
        {
            // Simplified C2 traffic detection
            return new List<string>();
        }

        private double CalculateZeroDayScore(ZeroDayAnalysisResult result)
        {
            var score = 0.0;

            score += result.AnomalyDetection.Score * 0.25;
            score += result.CodeAnalysis.Score * 0.3;
            score += result.BehaviorPrediction.Confidence * 0.25;
            score += result.SignatureEvasion.EvasionScore * 0.15;
            score += result.EntropyAnalysis.AnomalyScore * 0.05;

            return Math.Min(score, 1.0);
        }

        private double CalculateProcessZeroDayScore(ZeroDayAnalysisResult result)
        {
            var score = 0.0;

            score += result.AnomalyDetection.Score * 0.3;
            score += result.BehaviorPrediction.Confidence * 0.3;
            score += (result.MemoryAnalysis?.Score ?? 0) * 0.25;
            score += (result.NetworkAnalysis?.Score ?? 0) * 0.15;

            return Math.Min(score, 1.0);
        }

        private List<string> GenerateDetectionReasons(ZeroDayAnalysisResult result)
        {
            var reasons = new List<string>();

            if (result.AnomalyDetection.Anomalies.Count > 0)
                reasons.AddRange(result.AnomalyDetection.Anomalies);

            if (result.CodeAnalysis.ObfuscationIndicators.Count > 0)
                reasons.AddRange(result.CodeAnalysis.ObfuscationIndicators);

            if (result.CodeAnalysis.SuspiciousAPIs.Count > 0)
                reasons.Add($"Suspicious APIs: {string.Join(", ", result.CodeAnalysis.SuspiciousAPIs)}");

            if (result.SignatureEvasion.EvasionScore > 0.5)
                reasons.Add("Signature evasion techniques detected");

            return reasons;
        }

        private List<string> GenerateProcessDetectionReasons(ZeroDayAnalysisResult result)
        {
            var reasons = new List<string>();

            if (result.AnomalyDetection.Anomalies.Count > 0)
                reasons.AddRange(result.AnomalyDetection.Anomalies);

            if (result.BehaviorPrediction.PredictedBehaviors.Count > 0)
                reasons.AddRange(result.BehaviorPrediction.PredictedBehaviors);

            if (result.MemoryAnalysis?.SuspiciousAllocations.Count > 0)
                reasons.AddRange(result.MemoryAnalysis.SuspiciousAllocations);

            if (result.NetworkAnalysis?.SuspiciousConnections.Count > 0)
                reasons.AddRange(result.NetworkAnalysis.SuspiciousConnections);

            return reasons;
        }

        private Dictionary<string, ZeroDayPattern> LoadZeroDayPatterns()
        {
            if (!File.Exists(_zeroDayDbPath))
            {
                return new Dictionary<string, ZeroDayPattern>();
            }

            try
            {
                var json = File.ReadAllText(_zeroDayDbPath);
                var patterns = JsonSerializer.Deserialize<List<ZeroDayPattern>>(json) ?? new List<ZeroDayPattern>();
                return patterns.ToDictionary(p => p.PatternId);
            }
            catch
            {
                return new Dictionary<string, ZeroDayPattern>();
            }
        }
    }

    // Supporting classes
    public class ZeroDayAnalysisResult
    {
        public string FilePath { get; set; } = string.Empty;
        public int? ProcessId { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public AnomalyDetectionResult AnomalyDetection { get; set; } = new();
        public CodeAnalysisResult CodeAnalysis { get; set; } = new();
        public BehaviorPredictionResult BehaviorPrediction { get; set; } = new();
        public SignatureEvasionResult SignatureEvasion { get; set; } = new();
        public EntropyAnalysisResult EntropyAnalysis { get; set; } = new();
        public MemoryAnalysisResult? MemoryAnalysis { get; set; }
        public NetworkAnalysisResult? NetworkAnalysis { get; set; }
        public double ZeroDayScore { get; set; }
        public bool IsPotentialZeroDay { get; set; }
        public List<string> DetectionReasons { get; set; } = new();
        public string Error { get; set; } = string.Empty;

        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
    }

    public class AnomalyDetectionResult
    {
        public double Score { get; set; }
        public List<string> Anomalies { get; set; } = new();
    }

    public class CodeAnalysisResult
    {
        public double Score { get; set; }
        public List<string> ObfuscationIndicators { get; set; } = new();
        public List<string> SuspiciousAPIs { get; set; } = new();
        public List<string> ShellcodePatterns { get; set; } = new();
        public List<string> PolymorphicIndicators { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class BehaviorPredictionResult
    {
        public bool PredictedMalicious { get; set; }
        public double Confidence { get; set; }
        public List<string> PredictedBehaviors { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class SignatureEvasionResult
    {
        public double EvasionScore { get; set; }
        public bool JunkCodeDetected { get; set; }
        public bool ObfuscationDetected { get; set; }
        public bool PolymorphismDetected { get; set; }
        public bool EncryptionDetected { get; set; }
        public string Error { get; set; } = string.Empty;
    }

    public class EntropyAnalysisResult
    {
        public double OverallEntropy { get; set; }
        public Dictionary<int, double> EntropyDistribution { get; set; } = new();
        public List<int> EntropyTransitions { get; set; } = new();
        public double AnomalyScore { get; set; }
        public List<string> Anomalies { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class MemoryAnalysisResult
    {
        public double Score { get; set; }
        public List<string> SuspiciousAllocations { get; set; } = new();
        public List<string> CodeInjectionIndicators { get; set; } = new();
        public List<string> HookDetection { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class NetworkAnalysisResult
    {
        public double Score { get; set; }
        public List<string> SuspiciousConnections { get; set; } = new();
        public List<string> DataExfiltration { get; set; } = new();
        public List<string> CommandAndControl { get; set; } = new();
        public string Error { get; set; } = string.Empty;
    }

    public class ZeroDayPattern
    {
        public string PatternId { get; set; } = string.Empty;
        public string PatternType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public DateTime Discovered { get; set; }
        public int DetectionCount { get; set; }
    }
}
