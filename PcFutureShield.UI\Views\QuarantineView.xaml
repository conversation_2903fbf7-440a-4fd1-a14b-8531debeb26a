<UserControl x:Class="PcFutureShield.UI.Views.QuarantineView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="400" d:DesignWidth="600">
    <Grid Margin="20" Background="{DynamicResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Quarantine" FontSize="28" FontWeight="Bold" Margin="0,0,0,20" Foreground="{DynamicResource PrimaryFontBrush}" Background="{DynamicResource CardBackgroundBrush}"/>
        <ListBox Grid.Row="1" ItemsSource="{Binding QuarantinedItems}" Margin="0,10,0,0" Foreground="#FFEEFBFB" BorderBrush="#FFF3F505">
            <ListBox.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF0CB4E0" Offset="1"/>
                </LinearGradientBrush>
            </ListBox.Background>
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBlock Text="{Binding FileName}" FontSize="16"/>
                        <Button Content="Restore" Margin="20,0,0,0" Command="{Binding DataContext.RestoreCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}"/>
                        <Button Content="Delete" Margin="10,0,0,0" Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}"/>
                        <Button Content="Details" Margin="10,0,0,0" Command="{Binding DataContext.ViewDetailsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}" CommandParameter="{Binding}"/>
                    </StackPanel>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
    </Grid>
</UserControl>
