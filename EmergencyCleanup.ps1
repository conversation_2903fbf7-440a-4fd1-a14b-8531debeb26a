# EMERGENCY CLEANUP SCRIPT - Run as Administrator
# This will completely restore your internet access

Write-Host "EMERGENCY INTERNET ACCESS RESTORATION" -ForegroundColor Red
Write-Host "=====================================" -ForegroundColor Red

$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

# 1. Create backup
Write-Host "Creating backup..." -ForegroundColor Yellow
Copy-Item $hostsPath "$hostsPath.emergency.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')" -Force

# 2. Read original hosts file and remove ALL PcFutureShield entries
Write-Host "Cleaning hosts file..." -ForegroundColor Yellow
$originalContent = Get-Content $hostsPath
$cleanContent = @()

foreach ($line in $originalContent) {
    # Skip any line containing PcFutureShield
    if ($line -notmatch "PcFutureShield" -and 
        $line -notmatch "# Blocked by PcFutureShield" -and
        $line -notmatch "# End PcFutureShield" -and
        $line -notmatch "# PcFutureShield DNS Filter") {
        $cleanContent += $line
    }
}

# 3. Write clean content back
$cleanContent | Out-File -FilePath $hostsPath -Encoding ASCII -Force
Write-Host "Hosts file cleaned!" -ForegroundColor Green

# 4. Reset ALL network adapters to DHCP DNS
Write-Host "Resetting DNS settings..." -ForegroundColor Yellow
$adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
foreach ($adapter in $adapters) {
    try {
        Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ResetServerAddresses
        Write-Host "Reset DNS for: $($adapter.Name)" -ForegroundColor Green
    } catch {
        Write-Host "Failed to reset DNS for: $($adapter.Name)" -ForegroundColor Red
    }
}

# 5. Flush DNS cache multiple times
Write-Host "Flushing DNS cache..." -ForegroundColor Yellow
ipconfig /flushdns | Out-Null
Start-Sleep -Seconds 2
ipconfig /flushdns | Out-Null
Write-Host "DNS cache flushed!" -ForegroundColor Green

# 6. Remove any PcFutureShield firewall rules
Write-Host "Removing firewall rules..." -ForegroundColor Yellow
try {
    $rules = Get-NetFirewallRule | Where-Object { $_.DisplayName -like "*PcFutureShield*" }
    foreach ($rule in $rules) {
        Remove-NetFirewallRule -Name $rule.Name -Confirm:$false
        Write-Host "Removed rule: $($rule.DisplayName)" -ForegroundColor Green
    }
    if ($rules.Count -eq 0) {
        Write-Host "No PcFutureShield firewall rules found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error removing firewall rules: $($_.Exception.Message)" -ForegroundColor Red
}

# 7. Test connectivity
Write-Host "Testing connectivity..." -ForegroundColor Yellow
$testSites = @("google.com", "microsoft.com", "github.com")
foreach ($site in $testSites) {
    try {
        $result = Test-NetConnection -ComputerName $site -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($result) {
            Write-Host "✓ $site - ACCESSIBLE" -ForegroundColor Green
        } else {
            Write-Host "✗ $site - NOT ACCESSIBLE" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ $site - ERROR TESTING" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "EMERGENCY CLEANUP COMPLETE!" -ForegroundColor Green
Write-Host "Your internet access should now be fully restored." -ForegroundColor White
Write-Host ""
Write-Host "What was done:" -ForegroundColor Cyan
Write-Host "• Removed ALL PcFutureShield entries from hosts file" -ForegroundColor White
Write-Host "• Reset ALL network adapters to automatic DNS (DHCP)" -ForegroundColor White
Write-Host "• Flushed DNS cache multiple times" -ForegroundColor White
Write-Host "• Removed any PcFutureShield firewall rules" -ForegroundColor White
Write-Host ""
Write-Host "If you still have issues:" -ForegroundColor Yellow
Write-Host "1. Restart your browser completely" -ForegroundColor White
Write-Host "2. Try incognito/private browsing mode" -ForegroundColor White
Write-Host "3. Restart your computer" -ForegroundColor White
Write-Host "4. Contact your ISP if problems persist" -ForegroundColor White

Read-Host "Press Enter to exit"
