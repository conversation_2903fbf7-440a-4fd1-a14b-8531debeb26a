using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace AutoValidationTool.Generated
{
    public class LogerViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));

        public LogerViewModel() { }

        private string _sampleProperty;
        public string SampleProperty
        {
            get => _sampleProperty;
            set { if (_sampleProperty != value) { _sampleProperty = value; OnPropertyChanged(); } }
        }

        // Simple RelayCommand implementation
        private class RelayCommand : ICommand
        {
            private readonly Action _execute;
            private readonly Func<bool>? _canExecute;
            public RelayCommand(Action execute, Func<bool>? canExecute = null) { _execute = execute; _canExecute = canExecute; }
            public event EventHandler? CanExecuteChanged;
            public bool CanExecute(object? parameter) => _canExecute?.Invoke() ?? true;
            public void Execute(object? parameter) => _execute();
            public void RaiseCanExecuteChanged() => CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }

        private ICommand? _doSomethingCommand;
        public ICommand DoSomethingCommand => _doSomethingCommand ??= new RelayCommand(() => { /* TODO: implement */ });
    }
}
