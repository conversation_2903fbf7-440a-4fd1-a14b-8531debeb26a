﻿{
  "FilesScanned": 237,
  "Findings": [
    {
      "File": ".\\PcFutureShield.RealtimeScanner\\Program.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 41,
      "Severity": 1,
      "Preview": "nWithExitCode(this IHost host)\n        {\n            try\n            {\n                host.Run();\n                return 0;\n            }\n            catch (Exception ex)\n            {\n                var logger = host.Services.Ge"
    },
    {
      "File": ".\\PcFutureShield.RealtimeScanner\\Worker.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 28,
      "Severity": 1,
      "Preview": "{\n            _logger = logger;\n            _options = options.Value;\n        }\n\n        public override async Task StartAsync(CancellationToken cancellationToken)\n        {\n            _logger.LogInformation(\u0022RealtimeScann"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\Program.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 217,
      "Severity": 1,
      "Preview": "var text = File.ReadAllText(file);\n                var edited = text;\n\n                // Simple regex: find \u0027async Task\u0027 method headers and check for \u0027await\u0027 presence\n                var methodPattern = new Regex(@\u0022async\\s\u002BTask("
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\Program.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 129,
      "Severity": 1,
      "Preview": "ports written to: {output}\u0022);\n        if (failOnFindings \u0026\u0026 report.Findings.Count \u003E 0)\n            return 2;\n        return 0;\n    }\n\n    private static int GetLineNumber(string text, int index)\n    {\n        return text[..Math.Min("
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\Program.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 148,
      "Severity": 0,
      "Preview": "Files Scanned[/]\u0022, report.FilesScanned.ToString(), \u0022[bold]Findings[/]\u0022, report.Findings.Count.ToString());\n        AnsiConsole.Write(grid);\n\n        var table = new Table().Border(TableBorder.Rounded);\n        table.AddColumn(\u0022Rule\u0022);"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\Program.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 167,
      "Severity": 0,
      "Preview": ".Markup.Escape(\u0022\u0060\u0060\u0060\u0022 \u002B ex \u002B \u0022\u0060\u0060\u0060\u0022);\n            table.AddRow(title, severity, count, example);\n        }\n        AnsiConsole.Write(table);\n\n        var paths = new Tree(\u0022[bold]Top Offenders (by file)[/]\u0022);\n        foreach (var fileGro"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\Program.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 176,
      "Severity": 0,
      "Preview": "node.AddNode(Spectre.Console.Markup.Escape($\u0022L{f.Line}: {f.RuleId} \u2014 {f.Message}\u0022));\n        }\n        AnsiConsole.Write(paths);\n    }\n\n    private static void WriteArtifacts(ScanReport report, string outputDir)\n    {"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R003",
      "Message": "StubViewModel or \u0027Functionality not available\u0027 present",
      "Line": 13,
      "Severity": 2,
      "Preview": "\\*\\s*(TODO|FIXME|HACK)\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R003\u0022, \u0022Stub ViewModel\u0022, \u0022StubViewModel or \u0027Functionality not available\u0027 present\u0022, Severity.Error, new Regex(@\u0022StubViewModel|Functionality\\s\u002Bnot\\s"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R003",
      "Message": "StubViewModel or \u0027Functionality not available\u0027 present",
      "Line": 13,
      "Severity": 2,
      "Preview": "ACK)\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R003\u0022, \u0022Stub ViewModel\u0022, \u0022StubViewModel or \u0027Functionality not available\u0027 present\u0022, Severity.Error, new Regex(@\u0022StubViewModel|Functionality\\s\u002Bnot\\s\u002Bavailable\u0022, Regex"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R003",
      "Message": "StubViewModel or \u0027Functionality not available\u0027 present",
      "Line": 13,
      "Severity": 2,
      "Preview": "new(\u0022R003\u0022, \u0022Stub ViewModel\u0022, \u0022StubViewModel or \u0027Functionality not available\u0027 present\u0022, Severity.Error, new Regex(@\u0022StubViewModel|Functionality\\s\u002Bnot\\s\u002Bavailable\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R00"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 12,
      "Severity": 1,
      "Preview": "row\\s\u002Bnew\\s\u002BNotImplementedException\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R002\u0022, \u0022TODO Placeholder\u0022, \u0022Contains TODO/FIXME/HACK comment\u0022, Severity.Warning, new Regex(@\u0022//\\s*(TODO|FIXME|HACK)|/\\*\\s*(TODO|FIXME"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 13,
      "Severity": 1,
      "Preview": "TODO|FIXME|HACK)|/\\*\\s*(TODO|FIXME|HACK)\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R003\u0022, \u0022Stub ViewModel\u0022, \u0022StubViewModel or \u0027Functionality not available\u0027 present\u0022, Severity.Error, new Regex(@\u0022StubViewModel|Fun"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 13,
      "Severity": 1,
      "Preview": "\\*\\s*(TODO|FIXME|HACK)\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R003\u0022, \u0022Stub ViewModel\u0022, \u0022StubViewModel or \u0027Functionality not available\u0027 present\u0022, Severity.Error, new Regex(@\u0022StubViewModel|Functionality\\s\u002Bnot\\s"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 13,
      "Severity": 1,
      "Preview": "new(\u0022R003\u0022, \u0022Stub ViewModel\u0022, \u0022StubViewModel or \u0027Functionality not available\u0027 present\u0022, Severity.Error, new Regex(@\u0022StubViewModel|Functionality\\s\u002Bnot\\s\u002Bavailable\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R00"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "iewModel|Functionality\\s\u002Bnot\\s\u002Bavailable\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placehold"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "ity\\s\u002Bnot\\s\u002Bavailable\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.C"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "\\s\u002Bavailable\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled |"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "ailable\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | Rege"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "le\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOpti"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOptions.I"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "ase)),\n            new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R005\u0022, \u0022Async"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R005\u0022, \u0022Async Task.Com"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R005\u0022, \u0022Async Task.Complete"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "new(\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R005\u0022, \u0022Async Task.CompletedTask"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 14,
      "Severity": 1,
      "Preview": "\u0022R004\u0022, \u0022Simulated Logic\u0022, \u0022simulate/stub/fake/mock/placeholder\u0022, Severity.Warning, new Regex(@\u0022simulate|stub|fake|mock|placeholder\u0022, RegexOptions.Compiled | RegexOptions.IgnoreCase)),\n            new(\u0022R005\u0022, \u0022Async Task.CompletedTask\u0022, \u0022A"
    },
    {
      "File": ".\\PcFutureShield.Tools.AutoValidator\\ValidationRules.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 16,
      "Severity": 1,
      "Preview": "ask\\.CompletedTask\\s*;\u0022, RegexOptions.Compiled)),\n            new(\u0022R006\u0022, \u0022Empty Method Body\u0022, \u0022Method has empty body (stub)\u0022, Severity.Warning, new Regex(@\u0022\\b(public|private|protected|internal)\\s\u002B(async\\s\u002B)?[\\w\u003C\u003E\\[\\]]\u002B\\s\u002B\\w\u002B\\s*\\([^)]*\\)\\s"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 67,
      "Severity": 1,
      "Preview": "ineManager\u003E(new PcFutureShield.Engine.Quarantine.QuarantineManager());\n                        ServiceLocator.Register\u003CIRealtimeProtectionService\u003E(new RealtimeProtectionService());\n                        ServiceLocator.Register\u003CIEventLog"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 68,
      "Severity": 1,
      "Preview": "Register\u003CIRealtimeProtectionService\u003E(new RealtimeProtectionService());\n                        ServiceLocator.Register\u003CIEventLogService\u003E(new EventLogService());\n                        ServiceLocator.Register\u003CILicenseManager\u003E(new LicenseM"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 72,
      "Severity": 1,
      "Preview": "seManager());\n\n                        Update(35, \u0022Starting analysis engines...\u0022);\n                        var threatIntelligenceService = new ThreatIntelligenceService();\n                        var behavioralAnalysisService = new Beha"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 72,
      "Severity": 1,
      "Preview": "Update(35, \u0022Starting analysis engines...\u0022);\n                        var threatIntelligenceService = new ThreatIntelligenceService();\n                        var behavioralAnalysisService = new BehavioralAnalysisService();"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 74,
      "Severity": 1,
      "Preview": "();\n                        var zeroDayDetectionService = new ZeroDayDetectionService(behavioralAnalysisService, threatIntelligenceService);\n                        var aiDetectionService = new AdvancedAIDetectionService();"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 75,
      "Severity": 1,
      "Preview": "e(behavioralAnalysisService, threatIntelligenceService);\n                        var aiDetectionService = new AdvancedAIDetectionService();\n\n                        ServiceLocator.Register\u003CThreatIntelligenceService\u003E(threatIntelligenceSer"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 77,
      "Severity": 1,
      "Preview": "var aiDetectionService = new AdvancedAIDetectionService();\n\n                        ServiceLocator.Register\u003CThreatIntelligenceService\u003E(threatIntelligenceService);\n                        ServiceLocator.Register\u003CBehavioralAnalysisServ"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 77,
      "Severity": 1,
      "Preview": "= new AdvancedAIDetectionService();\n\n                        ServiceLocator.Register\u003CThreatIntelligenceService\u003E(threatIntelligenceService);\n                        ServiceLocator.Register\u003CBehavioralAnalysisService\u003E(behavioralAnalysisServ"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 80,
      "Severity": 1,
      "Preview": "r.Register\u003CZeroDayDetectionService\u003E(zeroDayDetectionService);\n                        ServiceLocator.Register\u003CAdvancedAIDetectionService\u003E(aiDetectionService);\n\n                        var antivirusOrchestrator = new AntivirusOrchestrator"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 86,
      "Severity": 1,
      "Preview": "aiDetectionService,\n                            behavioralAnalysisService,\n                            threatIntelligenceService,\n                            Microsoft.Extensions.Logging.Abstractions.NullLogger\u003CAntivirusOrchestr"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 102,
      "Severity": 1,
      "Preview": "irusScannerService(virusScannerLogger);\n\n                        ServiceLocator.Register\u003CPcFutureShield.UI.ViewModels.IVirusScannerService\u003E(\n                            new PcFutureShield.UI.ViewModels.VirusScannerServiceAdapter(virusSca"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 107,
      "Severity": 1,
      "Preview": "// Register notification service for background tasks\n                        ServiceLocator.Register\u003CINotificationService\u003E(new WpfNotificationService());\n\n                        Update(85, \u0022Finalizing startup...\u0022);"
    },
    {
      "File": ".\\PcFutureShield.UI\\App.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 134,
      "Severity": 1,
      "Preview": "registered notifier\n                                var notifier = ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                                notifier?.ShowError(\u0022Startup Error\u0022, $\u0022Failed to initialize ap"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 60,
      "Severity": 1,
      "Preview": "var behavioralAnalysis = new BehavioralAnalysisService();\n                var threatIntelligence = new ThreatIntelligenceService();\n\n                _antivirusOrchestrator = new AntivirusOrchestrator(\n                    new Z"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 64,
      "Severity": 1,
      "Preview": "new ZeroDayDetectionService(behavioralAnalysis, threatIntelligence),\n                    new AdvancedAIDetectionService(),\n                    behavioralAnalysis,\n                    threatIntelligence,"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 87,
      "Severity": 1,
      "Preview": "try\n            {\n                DashboardViewModel = new DashboardViewModel(_antivirusOrchestrator, new ThreatIntelligenceService());\n                ParentalControlViewModel = new ParentalControlViewModel(_parentalControlService"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainWindow.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 82,
      "Severity": 1,
      "Preview": "nViewModel: {ex.Message}\u0022, ex);\n                    var notifier = ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                    if (notifier != null)\n                        notifier.ShowError(\u0022Initial"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainWindow.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 119,
      "Severity": 1,
      "Preview": "led to initialize MainWindow\u0022, ex);\n                var notifier = ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                if (notifier != null)\n                    notifier.ShowError(\u0022Startup Error\u0022,"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainWindow.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 207,
      "Severity": 1,
      "Preview": "tion will continue with the current theme.\u0022;\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowWarning(\u0022Theme Loading Error\u0022, errorMessage); } catch { System.Windows.Application.Current?"
    },
    {
      "File": ".\\PcFutureShield.UI\\MainWindow.xaml.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 227,
      "Severity": 1,
      "Preview": "w\u0022, \u0022Failed to open log viewer\u0022, ex);\n                var notify = ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                if (notify != null) notify.ShowError(\u0022Error\u0022, $\u0022Failed to open log viewer: {ex"
    },
    {
      "File": ".\\PcFutureShield.Common\\Interfaces\\IVirusScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 45,
      "Severity": 1,
      "Preview": "/// \u003Csummary\u003E\n    /// Provides virus scanning and threat detection services\n    /// \u003C/summary\u003E\n    public interface IVirusScannerService : IDisposable\n    {\n        /// \u003Csummary\u003E\n        /// Event raised when a threat is detected"
    },
    {
      "File": ".\\PcFutureShield.Common\\Interfaces\\QuarantineTypes.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 6,
      "Severity": 1,
      "Preview": "contained duplicate definitions of QuarantineItem and QuarantineResult\n    // These types are now properly defined in IVirusScannerService.cs and IQuarantineManager.cs\n    // This file can be removed or left empty to avoid build conflict"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 42,
      "Severity": 1,
      "Preview": "cipal(identity);\n            return principal.IsInRole(WindowsBuiltInRole.Administrator);\n        }\n\n        public async Task\u003CAdminRequestResult\u003E RequestAdminAccessAsync(string reason, TimeSpan duration)\n        {\n            var res"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 83,
      "Severity": 1,
      "Preview": "ailed to request admin access: {ex.Message}\u0022;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CElevationResult\u003E ElevateProcessAsync(string processPath, string arguments = \u0022\u0022)\n        {\n            var r"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 120,
      "Severity": 1,
      "Preview": "= $\u0022Failed to elevate process: {ex.Message}\u0022;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CAdminSessionResult\u003E CreateTemporaryAdminSessionAsync(string reason, TimeSpan duration)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 173,
      "Severity": 1,
      "Preview": "return _pendingRequests.Where(r =\u003E r.Status == AdminRequestStatus.Pending).ToList();\n        }\n\n        public async Task ApproveAdminRequestAsync(string requestId, string approvedBy)\n        {\n            var request = _pendingR"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 190,
      "Severity": 1,
      "Preview": "request.SessionId = sessionId;\n\n                SavePendingRequests();\n            }\n        }\n\n        public async Task DenyAdminRequestAsync(string requestId, string deniedBy, string reason)\n        {\n            var request ="
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 239,
      "Severity": 1,
      "Preview": "eRemaining = s.ExpiresAt - DateTime.UtcNow\n                })\n                .ToList();\n        }\n\n        public async Task\u003CSecurityAuditResult\u003E PerformSecurityAuditAsync()\n        {\n            var result = new SecurityAuditResult"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 9,
      "Severity": 1,
      "Preview": "using System.Linq;\nusing System.IO;\nusing System.Diagnostics;\nusing System.Security.Principal;\nusing System.Runtime.InteropServices;\nusing System.Threading.Tasks;\nusing System.Text.Json;\n\nnamespace PcFutureShield.Common.Services\n{"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 148,
      "Severity": 1,
      "Preview": "sion))\n            {\n                if (session.ExpiresAt \u003E DateTime.UtcNow)\n                {\n                    return true;\n                }\n                else\n                {\n                    // Session expired, remove"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 157,
      "Severity": 1,
      "Preview": "veSessions.Remove(sessionId);\n                    SaveActiveSessions();\n                }\n            }\n            return false;\n        }\n\n        public void EndAdminSession(string sessionId)\n        {\n            if (_activeSes"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 378,
      "Severity": 0,
      "Preview": "File.WriteAllText(_adminDbPath, json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving active sessions: {ex.Message}\u0022);\n            }\n        }\n\n        private void Save"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdminOverrideService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 392,
      "Severity": 0,
      "Preview": "File.WriteAllText(requestsPath, json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving pending requests: {ex.Message}\u0022);\n            }\n        }\n    }\n\n    // Supporting"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 157,
      "Severity": 1,
      "Preview": "Label = true\n                });\n            }\n\n            return data;\n        }\n\n        public async Task\u003CAIAnalysisResult\u003E AnalyzeFileAsync(string filePath)\n        {\n            var result = new AIAnalysisResult"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 295,
      "Severity": 1,
      "Preview": "}\n            catch\n            {\n                return 0;\n            }\n        }\n\n        private async Task\u003CList\u003Cstring\u003E\u003E AnalyzeBehavioralPatternsAsync(string filePath)\n        {\n            var indicators = new Li"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 378,
      "Severity": 1,
      "Preview": "return ThreatRisk.Safe;\n        }\n\n        // Enhanced system analysis with AI insights\n        public async Task\u003CAIEnhancedAnalysisResult\u003E AnalyzeSystemAsync()\n        {\n            var result = new AIEnhancedAnalysisRes"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 477,
      "Severity": 1,
      "Preview": "result.EndTime = DateTime.UtcNow;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CList\u003Cstring\u003E\u003E GetAutorunEntriesAsync()\n        {\n            var autorunEntries = new List\u003Cstring\u003E();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 22,
      "Severity": 1,
      "Preview": "d AI-powered threat detection service with machine learning capabilities\n    /// \u003C/summary\u003E\n    public class AdvancedAIDetectionService\n    {\n        private readonly MLContext _mlContext;\n        private ITransformer? _trainedModel;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 56,
      "Severity": 1,
      "Preview": "PackedFloat { get; set; }\n            public float HasImportsFloat { get; set; }\n        }\n\n        public AdvancedAIDetectionService()\n        {\n            _mlContext = new MLContext(seed: 0);\n            var appData = Environment."
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 291,
      "Severity": 1,
      "Preview": "ntains(pattern, StringComparison.OrdinalIgnoreCase));\n            }\n            catch\n            {\n                return 0;\n            }\n        }\n\n        private async Task\u003CList\u003Cstring\u003E\u003E AnalyzeBehavioralPatternsAsync(string fi"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 548,
      "Severity": 1,
      "Preview": "y.Cryptography.X509Certificates.X509Certificate2(filePath);\n                publisher = cert.Subject;\n                return true;\n            }\n            catch { return false; }\n        }\n\n        private static string ThreatIntel"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 550,
      "Severity": 1,
      "Preview": "filePath);\n                publisher = cert.Subject;\n                return true;\n            }\n            catch { return false; }\n        }\n\n        private static string ThreatIntelLookup(string sha256, string name, string path)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 83,
      "Severity": 0,
      "Preview": "rainInitialModel();\n                }\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022AI Model loading failed: {ex.Message}\u0022);\n                TrainInitialModel();\n            }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AdvancedAIDetectionService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 244,
      "Severity": 0,
      "Preview": "= CountSuspiciousStrings(filePath);\n\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Feature extraction error: {ex.Message}\u0022);\n            }\n\n            return features;\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AIDetectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 8,
      "Severity": 1,
      "Preview": "g System.Linq;\nusing PcFutureShield.Common.Services;\n\nnamespace PcFutureShield.Common.Services\n{\n    public class AIDetectionService\n    {\n        public string AnalyzeSystem()\n        {\n            // --- REAL AI/SECURITY ANALYSIS"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AIDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 145,
      "Severity": 1,
      "Preview": "graphy.X509Certificates.X509Certificate2(filePath);\n                    publisher = cert.Subject;\n                    return true;\n                }\n                catch { return false; }\n            }\n\n            static string Thr"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AIDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 147,
      "Severity": 1,
      "Preview": "publisher = cert.Subject;\n                    return true;\n                }\n                catch { return false; }\n            }\n\n            static string ThreatIntelLookup(string sha256, string name, string path)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 192,
      "Severity": 1,
      "Preview": "return scanResult;\n                    }\n                    catch\n                    {\n                        return null;\n                    }\n                });\n\n                var processResults = await Task.WhenAll(proc"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 48,
      "Severity": 1,
      "Preview": "ombine(resultsPath, \u0022scan_history.json\u0022);\n\n            _scanHistory = LoadScanHistory();\n        }\n\n        public async Task\u003CComprehensiveScanResult\u003E PerformComprehensiveScanAsync(string filePath)\n        {\n            var result ="
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 109,
      "Severity": 1,
      "Preview": "mprehensive\u0022\n            };\n            SaveScanHistory();\n\n            return result;\n        }\n\n        public async Task\u003CComprehensiveScanResult\u003E PerformProcessScanAsync(Process process)\n        {\n            var result = new Co"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 168,
      "Severity": 1,
      "Preview": "= \u0022Process\u0022\n            };\n            SaveScanHistory();\n\n            return result;\n        }\n\n        public async Task\u003CSystemScanResult\u003E PerformSystemScanAsync()\n        {\n            var result = new SystemScanResult"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 221,
      "Severity": 1,
      "Preview": "}\n\n            result.EndTime = DateTime.UtcNow;\n            return result;\n        }\n\n        public async Task\u003CComprehensiveScanResult\u003E PerformURLScanAsync(string url)\n        {\n            var result = new Comprehensi"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 20,
      "Severity": 1,
      "Preview": "estrator\n    {\n        private readonly ZeroDayDetectionService _zeroDayDetection;\n        private readonly AdvancedAIDetectionService _aiDetection;\n        private readonly BehavioralAnalysisService _behavioralAnalysis;\n        privat"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 22,
      "Severity": 1,
      "Preview": "_aiDetection;\n        private readonly BehavioralAnalysisService _behavioralAnalysis;\n        private readonly ThreatIntelligenceService _threatIntelligence;\n        private readonly string _scanResultsPath;\n        private readonly Mi"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 29,
      "Severity": 1,
      "Preview": ";\n\n        public AntivirusOrchestrator(\n            ZeroDayDetectionService zeroDayDetection,\n            AdvancedAIDetectionService aiDetection,\n            BehavioralAnalysisService behavioralAnalysis,\n            ThreatIntelligenc"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 31,
      "Severity": 1,
      "Preview": "AdvancedAIDetectionService aiDetection,\n            BehavioralAnalysisService behavioralAnalysis,\n            ThreatIntelligenceService threatIntelligence,\n            Microsoft.Extensions.Logging.ILogger\u003CAntivirusOrchestrator\u003E logger)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 320,
      "Severity": 1,
      "Preview": "/ Threat if any service detects with high confidence\n            if (result.ZeroDayResult?.IsPotentialZeroDay == true) return true;\n            if (result.AIResult?.IsMalicious == true \u0026\u0026 result.AIResult.Confidence \u003E 0.7) return true;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 321,
      "Severity": 1,
      "Preview": "eroDay == true) return true;\n            if (result.AIResult?.IsMalicious == true \u0026\u0026 result.AIResult.Confidence \u003E 0.7) return true;\n            if (result.ThreatIntelResult?.IsMalicious == true) return true;\n            if (result.Behavi"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 322,
      "Severity": 1,
      "Preview": "true \u0026\u0026 result.AIResult.Confidence \u003E 0.7) return true;\n            if (result.ThreatIntelResult?.IsMalicious == true) return true;\n            if (result.BehavioralResult?.RiskLevel \u003E= ThreatRisk.Medium) return true;\n\n            // Ov"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 323,
      "Severity": 1,
      "Preview": "ntelResult?.IsMalicious == true) return true;\n            if (result.BehavioralResult?.RiskLevel \u003E= ThreatRisk.Medium) return true;\n\n            // Overall score threshold\n            return result.OverallThreatScore \u003E 0.6;\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 331,
      "Severity": 1,
      "Preview": "ssVerdict(ComprehensiveScanResult result)\n        {\n            if (result.ZeroDayResult?.IsPotentialZeroDay == true) return true;\n            if (result.AIResult?.IsMalicious == true \u0026\u0026 result.AIResult.Confidence \u003E 0.6) return true;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 332,
      "Severity": 1,
      "Preview": "eroDay == true) return true;\n            if (result.AIResult?.IsMalicious == true \u0026\u0026 result.AIResult.Confidence \u003E 0.6) return true;\n            if (result.BehavioralResult?.RiskLevel \u003E= ThreatRisk.Medium) return true;\n\n            retur"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\AntivirusOrchestrator.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 333,
      "Severity": 1,
      "Preview": "esult.AIResult.Confidence \u003E 0.6) return true;\n            if (result.BehavioralResult?.RiskLevel \u003E= ThreatRisk.Medium) return true;\n\n            return result.OverallThreatScore \u003E 0.5;\n        }\n\n        private double CalculateOveral"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 31,
      "Severity": 1,
      "Preview": "ehaviorPath, \u0022behavior_db.json\u0022);\n            _behaviorDatabase = LoadBehaviorDatabase();\n        }\n\n        public async Task\u003CBehavioralAnalysisResult\u003E AnalyzeProcessAsync(Process process)\n        {\n            var result = new Behav"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 100,
      "Severity": 1,
      "Preview": "d($\u0022Memory analysis error: {ex.Message}\u0022);\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CNetworkAnalysis\u003E AnalyzeNetworkActivityAsync(Process process)\n        {\n            var analysis = new Netw"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 126,
      "Severity": 1,
      "Preview": "($\u0022Network analysis error: {ex.Message}\u0022);\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CFileSystemAnalysis\u003E AnalyzeFileSystemActivityAsync(Process process)\n        {\n            var analysis = ne"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 160,
      "Severity": 1,
      "Preview": "ile system analysis error: {ex.Message}\u0022);\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CRegistryAnalysis\u003E AnalyzeRegistryActivityAsync(Process process)\n        {\n            var analysis = new Re"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 194,
      "Severity": 1,
      "Preview": "$\u0022Registry analysis error: {ex.Message}\u0022);\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CApiAnalysis\u003E AnalyzeApiCallsAsync(Process process)\n        {\n            var analysis = new ApiAnalysis();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 278,
      "Severity": 1,
      "Preview": "}\n\n            return patterns;\n        }\n\n        // Helper methods (simplified implementations)\n        private async Task\u003CMemoryInfo\u003E GetMemoryInfoAsync(Process process)\n        {\n            // Simplified memory analysis"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 294,
      "Severity": 1,
      "Preview": "rk check\n            try\n            {\n                // This would need actual network monitoring\n                return false;\n            }\n            catch\n            {\n                return false;\n            }\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 298,
      "Severity": 1,
      "Preview": "ual network monitoring\n                return false;\n            }\n            catch\n            {\n                return false;\n            }\n        }\n\n        private int GetConnectionCount(Process process)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 305,
      "Severity": 1,
      "Preview": "private int GetConnectionCount(Process process)\n        {\n            // Simplified connection count\n            return 0;\n        }\n\n        private List\u003Cint\u003E DetectSuspiciousPorts(Process process)\n        {\n            // Simp"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BehavioralAnalysisService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 378,
      "Severity": 0,
      "Preview": "e.WriteAllText(_behaviorDbPath, json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving behavior database: {ex.Message}\u0022);\n            }\n        }\n    }\n\n    // Supportin"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 42,
      "Severity": 1,
      "Preview": "ector = new PhishingDetector();\n            _malwareDetector = new MalwareSiteDetector();\n        }\n\n        public async Task\u003CUrlAnalysisResult\u003E AnalyzeUrlAsync(string url, string browserType, string userId)\n        {\n            var"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 86,
      "Severity": 1,
      "Preview": "Block = true; // Default to blocking on error\n            }\n\n            return result;\n        }\n\n        public async Task\u003CContentAnalysisResult\u003E AnalyzePageContentAsync(string url, string content, string browserType, string userId)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 128,
      "Severity": 1,
      "Preview": ";\n                result.ShouldBlock = true;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CDownloadAnalysisResult\u003E AnalyzeDownloadAsync(string url, string filename, long fileSize, string browserType,"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 224,
      "Severity": 1,
      "Preview": ";\n                result.IsHealthy = false;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CContentAnalysis\u003E AnalyzeContentAsync(string content, BrowserProfile profile)\n        {\n            var anal"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 251,
      "Severity": 1,
      "Preview": "analysis.Error = ex.Message;\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CScriptAnalysis\u003E AnalyzeScriptsAsync(string content)\n        {\n            var analysis = new ScriptAnalysis"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 281,
      "Severity": 1,
      "Preview": "analysis.Error = ex.Message;\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CFormAnalysis\u003E AnalyzeFormsAsync(string content)\n        {\n            var analysis = new FormAnalysis();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 331,
      "Severity": 1,
      "Preview": "00 * 1024 * 1024) return RiskLevel.High; // \u003E 500MB\n\n            return RiskLevel.Safe;\n        }\n\n        private async Task\u003CRiskLevel\u003E AnalyzeDownloadSourceAsync(string url)\n        {\n            try\n            {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 899,
      "Severity": 1,
      "Preview": "ed,\n        MalwareDetected\n    }\n\n    // Internal engines\n    public class WebFilterEngine\n    {\n        public async Task\u003CWebFilterResult\u003E CheckUrlAsync(string url, BrowserProfile profile)\n        {\n            var result = new W"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 932,
      "Severity": 1,
      "Preview": "}\n\n            return result;\n        }\n    }\n\n    public class PhishingDetector\n    {\n        public async Task\u003CPhishingAnalysisResult\u003E AnalyzeUrlAsync(string url)\n        {\n            var result = new PhishingAnalysis"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 968,
      "Severity": 1,
      "Preview": "}\n\n            return result;\n        }\n    }\n\n    public class MalwareSiteDetector\n    {\n        public async Task\u003CMalwareAnalysisResult\u003E CheckMalwareAsync(string url)\n        {\n            var result = new MalwareAnalysis"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 487,
      "Severity": 1,
      "Preview": "UrlAnalysisResult result, BrowserProfile profile)\n        {\n            if (result.OverallRisk \u003E= RiskLevel.Critical) return true;\n            if (result.OverallRisk \u003E= RiskLevel.High \u0026\u0026 profile.Settings.BlockHighRiskSites) return true;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 488,
      "Severity": 1,
      "Preview": "el.Critical) return true;\n            if (result.OverallRisk \u003E= RiskLevel.High \u0026\u0026 profile.Settings.BlockHighRiskSites) return true;\n            if (result.PhishingResult.RiskLevel \u003E= RiskLevel.Medium) return true;\n            if (result."
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 489,
      "Severity": 1,
      "Preview": "profile.Settings.BlockHighRiskSites) return true;\n            if (result.PhishingResult.RiskLevel \u003E= RiskLevel.Medium) return true;\n            if (result.MalwareResult.RiskLevel \u003E= RiskLevel.Medium) return true;\n\n            return fal"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 490,
      "Severity": 1,
      "Preview": "Result.RiskLevel \u003E= RiskLevel.Medium) return true;\n            if (result.MalwareResult.RiskLevel \u003E= RiskLevel.Medium) return true;\n\n            return false;\n        }\n\n        private bool ShouldBlockContent(ContentAnalysisResult re"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 492,
      "Severity": 1,
      "Preview": "l.Medium) return true;\n            if (result.MalwareResult.RiskLevel \u003E= RiskLevel.Medium) return true;\n\n            return false;\n        }\n\n        private bool ShouldBlockContent(ContentAnalysisResult result, BrowserProfile profile"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 497,
      "Severity": 1,
      "Preview": "entAnalysisResult result, BrowserProfile profile)\n        {\n            if (result.OverallRisk \u003E= RiskLevel.Critical) return true;\n            if (result.ScriptAnalysis.RiskLevel \u003E= RiskLevel.High) return true;\n            if (result.Co"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 498,
      "Severity": 1,
      "Preview": "ult.OverallRisk \u003E= RiskLevel.Critical) return true;\n            if (result.ScriptAnalysis.RiskLevel \u003E= RiskLevel.High) return true;\n            if (result.ContentAnalysis.InappropriateContent.Count \u003E 0 \u0026\u0026 profile.Settings.StrictContentFil"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 499,
      "Severity": 1,
      "Preview": "n true;\n            if (result.ContentAnalysis.InappropriateContent.Count \u003E 0 \u0026\u0026 profile.Settings.StrictContentFilter) return true;\n\n            return false;\n        }\n\n        private bool ShouldBlockDownload(DownloadAnalysisResult"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 501,
      "Severity": 1,
      "Preview": "ult.ContentAnalysis.InappropriateContent.Count \u003E 0 \u0026\u0026 profile.Settings.StrictContentFilter) return true;\n\n            return false;\n        }\n\n        private bool ShouldBlockDownload(DownloadAnalysisResult result, BrowserProfile profi"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 506,
      "Severity": 1,
      "Preview": "oadAnalysisResult result, BrowserProfile profile)\n        {\n            if (result.OverallRisk \u003E= RiskLevel.Critical) return true;\n            if (result.ExtensionRisk \u003E= RiskLevel.High \u0026\u0026 profile.Settings.BlockExecutableDownloads) retur"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 507,
      "Severity": 1,
      "Preview": "cal) return true;\n            if (result.ExtensionRisk \u003E= RiskLevel.High \u0026\u0026 profile.Settings.BlockExecutableDownloads) return true;\n            if (result.SourceRisk \u003E= RiskLevel.High) return true;\n\n            return false;\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 508,
      "Severity": 1,
      "Preview": "l.High \u0026\u0026 profile.Settings.BlockExecutableDownloads) return true;\n            if (result.SourceRisk \u003E= RiskLevel.High) return true;\n\n            return false;\n        }\n\n        private List\u003Cstring\u003E GenerateUrlRecommendations(UrlAnaly"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 510,
      "Severity": 1,
      "Preview": "lockExecutableDownloads) return true;\n            if (result.SourceRisk \u003E= RiskLevel.High) return true;\n\n            return false;\n        }\n\n        private List\u003Cstring\u003E GenerateUrlRecommendations(UrlAnalysisResult result)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 732,
      "Severity": 1,
      "Preview": "Folder.LocalApplicationData), \u0022PcFutureShield\u0022, \u0022BrowserExtension\u0022);\n            if (!Directory.Exists(extensionPath)) return false;\n\n            // Check if we can read/write to the extension directory\n            try\n            {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 740,
      "Severity": 1,
      "Preview": "await File.WriteAllTextAsync(testFile, \u0022test\u0022);\n                File.Delete(testFile);\n                return true;\n            }\n            catch\n            {\n                return false;\n            }\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 744,
      "Severity": 1,
      "Preview": "File.Delete(testFile);\n                return true;\n            }\n            catch\n            {\n                return false;\n            }\n        }\n    }\n\n    // Supporting classes for Browser Extension\n    public class UrlA"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\BrowserExtensionService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 670,
      "Severity": 0,
      "Preview": ".WriteAllText(_extensionDbPath, json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving browser profiles: {ex.Message}\u0022);\n            }\n        }\n\n        private Task\u003Cboo"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 844,
      "Severity": 1,
      "Preview": "ion history analysis\n\n                // Check for known scam patterns\n                if (transactionData.Contains(\u0022fake\u0022) || transactionData.Contains(\u0022test\u0022))\n                    fraudIndicators.Add(\u0022Suspicious transaction data\u0022);"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 44,
      "Severity": 1,
      "Preview": "dEngine = new FraudDetectionEngine();\n            _casinoAnalyzer = new CasinoAnalyzer();\n        }\n\n        public async Task\u003CGamingAnalysisResult\u003E AnalyzeGameAsync(string gameName, string gamePath, Process? gameProcess = null)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 87,
      "Severity": 1,
      "Preview": "kLevel.High; // Default to high risk on error\n            }\n\n            return result;\n        }\n\n        public async Task\u003CCasinoAnalysisResult\u003E AnalyzeCasinoAsync(string casinoUrl, string casinoName)\n        {\n            var res"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 164,
      "Severity": 1,
      "Preview": "ReportedBy = Environment.UserName\n            });\n            SaveCasinoProfiles();\n        }\n\n        private async Task\u003CExecutableAnalysis\u003E AnalyzeGameExecutableAsync(string gamePath)\n        {\n            var analysis = new Ex"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 202,
      "Severity": 1,
      "Preview": "tors.Add($\u0022Analysis error: {ex.Message}\u0022);\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CModificationCheck\u003E CheckGameModificationsAsync(string gamePath, GameProfile profile)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 238,
      "Severity": 1,
      "Preview": "{\n                check.Error = ex.Message;\n            }\n\n            return check;\n        }\n\n        private async Task\u003CGameNetworkAnalysis\u003E AnalyzeGameNetworkAsync(Process gameProcess)\n        {\n            var analysis = new"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 273,
      "Severity": 1,
      "Preview": "analysis.Error = ex.Message;\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CGamblingDetection\u003E DetectGamblingElementsAsync(string gamePath, string gameName)\n        {\n            var"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 346,
      "Severity": 1,
      "Preview": "analysis.IsAccessible = false;\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CLicensingCheck\u003E CheckCasinoLicensingAsync(string casinoUrl)\n        {\n            var check = new Licensing"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 401,
      "Severity": 1,
      "Preview": "{\n                check.Error = ex.Message;\n            }\n\n            return check;\n        }\n\n        private async Task\u003CFairnessAnalysis\u003E AnalyzeGameFairnessAsync(string casinoUrl, CasinoProfile profile)\n        {\n            v"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 428,
      "Severity": 1,
      "Preview": "analysis.Error = ex.Message;\n            }\n\n            return analysis;\n        }\n\n        private async Task\u003CScamDetection\u003E DetectCasinoScamsAsync(string casinoUrl, CasinoProfile profile)\n        {\n            var de"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 458,
      "Severity": 1,
      "Preview": "detection.Error = ex.Message;\n            }\n\n            return detection;\n        }\n\n        private async Task\u003CFinancialRisk\u003E AssessFinancialRiskAsync(CasinoProfile profile)\n        {\n            var risk = new Financi"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 489,
      "Severity": 1,
      "Preview": "{\n                risk.Error = ex.Message;\n            }\n\n            return risk;\n        }\n\n        private async Task\u003CList\u003Cstring\u003E\u003E CheckForMalwarePatternsAsync(string gamePath)\n        {\n            // This would integrate w"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 495,
      "Severity": 1,
      "Preview": "// This would integrate with the antivirus engine\n            return new List\u003Cstring\u003E();\n        }\n\n        private async Task\u003CList\u003Cstring\u003E\u003E CheckForKnownCheatsAsync(string gamePath, string gameName)\n        {\n            // This woul"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 828,
      "Severity": 1,
      "Preview": "string ReportedBy { get; set; } = string.Empty;\n    }\n\n    public class FraudDetectionEngine\n    {\n        public async Task\u003CFraudAlertResult\u003E AnalyzeTransactionAsync(string transactionData, decimal amount, string gameOrCasino)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 651,
      "Severity": 0,
      "Preview": "bPath)!, \u0022game_profiles.json\u0022), json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving game profiles: {ex.Message}\u0022);\n            }\n        }\n\n        private void SaveCa"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\GamingProtectionService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 664,
      "Severity": 0,
      "Preview": "ath)!, \u0022casino_profiles.json\u0022), json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving casino profiles: {ex.Message}\u0022);\n            }\n        }\n    }\n\n    // Supporting"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\HashingService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 66,
      "Severity": 1,
      "Preview": "using var fs = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);\n            if (fs.Length \u003C 2) return false;\n            fs.Read(hdr);\n            return hdr[0] == (byte)\u0027M\u0027 \u0026\u0026 hdr[1] == (byte)\u0027Z\u0027;\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 41,
      "Severity": 1,
      "Preview": "serProfiles = LoadUserProfiles();\n            _behaviorAnalyzer = new BehaviorAnalyzer();\n        }\n\n        public async Task\u003CParentalAnalysisResult\u003E AnalyzeContentAsync(string content, string url, UserProfile userProfile)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 112,
      "Severity": 1,
      "Preview": "erProfiles.TryGetValue(userId, out var profile) ? profile : CreateDefaultProfile(userId);\n        }\n\n        private async Task\u003CRiskLevel\u003E AnalyzePredatorContentAsync(string content, string url)\n        {\n            var riskScore = 0."
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 141,
      "Severity": 1,
      "Preview": ")\n                riskScore \u002B= 0.2;\n\n            return DetermineRiskLevel(riskScore);\n        }\n\n        private async Task\u003CRiskLevel\u003E AnalyzeInappropriateContentAsync(string content)\n        {\n            var riskScore = 0.0;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 165,
      "Severity": 1,
      "Preview": "\u002B\n                riskScore \u002B= 0.2;\n\n            return DetermineRiskLevel(riskScore);\n        }\n\n        private async Task\u003CRiskLevel\u003E AnalyzeViolenceContentAsync(string content)\n        {\n            var riskScore = 0.0;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 184,
      "Severity": 1,
      "Preview": "riskScore \u002B= 0.3;\n            }\n\n            return DetermineRiskLevel(riskScore);\n        }\n\n        private async Task\u003CRiskLevel\u003E AnalyzeBullyingContentAsync(string content)\n        {\n            var riskScore = 0.0;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 206,
      "Severity": 1,
      "Preview": "riskScore \u002B= 0.4;\n            }\n\n            return DetermineRiskLevel(riskScore);\n        }\n\n        private async Task\u003CRiskLevel\u003E AnalyzeSuicideIndicatorsAsync(string content)\n        {\n            var riskScore = 0.0;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 430,
      "Severity": 1,
      "Preview": "r\n    {\n        private readonly Dictionary\u003Cstring, UserBehaviorPattern\u003E _behaviorPatterns = new();\n\n        public async Task UpdateBehaviorPatternsAsync(UserProfile profile, ParentalAnalysisResult result)\n        {\n            if (!"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 446,
      "Severity": 1,
      "Preview": "\u002B;\n            if (result.PredatorRisk \u003E= RiskLevel.Medium) pattern.PredatorEncounters\u002B\u002B;\n        }\n\n        public async Task\u003CBehaviorAnalysisResult\u003E AnalyzeBehaviorAsync(UserProfile profile, List\u003Cstring\u003E recentActivities)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 255,
      "Severity": 1,
      "Preview": "file profile)\n        {\n            // Always block critical content\n            if (result.OverallRiskScore \u003E= 0.8) return true;\n\n            // Check user profile restrictions\n            if (profile.Age \u003C 13 \u0026\u0026 result.OverallRiskSc"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 258,
      "Severity": 1,
      "Preview": "// Check user profile restrictions\n            if (profile.Age \u003C 13 \u0026\u0026 result.OverallRiskScore \u003E= 0.3) return true;\n            if (profile.Age \u003C 16 \u0026\u0026 result.OverallRiskScore \u003E= 0.5) return true;\n\n            // Block b"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 259,
      "Severity": 1,
      "Preview": "13 \u0026\u0026 result.OverallRiskScore \u003E= 0.3) return true;\n            if (profile.Age \u003C 16 \u0026\u0026 result.OverallRiskScore \u003E= 0.5) return true;\n\n            // Block based on specific risk types\n            if (result.SuicideRisk \u003E= RiskLevel.High)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 262,
      "Severity": 1,
      "Preview": "eturn true;\n\n            // Block based on specific risk types\n            if (result.SuicideRisk \u003E= RiskLevel.High) return true;\n            if (result.PredatorRisk \u003E= RiskLevel.Medium) return true;\n\n            return false;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 263,
      "Severity": 1,
      "Preview": "if (result.SuicideRisk \u003E= RiskLevel.High) return true;\n            if (result.PredatorRisk \u003E= RiskLevel.Medium) return true;\n\n            return false;\n        }\n\n        private List\u003Cstring\u003E GenerateParentalAlerts(ParentalAnal"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 265,
      "Severity": 1,
      "Preview": "k \u003E= RiskLevel.High) return true;\n            if (result.PredatorRisk \u003E= RiskLevel.Medium) return true;\n\n            return false;\n        }\n\n        private List\u003Cstring\u003E GenerateParentalAlerts(ParentalAnalysisResult result)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 350,
      "Severity": 0,
      "Preview": "e.WriteAllText(_parentalDbPath, json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving user profiles: {ex.Message}\u0022);\n            }\n        }\n\n        private void SaveCo"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ParentalControlService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 363,
      "Severity": 0,
      "Preview": "ath)!, \u0022content_filters.json\u0022), json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving content filters: {ex.Message}\u0022);\n            }\n        }\n    }\n\n    // Supporting"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 76,
      "Severity": 1,
      "Preview": "report.OverallHealthScore = 0;\n            }\n\n            return report;\n        }\n\n        public async Task\u003COptimizationResult\u003E PerformDeepOptimizationAsync(OptimizationOptions options)\n        {\n            var res"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 137,
      "Severity": 1,
      "Preview": "result.EndTime = DateTime.UtcNow;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CRepairResult\u003E PerformSystemRepairAsync(RepairOptions options)\n        {\n            var result = new Repair"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 192,
      "Severity": 1,
      "Preview": "result.EndTime = DateTime.UtcNow;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CPerformanceBoostResult\u003E BoostPerformanceAsync()\n        {\n            var result = new PerformanceBoostResu"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 237,
      "Severity": 1,
      "Preview": "result.EndTime = DateTime.UtcNow;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CUninstallResult\u003E PerformSmartUninstallAsync(List\u003Cstring\u003E programsToRemove)\n        {\n            var result"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 607,
      "Severity": 1,
      "Preview": "ew();\n    }\n\n    // System analysis and optimization engines\n    public class SystemAnalyzer\n    {\n        public async Task\u003CSystemInformation\u003E GetSystemInformationAsync()\n        {\n            var info = new SystemInformation();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 682,
      "Severity": 1,
      "Preview": ");\n                health.HealthScore = 0.5;\n            }\n\n            return health;\n        }\n\n        public async Task\u003CMemoryHealth\u003E AnalyzeMemoryHealthAsync()\n        {\n            var health = new MemoryHealth();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 707,
      "Severity": 1,
      "Preview": ");\n                health.HealthScore = 0.5;\n            }\n\n            return health;\n        }\n\n        public async Task\u003CDiskHealth\u003E AnalyzeDiskHealthAsync()\n        {\n            var health = new DiskHealth();\n\n            tr"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1048,
      "Severity": 1,
      "Preview": "result.Success = false;\n            }\n\n            return Task.FromResult(result);\n        }\n\n        public async Task\u003CDefragResult\u003E DefragmentDrivesAsync()\n        {\n            var result = new DefragResult();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1135,
      "Severity": 1,
      "Preview": "result.Success = false;\n            }\n\n            return Task.FromResult(result);\n        }\n\n        public async Task\u003CRegistryResult\u003E CleanRegistryAsync()\n        {\n            var result = new RegistryResult();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1294,
      "Severity": 1,
      "Preview": "ge);\n                result.Success = false;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CNetworkResult\u003E OptimizeNetworkAsync()\n        {\n            var result = new NetworkResult();"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1316,
      "Severity": 1,
      "Preview": "ge);\n                result.Success = false;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CUninstallOperation\u003E UninstallProgramAsync(string programName)\n        {\n            var result = new Uninst"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1512,
      "Severity": 1,
      "Preview": "rivate static extern int EmptyWorkingSet(IntPtr hwProc);\n    }\n\n    public class RepairEngine\n    {\n        public async Task\u003CRepairOperation\u003E RepairSystemFilesAsync()\n        {\n            var result = new RepairOperation { Operatio"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1545,
      "Severity": 1,
      "Preview": "ge);\n                result.Success = false;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CRepairOperation\u003E FixWindowsCorruptionAsync()\n        {\n            var result = new RepairOperation { Opera"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1578,
      "Severity": 1,
      "Preview": "ge);\n                result.Success = false;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CRepairOperation\u003E RepairBootIssuesAsync()\n        {\n            var result = new RepairOperation { Operation"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1615,
      "Severity": 1,
      "Preview": "ge);\n                result.Success = false;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CRepairOperation\u003E RepairDriversAsync()\n        {\n            var result = new RepairOperation { Operation ="
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1642,
      "Severity": 1,
      "Preview": "ge);\n                result.Success = false;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CRepairOperation\u003E RepairNetworkAsync()\n        {\n            var result = new RepairOperation { Operation ="
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 9,
      "Severity": 1,
      "Preview": "neric;\nusing System.Linq;\nusing System.IO;\nusing System.Diagnostics;\nusing System.Management;\nusing System.Runtime.InteropServices;\nusing System.Threading.Tasks;\nusing Microsoft.Win32;\nusing System.Threading;\n\nnamespace PcFutureSh"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1440,
      "Severity": 1,
      "Preview": "too new (might be in use)\n                if (fileInfo.LastWriteTime \u003E DateTime.Now.AddHours(-1))\n                    return false;\n\n                // Don\u0027t delete system files\n                var systemFiles = new[] { \u0022desktop.ini\u0022,"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1445,
      "Severity": 1,
      "Preview": ".ini\u0022, \u0022thumbs.db\u0022 };\n                if (systemFiles.Contains(fileInfo.Name.ToLowerInvariant()))\n                    return false;\n\n                // Only delete certain file types\n                var safeExtensions = new[] { \u0022.tmp\u0022,"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1454,
      "Severity": 1,
      "Preview": "ns.Contains(fileInfo.Extension.ToLowerInvariant());\n\n            }\n            catch\n            {\n                return false;\n            }\n        }\n\n        private IEnumerable\u003Cstring\u003E GetInstalledPrograms()\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 626,
      "Severity": 0,
      "Preview": "on ex)\n            {\n                // Log to console as fallback; callers should handle exceptions\n                Console.WriteLine($\u0022Error getting system information: {ex.Message}\u0022);\n            }\n\n            return info;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\PcOptimizationService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 772,
      "Severity": 0,
      "Preview": "metrics.Timestamp = DateTime.UtcNow;\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error getting performance metrics: {ex.Message}\u0022);\n            }\n\n            return metrics;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 38,
      "Severity": 1,
      "Preview": "tIndicators = LoadThreatIndicators();\n            _threatFeeds = InitializeThreatFeeds();\n        }\n\n        public async Task\u003CThreatIntelligenceResult\u003E CheckHashAsync(string hash)\n        {\n            var result = new ThreatIntellig"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 106,
      "Severity": 1,
      "Preview": "{\n                result.Error = ex.Message;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CThreatIntelligenceResult\u003E CheckDomainAsync(string domain)\n        {\n            var result = new ThreatInte"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 145,
      "Severity": 1,
      "Preview": "{\n                result.Error = ex.Message;\n            }\n\n            return result;\n        }\n\n        public async Task\u003CThreatIntelligenceResult\u003E CheckIPAddressAsync(string ipAddress)\n        {\n            var result = new Thre"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 268,
      "Severity": 1,
      "Preview": "alse,\n                    ApiKey = null // Public API\n                }\n            };\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckFeedAsync(ThreatFeed feed, string hash)\n        {\n            var result = n"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 291,
      "Severity": 1,
      "Preview": "licious = false;\n                    break;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckDomainFeedAsync(ThreatFeed feed, string domain)\n        {\n            var re"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 311,
      "Severity": 1,
      "Preview": "licious = false;\n                    break;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckIPFeedAsync(ThreatFeed feed, string ipAddress)\n        {\n            var res"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 332,
      "Severity": 1,
      "Preview": "}\n\n            return result;\n        }\n\n        // Simplified feed checking implementations\n        private async Task\u003CThreatIntelligenceResult\u003E CheckMalwareBazaarAsync(string hash)\n        {\n            // Simplified implemen"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 343,
      "Severity": 1,
      "Preview": "alse, // Would check actual API\n                Source = \u0022MalwareBazaar\u0022\n            };\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckVirusTotalAsync(string hash)\n        {\n            // Simplified implementat"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 354,
      "Severity": 1,
      "Preview": "= false, // Would check actual API\n                Source = \u0022VirusTotal\u0022\n            };\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckVirusTotalDomainAsync(string domain)\n        {\n            return new Threat"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 364,
      "Severity": 1,
      "Preview": "IsMalicious = false,\n                Source = \u0022VirusTotal\u0022\n            };\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckPhishTankAsync(string domain)\n        {\n            return new ThreatIntelli"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 374,
      "Severity": 1,
      "Preview": "IsMalicious = false,\n                Source = \u0022PhishTank\u0022\n            };\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckAbuseIPDBAsync(string ipAddress)\n        {\n            return new ThreatInte"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 384,
      "Severity": 1,
      "Preview": "IsMalicious = false,\n                Source = \u0022AbuseIPDB\u0022\n            };\n        }\n\n        private async Task\u003CThreatIntelligenceResult\u003E CheckVirusTotalIPAsync(string ipAddress)\n        {\n            return new ThreatI"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 394,
      "Severity": 1,
      "Preview": "IsMalicious = false,\n                Source = \u0022VirusTotal\u0022\n            };\n        }\n\n        private async Task\u003CList\u003CThreatIndicator\u003E\u003E DownloadFeedAsync(ThreatFeed feed)\n        {\n            // Simplified implementatio"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 17,
      "Severity": 1,
      "Preview": "y\u003E\n    /// Advanced threat intelligence service with multiple threat feeds\n    /// \u003C/summary\u003E\n    public class ThreatIntelligenceService\n    {\n        private readonly HttpClient _httpClient;\n        private readonly string _threatDbP"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 24,
      "Severity": 1,
      "Preview": "g, ThreatIndicator\u003E _threatIndicators;\n        private readonly List\u003CThreatFeed\u003E _threatFeeds;\n\n        public ThreatIntelligenceService()\n        {\n            _httpClient = new HttpClient();\n            _httpClient.Timeout = TimeSpa"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 90,
      "Severity": 0,
      "Preview": "}\n                        catch (Exception ex)\n                        {\n                            Console.WriteLine($\u0022Error checking feed {feed.Name}: {ex.Message}\u0022);\n                        }\n                    }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 130,
      "Severity": 0,
      "Preview": "}\n                    }\n                    catch (Exception ex)\n                    {\n                        Console.WriteLine($\u0022Error checking domain feed {feed.Name}: {ex.Message}\u0022);\n                    }\n                }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 169,
      "Severity": 0,
      "Preview": "}\n                    }\n                    catch (Exception ex)\n                    {\n                        Console.WriteLine($\u0022Error checking IP feed {feed.Name}: {ex.Message}\u0022);\n                    }\n                }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 186,
      "Severity": 0,
      "Preview": "return result;\n        }\n\n        public async Task UpdateThreatFeedsAsync()\n        {\n            Console.WriteLine(\u0022Updating threat intelligence feeds...\u0022);\n\n            foreach (var feed in _threatFeeds)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 200,
      "Severity": 0,
      "Preview": "}\n                }\n                catch (Exception ex)\n                {\n                    Console.WriteLine($\u0022Error updating feed {feed.Name}: {ex.Message}\u0022);\n                }\n            }\n\n            Sa"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 205,
      "Severity": 0,
      "Preview": "feed {feed.Name}: {ex.Message}\u0022);\n                }\n            }\n\n            SaveThreatIndicators();\n            Console.WriteLine($\u0022Updated {_threatIndicators.Count} threat indicators\u0022);\n        }\n\n        public ThreatStatistics"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ThreatIntelligenceService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 429,
      "Severity": 0,
      "Preview": "ile.WriteAllText(_threatDbPath, json);\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error saving threat indicators: {ex.Message}\u0022);\n            }\n        }\n\n        private DateTim"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 40,
      "Severity": 1,
      "Preview": "yPath, \u0022zero_day_patterns.json\u0022);\n\n            _zeroDayPatterns = LoadZeroDayPatterns();\n        }\n\n        public async Task\u003CZeroDayAnalysisResult\u003E AnalyzeFileAsync(string filePath)\n        {\n            var result = new ZeroDayAnal"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 74,
      "Severity": 1,
      "Preview": "}\n\n            result.EndTime = DateTime.UtcNow;\n            return result;\n        }\n\n        public async Task\u003CZeroDayAnalysisResult\u003E AnalyzeProcessAsync(Process process)\n        {\n            var result = new ZeroDayA"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 125,
      "Severity": 1,
      "Preview": "}\n\n            result.EndTime = DateTime.UtcNow;\n            return result;\n        }\n\n        private async Task\u003CAnomalyDetectionResult\u003E DetectAnomaliesAsync(string filePath)\n        {\n            var result = new Anomal"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 167,
      "Severity": 1,
      "Preview": "d($\u0022Anomaly detection error: {ex.Message}\u0022);\n            }\n\n            return result;\n        }\n\n        private async Task\u003CCodeAnalysisResult\u003E AnalyzeCodePatternsAsync(string filePath)\n        {\n            var result = new CodeAn"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 249,
      "Severity": 1,
      "Preview": "result.Error = ex.Message;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CSignatureEvasionResult\u003E DetectSignatureEvasionAsync(string filePath)\n        {\n            var result = new"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 318,
      "Severity": 1,
      "Preview": "result.Error = ex.Message;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CMemoryAnalysisResult\u003E AnalyzeProcessMemoryAsync(Process process)\n        {\n            var result = new Mem"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 342,
      "Severity": 1,
      "Preview": "result.Error = ex.Message;\n            }\n\n            return result;\n        }\n\n        private async Task\u003CNetworkAnalysisResult\u003E AnalyzeProcessNetworkAsync(Process process)\n        {\n            var result = new N"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 476,
      "Severity": 1,
      "Preview": "var entropy = CalculateByteEntropy(content);\n            return entropy \u003E 7.5;\n        }\n\n        private async Task\u003Cbool\u003E IsFileSignedAsync(string filePath)\n        {\n            try\n            {\n                var cert"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 21,
      "Severity": 1,
      "Preview": "Service\n    {\n        private readonly BehavioralAnalysisService _behavioralAnalysis;\n        private readonly ThreatIntelligenceService _threatIntelligence;\n        private readonly string _zeroDayDbPath;\n        private readonly Dict"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 27,
      "Severity": 1,
      "Preview": "public ZeroDayDetectionService(\n            BehavioralAnalysisService behavioralAnalysis,\n            ThreatIntelligenceService threatIntelligence)\n        {\n            _behavioralAnalysis = behavioralAnalysis;\n            _"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 430,
      "Severity": 1,
      "Preview": "{\n            // Simple junk code detection - look for repetitive patterns\n            if (content.Length \u003C 100) return false;\n\n            int consecutiveSame = 0;\n            for (int i = 1; i \u003C content.Length; i\u002B\u002B)"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 439,
      "Severity": 1,
      "Preview": "eSame\u002B\u002B;\n                    if (consecutiveSame \u003E 50) // More than 50 consecutive same bytes\n                        return true;\n                }\n                else\n                {\n                    consecutiveSame = 0;"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 447,
      "Severity": 1,
      "Preview": "else\n                {\n                    consecutiveSame = 0;\n                }\n            }\n\n            return false;\n        }\n\n        private bool DetectCodeObfuscation(byte[] content)\n        {\n            // Check"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 481,
      "Severity": 1,
      "Preview": "var cert = new System.Security.Cryptography.X509Certificates.X509Certificate2(filePath);\n                return true;\n            }\n            catch\n            {\n                return false;\n            }\n        }"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 485,
      "Severity": 1,
      "Preview": "Certificate2(filePath);\n                return true;\n            }\n            catch\n            {\n                return false;\n            }\n        }\n\n        private double CalculateByteEntropy(byte[] data)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Common\\Services\\ZeroDayDetectionService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 491,
      "Severity": 1,
      "Preview": "}\n        }\n\n        private double CalculateByteEntropy(byte[] data)\n        {\n            if (data.Length == 0) return 0;\n\n            var frequencies = new int[256];\n            foreach (var b in data) frequencies[b]\u002B\u002B;"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\IScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 10,
      "Severity": 1,
      "Preview": "// Service for scanning files, directories, and processes for potential threats\n    /// \u003C/summary\u003E\n    public interface IScannerService\n    {\n        /// \u003Csummary\u003E\n        /// Scans a single file for threats\n        /// \u003C/summary\u003E\n        T"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R005",
      "Message": "Async method returns Task.CompletedTask",
      "Line": 1619,
      "Severity": 1,
      "Preview": "ignore\n            }\n            Dispose();\n            _logger.LogInformation(\u0022Scanner Service stopped\u0022);\n            return Task.CompletedTask;\n        }\n\n        #endregion\n\n        #region IDisposable Implementation\n\n        public voi"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 1361,
      "Severity": 1,
      "Preview": "shal.AllocHGlobal((int)bufferSize);\n                    }\n\n                    if (status != 0)\n                        return null;\n\n                    // Parse the SYSTEM_HANDLE_INFORMATION_EX structure\n                    var handleCoun"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 1389,
      "Severity": 1,
      "Preview": "tion ex)\n            {\n                _logger.LogError(ex, \u0022Error getting system handle information\u0022);\n                return null;\n            }\n\n            return handles;\n        }\n\n        private string GetHandleTypeName(ushort objec"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 39,
      "Severity": 1,
      "Preview": "rviceProvider));\n        }\n\n        // IHostedService implementation is provided at the end of the file\n\n        public async Task\u003CScanResult\u003E ScanFileAsync(string filePath, CancellationToken ct = default)\n        {\n            var result ="
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 208,
      "Severity": 1,
      "Preview": "oseAsync();\n                }\n                result.EndTime = DateTime.UtcNow;\n            }\n        }\n\n        public async Task\u003CScanResult\u003E ScanDirectoryAsync(string directoryPath, bool recursive = true, CancellationToken ct = default)"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 295,
      "Severity": 1,
      "Preview": "or scanning directory: {DirectoryPath}\u0022, directoryPath);\n                throw;\n            }\n        }\n\n        public async Task\u003CScanResult\u003E ScanProcessAsync(int processId, CancellationToken ct = default)\n        {\n            var result"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 467,
      "Severity": 1,
      "Preview": "ask.FromResult(((bool, string?))(false, $\u0022Verification error: {ex.Message}\u0022));\n            }\n        }\n\n        private async Task\u003CList\u003CThreatDetection\u003E\u003E ScanProcessMemoryAsync(Process process, CancellationToken ct)\n        {\n            va"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 602,
      "Severity": 1,
      "Preview": "alAs(UnmanagedType.Bool)]\n            public static extern bool CloseHandle(IntPtr hObject);\n        }\n\n        private async Task\u003CList\u003CMemoryRegionInfo\u003E\u003E FindSuspiciousMemoryRegionsAsync(Process process, CancellationToken ct)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 738,
      "Severity": 1,
      "Preview": "return true;\n                }\n            }\n\n            return false;\n        }\n\n        private async Task\u003CList\u003CThreatDetection\u003E\u003E ScanForMemoryPatternsAsync(Process process, CancellationToken ct)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1002,
      "Severity": 1,
      "Preview": "verity Severity { get; set; }\n            public string Reason { get; set; } = string.Empty;\n        }\n\n        private async Task\u003CList\u003CThreatDetection\u003E\u003E ScanProcessHandlesAsync(Process process, CancellationToken ct)\n        {\n            v"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1063,
      "Severity": 1,
      "Preview": "ing handles of process {ProcessId}\u0022, process.Id);\n            }\n\n            return threats;\n        }\n\n        private async Task\u003CHeuristicAnalysisResult\u003E PerformHeuristicAnalysisAsync(string filePath, CancellationToken ct)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 1213,
      "Severity": 1,
      "Preview": "ic IntPtr HandleValue { get; set; }\n            public ThreatSeverity Severity { get; set; }\n        }\n\n        private async Task\u003CList\u003CHandleInfo\u003E\u003E EnumerateProcessHandlesAsync(int processId, CancellationToken ct)\n        {\n            var"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 8,
      "Severity": 1,
      "Preview": "t;\nusing System.Collections.Generic;\nusing System.Diagnostics;\nusing System.IO;\nusing System.Linq;\nusing System.Runtime.InteropServices;\nusing System.Security.Cryptography;\nusing System.Threading;\nusing System.Threading.Tasks;\nusing Microso"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 17,
      "Severity": 1,
      "Preview": "Microsoft.Extensions.DependencyInjection;\n\nnamespace PcFutureShield.Engine.Scanning\n{\n    public class ScannerService : IScannerService, IDisposable, IHostedService\n    {\n        private const int MaxConcurrentScans = 8;\n        private con"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 17,
      "Severity": 1,
      "Preview": "yInjection;\n\nnamespace PcFutureShield.Engine.Scanning\n{\n    public class ScannerService : IScannerService, IDisposable, IHostedService\n    {\n        private const int MaxConcurrentScans = 8;\n        private const int MaxFileSizeBytes = 200"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 37,
      "Severity": 1,
      "Preview": "_serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));\n        }\n\n        // IHostedService implementation is provided at the end of the file\n\n        public async Task\u003CScanResult\u003E ScanFileAsync(st"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 1597,
      "Severity": 1,
      "Preview": "itHandle,\n            uint dwOptions);\n\n        private const uint DUPLICATE_SAME_ACCESS = 0x00000002;\n\n        #region IHostedService Implementation\n\n        public async Task StartAsync(CancellationToken cancellationToken)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R032",
      "Message": "Async method defined but never awaited or invoked",
      "Line": 1599,
      "Severity": 1,
      "Preview": "private const uint DUPLICATE_SAME_ACCESS = 0x00000002;\n\n        #region IHostedService Implementation\n\n        public async Task StartAsync(CancellationToken cancellationToken)\n        {\n            _logger.LogInformation(\u0022Scanner Service"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 686,
      "Severity": 1,
      "Preview": "ContainsShellcodePatterns(byte[] buffer)\n        {\n            if (buffer == null || buffer.Length \u003C 4)\n                return false;\n\n            // Common shellcode patterns (simplified for example)\n            var patterns = new (byte[]"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 704,
      "Severity": 1,
      "Preview": "if (\u002B\u002BconsecutiveNops \u003E 10) // More than 10 consecutive NOPs is suspicious\n                        return true;\n                }\n                else\n                {\n                    consecutiveNops = 0;"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 731,
      "Severity": 1,
      "Preview": "break;\n                        }\n                    }\n                    if (match)\n                        return true;\n                }\n            }\n\n            return false;\n        }\n\n        private async Task\u003CList\u003CThrea"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 735,
      "Severity": 1,
      "Preview": "}\n                    if (match)\n                        return true;\n                }\n            }\n\n            return false;\n        }\n\n        private async Task\u003CList\u003CThreatDetection\u003E\u003E ScanForMemoryPatternsAsync(Process process, C"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1190,
      "Severity": 1,
      "Preview": "uffer[i]]\u002B\u002B;\n                }\n                totalBytes \u002B= bytesRead;\n            }\n\n            if (totalBytes == 0) return 0;\n\n            for (var i = 0; i \u003C 256; i\u002B\u002B)\n            {\n                if (frequencies[i] \u003E 0)"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1270,
      "Severity": 1,
      "Preview": "sensitiveProcesses.Any(p =\u003E handle.Name.Contains(p, StringComparison.OrdinalIgnoreCase)))\n            {\n                return true;\n            }\n\n            // Check for handles to sensitive files/registry\n            var sensitivePaths"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1277,
      "Severity": 1,
      "Preview": "if (sensitivePaths.Any(p =\u003E handle.Name.Contains(p, StringComparison.OrdinalIgnoreCase)))\n            {\n                return true;\n            }\n\n            // Check for high-privilege access\n            if (handle.Access.Contains(\u0022ALL_A"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1284,
      "Severity": 1,
      "Preview": "||\n                handle.Access.Contains(\u0022DELETE\u0022) || handle.Access.Contains(\u0022MODIFY\u0022))\n            {\n                return true;\n            }\n\n            return false;\n        }\n\n        private ThreatSeverity DetermineHandleSeverity("
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\ScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 1287,
      "Severity": 1,
      "Preview": "ns(\u0022DELETE\u0022) || handle.Access.Contains(\u0022MODIFY\u0022))\n            {\n                return true;\n            }\n\n            return false;\n        }\n\n        private ThreatSeverity DetermineHandleSeverity(string type, string access, string name)"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 108,
      "Severity": 1,
      "Preview": "Severity = (ThreatSeverity)reader.GetInt32(2)\n                    };\n                }\n\n                return null;\n            }\n            catch (Exception ex)\n            {\n                _logger.LogError(ex, \u0022Error lo"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 113,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                _logger.LogError(ex, \u0022Error looking up signature\u0022);\n                return null;\n            }\n        }\n\n        private async Task CreateTablesIfNotExistAsync(CancellationToken ct)"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 36,
      "Severity": 1,
      "Preview": "directory))\n            {\n                Directory.CreateDirectory(directory);\n            }\n        }\n\n        public async Task\u003Cbool\u003E InitializeAsync(CancellationToken ct = default)\n        {\n            try\n            {"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R032",
      "Message": "Async method defined but never awaited or invoked",
      "Line": 117,
      "Severity": 1,
      "Preview": "ogger.LogError(ex, \u0022Error looking up signature\u0022);\n                return null;\n            }\n        }\n\n        private async Task CreateTablesIfNotExistAsync(CancellationToken ct)\n        {\n            if (_connection == null) return;"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 44,
      "Severity": 1,
      "Preview": "await _connection.OpenAsync(ct);\n\n                await CreateTablesIfNotExistAsync(ct);\n                return true;\n            }\n            catch (Exception ex)\n            {\n                _logger.LogError(ex, \u0022Failed t"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 49,
      "Severity": 1,
      "Preview": "tion ex)\n            {\n                _logger.LogError(ex, \u0022Failed to initialize signature database\u0022);\n                return false;\n            }\n        }\n\n        public async Task\u003Cbool\u003E UpdateAsync(CancellationToken ct = default)"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 74,
      "Severity": 1,
      "Preview": "_logger.LogInformation(\u0022Updated {Count} signatures\u0022, signatures.Count);\n                }\n\n                return true;\n            }\n            catch (Exception ex)\n            {\n                _logger.LogError(ex, \u0022Failed t"
    },
    {
      "File": ".\\PcFutureShield.Engine\\Scanning\\SqliteSignatureDatabase.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 79,
      "Severity": 1,
      "Preview": "xception ex)\n            {\n                _logger.LogError(ex, \u0022Failed to update signature database\u0022);\n                return false;\n            }\n        }\n\n        public async Task\u003CThreatSignature?\u003E LookupSignatureAsync(string signature"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\SignatureDatabase.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 88,
      "Severity": 1,
      "Preview": "tring hash, CancellationToken cancellationToken = default)\n        {\n            if (string.IsNullOrWhiteSpace(hash)) return null;\n            hash = hash.ToLowerInvariant();\n            await using var conn = new SqliteConnection($\u0022Dat"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 284,
      "Severity": 1,
      "Preview": "etectedAt = DateTime.UtcNow,\n                    IsQuarantined = false\n                };\n            }\n            return null;\n        }\n\n        private async Task\u003Cstring?\u003E GetThreatNameFromSignatureDbAsync(string hash, Cancellati"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 297,
      "Severity": 1,
      "Preview": "and log\n                _logger.LogWarning(\u0022Signature DB not configured; skipping signature lookup\u0022);\n                return null;\n            }\n            catch (Exception ex)\n            {\n                _logger.LogError(ex, \u0022Erro"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 302,
      "Severity": 1,
      "Preview": "ex)\n            {\n                _logger.LogError(ex, \u0022Error querying threat signature database\u0022);\n                return null;\n            }\n        }\n        #region IDisposable Implementation\n\n        public void Dispose()"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 110,
      "Severity": 1,
      "Preview": "atch.Stop();\n            result.Duration = stopwatch.Elapsed;\n            return result;\n        }\n\n        public async Task\u003CIReadOnlyList\u003CQuarantineItem\u003E\u003E GetQuarantineAsync(CancellationToken cancellationToken = default)\n        {"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 139,
      "Severity": 1,
      "Preview": "IsQuarantined = true\n                });\n            }\n            return items;\n        }\n\n        public async Task\u003Cbool\u003E RemoveFromQuarantineAsync(string quarantineId, CancellationToken cancellationToken = default)"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 179,
      "Severity": 1,
      "Preview": "n if file deletion fails\n                }\n            }\n\n            return rows \u003E 0;\n        }\n\n        public async Task\u003Cbool\u003E RestoreFromQuarantineAsync(string quarantineId, string? targetPath = null, CancellationToken cancellati"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 13,
      "Severity": 1,
      "Preview": "sing System.Threading.Tasks;\n\nnamespace PcFutureShield.Engine.VirusScanner\n{\n    public class VirusScannerService : IVirusScannerService, IDisposable\n    {\n        private readonly ILogger\u003CVirusScannerService\u003E _logger;\n        privat"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 143,
      "Severity": 1,
      "Preview": "Domain.BaseDirectory, \u0022Quarantine\u0022, \u0022quarantine.db\u0022);\n            if (!File.Exists(quarantineDbPath))\n                return false;\n\n            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($\u0022Data Source={quarantineDbPath"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 183,
      "Severity": 1,
      "Preview": "Domain.BaseDirectory, \u0022Quarantine\u0022, \u0022quarantine.db\u0022);\n            if (!File.Exists(quarantineDbPath))\n                return false;\n\n            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($\u0022Data Source={quarantineDbPath"
    },
    {
      "File": ".\\PcFutureShield.Engine\\VirusScanner\\VirusScannerService.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 195,
      "Severity": 1,
      "Preview": "md.ExecuteReaderAsync(cancellationToken);\n            if (!await reader.ReadAsync(cancellationToken))\n                return false;\n\n            var originalPath = reader.GetString(0);\n            var quarantinePath = reader.GetString("
    },
    {
      "File": ".\\PcFutureShield.ExtensionHost\\Hubs\\DetectionHub.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 18,
      "Severity": 1,
      "Preview": "/// Server will re-broadcast to all connected clients as ReceiveDetection.\n        /// \u003C/summary\u003E\n        public async Task PublishDetection(DetectionEvent detection)\n        {\n            _logger.LogInformation(\u0022PublishDetection f"
    },
    {
      "File": ".\\PcFutureShield.RealtimeScanner\\FileMonitor\\FileWatcher.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 51,
      "Severity": 1,
      "Preview": "ueue.Add(path);\n            }\n            catch { /* swallow noisy race conditions */ }\n        }\n\n        private async Task ProcessLoopAsync()\n        {\n            foreach (var path in _queue.GetConsumingEnumerable(_cts.Token))"
    },
    {
      "File": ".\\PcFutureShield.UI\\Converters\\BooleanToVisibilityConverter.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 20,
      "Severity": 1,
      "Preview": "lture)\n        {\n            if (value is Visibility v)\n                return v == Visibility.Visible;\n            return false;\n        }\n    }\n}"
    },
    {
      "File": ".\\PcFutureShield.UI\\Scan\\ScanManager.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 42,
      "Severity": 1,
      "Preview": "try\n                {\n                    var notifier = Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                    notifier.ShowError(\u0022PcFutureShield\u0022, $\u0022ScanManager initialization failed:"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\EventLogService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 8,
      "Severity": 1,
      "Preview": "IO;\nusing PcFutureShield.UI.ViewModels;\n\nnamespace PcFutureShield.UI.Services\n{\n    public class EventLogService : IEventLogService\n    {\n        private readonly List\u003CRealtimeEvent\u003E _events = new();\n        public IEnumerable\u003CRealt"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\IEventLogService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 6,
      "Severity": 1,
      "Preview": "lections.Generic;\nusing PcFutureShield.UI.ViewModels;\n\nnamespace PcFutureShield.UI.Services\n{\n    public interface IEventLogService\n    {\n        IEnumerable\u003CRealtimeEvent\u003E GetRecentEvents();\n        void ShowEventLogWindow();"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\INotificationService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 5,
      "Severity": 1,
      "Preview": "using System;\n\nnamespace PcFutureShield.UI.Services\n{\n    public interface INotificationService\n    {\n        void ShowInfo(string title, string message);\n        void ShowWarning(string title, string message);\n        void ShowErro"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\IRealtimeProtectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 5,
      "Severity": 1,
      "Preview": "using System;\n\nnamespace PcFutureShield.UI.Services\n{\n    public interface IRealtimeProtectionService\n    {\n        event EventHandler\u003CUI.ViewModels.RealtimeEvent\u003E ProtectionEvent;\n        bool IsEnabled { get; }\n        void SetEna"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\LoggingService.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 76,
      "Severity": 0,
      "Preview": "coding.UTF8);\n                }\n\n                // Also output to console in debug mode\n#if DEBUG\n                Console.WriteLine(logEntry);\n#endif\n            }\n            catch\n            {\n                // If logging fai"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\RealtimeProtectionService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 6,
      "Severity": 1,
      "Preview": "PcFutureShield.UI.ViewModels;\n\nnamespace PcFutureShield.UI.Services\n{\n    public class RealtimeProtectionService : IRealtimeProtectionService\n    {\n        public event EventHandler\u003CRealtimeEvent\u003E? ProtectionEvent;\n        public bo"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\ServiceLocator.cs",
      "RuleId": "R007",
      "Message": "Method returns default or null",
      "Line": 62,
      "Severity": 1,
      "Preview": "typeof(T), out var service))\n                    return (T)service;\n            }\n            catch { }\n            return null;\n        }\n    }\n}"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\UpdateService.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 33,
      "Severity": 1,
      "Preview": "ic record UpdateFeedEntry(string Version, string Url, string Sha256, bool IsCritical, string? Notes);\n\n        public async Task\u003CIReadOnlyList\u003CUpdateFeedEntry\u003E\u003E GetAvailableUpdatesAsync(CancellationToken cancellationToken = default)"
    },
    {
      "File": ".\\PcFutureShield.UI\\Services\\WpfNotificationService.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 6,
      "Severity": 1,
      "Preview": "ng System;\nusing System.Windows;\n\nnamespace PcFutureShield.UI.Services\n{\n    public class WpfNotificationService : INotificationService\n    {\n        public void ShowInfo(string title, string message)\n        {\n            Applicat"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 158,
      "Severity": 1,
      "Preview": "ssage}\u0022, \u0022Data Load Error\u0022, MessageBoxButton.OK, MessageBoxImage.Error); }\n            }\n        }\n\n        private async Task RequestOverride()\n        {\n            if (string.IsNullOrWhiteSpace(OverrideReason))\n            {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 220,
      "Severity": 1,
      "Preview": "ex.Message}\u0022, \u0022Deny Error\u0022, MessageBoxButton.OK, MessageBoxImage.Error); }\n            }\n        }\n\n        private async Task EndOverride()\n        {\n            try\n            {\n                // End all active sessions"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 154,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowError(\u0022Data Load Error\u0022, $\u0022Error loading admin override data: {ex.Message}\u0022); } catch { Mess"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 162,
      "Severity": 1,
      "Preview": "hiteSpace(OverrideReason))\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowWarning(\u0022Reason Required\u0022, \u0022Please provide a reason for the override request.\u0022); } catch { M"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 173,
      "Severity": 1,
      "Preview": "dAdminOverrideData(); // Refresh data\n                    try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Access Granted\u0022, \u0022Admin access granted successfully.\u0022); } catch { MessageBox.Show(\u0022Adm"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 177,
      "Severity": 1,
      "Preview": "else\n                {\n                    try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Request Submitted\u0022, $\u0022Admin access request submitted: {result.Message}\u0022); } catch { Me"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 182,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowError(\u0022Request Error\u0022, $\u0022Override request failed: {ex.Message}\u0022); } catch { MessageBox.Show("
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 195,
      "Severity": 1,
      "Preview": "LoadAdminOverrideData(); // Refresh data\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Request Approved\u0022, \u0022Admin request approved.\u0022); } catch { MessageBox.Show(\u0022Admin reques"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 199,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowError(\u0022Approval Error\u0022, $\u0022Approval failed: {ex.Message}\u0022); } catch { MessageBox.Show($\u0022Appro"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 212,
      "Severity": 1,
      "Preview": "LoadAdminOverrideData(); // Refresh data\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Request Denied\u0022, \u0022Admin request denied.\u0022); } catch { MessageBox.Show(\u0022Admin request de"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 216,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowError(\u0022Deny Error\u0022, $\u0022Deny failed: {ex.Message}\u0022); } catch { MessageBox.Show($\u0022Deny failed:"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 233,
      "Severity": 1,
      "Preview": "LoadAdminOverrideData(); // Refresh data\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Sessions Ended\u0022, \u0022All admin sessions ended successfully.\u0022); } catch { MessageBox.Show("
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 237,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowError(\u0022End Error\u0022, $\u0022Failed to end sessions: {ex.Message}\u0022); } catch { MessageBox.Show($\u0022Fai"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 247,
      "Severity": 1,
      "Preview": "{l.Action} by {l.User} - {l.Details}\u0022));\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Admin Logs\u0022, $\u0022Admin Activity Logs:\\n{logText}\u0022); } catch { MessageBox.Show($\u0022Admin Ac"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 251,
      "Severity": 1,
      "Preview": "}\n            else\n            {\n                try { Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E().ShowInfo(\u0022Admin Logs\u0022, \u0022No recent admin activity.\u0022); } catch { MessageBox.Show(\u0022No recent admin"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 94,
      "Severity": 1,
      "Preview": "\u0022Analysis Failed\u0022;\n                LastAnalysis = $\u0022Error: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task PerformQuickScan()\n        {\n            try\n            {\n                AIStatus = \u0022Quick AI Scan...\u0022;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 23,
      "Severity": 1,
      "Preview": "ble _systemRiskScore = 0.0;\n        private string _aiModelStatus = \u0022Loading...\u0022;\n\n        private readonly AdvancedAIDetectionService _aiService;\n\n        public AIDashboardViewModel()\n        {\n            _aiService = ServiceLocat"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 27,
      "Severity": 1,
      "Preview": "e _aiService;\n\n        public AIDashboardViewModel()\n        {\n            _aiService = ServiceLocator.Get\u003CAdvancedAIDetectionService\u003E();\n            AIStatus = \u0022Ready\u0022;\n            LastAnalysis = \u0022Never\u0022;\n            AIModelStatus ="
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 145,
      "Severity": 1,
      "Preview": "{\n            try\n            {\n                var cert = new X509Certificate2(filePath);\n                return true;\n            }\n            catch { return false; }\n        }\n    }\n}"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 147,
      "Severity": 1,
      "Preview": "var cert = new X509Certificate2(filePath);\n                return true;\n            }\n            catch { return false; }\n        }\n    }\n}"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BaseViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 20,
      "Severity": 1,
      "Preview": "rMemberName] string? propertyName = null)\n        {\n            if (EqualityComparer\u003CT\u003E.Default.Equals(field, value)) return false;\n            field = value;\n            OnPropertyChanged(propertyName);\n            return true;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BaseViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 23,
      "Severity": 1,
      "Preview": "als(field, value)) return false;\n            field = value;\n            OnPropertyChanged(propertyName);\n            return true;\n        }\n    }\n\n    public class RelayCommand : ICommand\n    {\n        private readonly Action _exec"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 258,
      "Severity": 1,
      "Preview": "a.Type == PcFutureShield.Common.Services.AlertType.DownloadBlocked \u0026\u0026 a.Message.Contains(\u0022safe\u0022));\n                // Placeholder for tracking cookies blocked - would require browser integration\n                TrackingCookiesBlocked = 0"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 291,
      "Severity": 1,
      "Preview": "return parts[1].Trim();\n            }\n            return string.Empty;\n        }\n\n        private async Task ScanForMaliciousSites()\n        {\n            try\n            {\n                // Scan some known suspic"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 348,
      "Severity": 1,
      "Preview": "blocked downloads or alerts.\u0022);\n                }\n            }\n            catch { }\n        }\n\n        private async Task UpdateExtensionSettings()\n        {\n            try\n            {\n                var settings = new Brow"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 263,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Data Load Error\u0022, $\u0022Error loading browser extension data: {ex.Message}\u0022); } catch {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 315,
      "Severity": 1,
      "Preview": "try\n                {\n                    var notifier = Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                    if (scanResults.Any()) notifier?.ShowInfo(\u0022Scan Complete\u0022, $\u0022Scan Complete"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 325,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Malicious site scan failed: {ex.Message}\u0022); } catch { }"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 334,
      "Severity": 1,
      "Preview": "try\n            {\n                var notifier = Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                if (alerts.Any())\n                {\n                    var alertText = stri"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 362,
      "Severity": 1,
      "Preview": "Browser, \u0022current_user\u0022, settings);\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Settings Updated\u0022, \u0022Browser extension settings updated successfully.\u0022); } catch { }"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 366,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Update Error\u0022, $\u0022Failed to update settings: {ex.Message}\u0022); } catch { }"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 374,
      "Severity": 1,
      "Preview": "try\n            {\n                var notifier = Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                // No direct confirmation dialog in notifier abstraction, fall back to Message"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 399,
      "Severity": 1,
      "Preview": "IsInstalled)\n                {\n                    try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Already Installed\u0022, \u0022Browser extension is already installed.\u0022); } catch { MessageBox."
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 407,
      "Severity": 1,
      "Preview": "IsExtensionInstalled = true;\n                    try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Install Complete\u0022, \u0022Browser extension installed successfully.\u0022); } catch { MessageBox"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 412,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Install Error\u0022, $\u0022Installation failed: {ex.Message}\u0022); } catch { MessageBox.Show($\u0022I"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 427,
      "Severity": 1,
      "Preview": "ExtensionVersion = \u00221.0.4\u0022;\n                    try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Update Complete\u0022, \u0022Browser extension updated successfully.\u0022); } catch { MessageBox.Sh"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 431,
      "Severity": 1,
      "Preview": "else\n                {\n                    try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022No Update Needed\u0022, \u0022Browser extension is up to date.\u0022); } catch { MessageBox.Show(\u0022Br"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 436,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Update Error\u0022, $\u0022Update failed: {ex.Message}\u0022); } catch { MessageBox.Show($\u0022Update f"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 442,
      "Severity": 1,
      "Preview": "te void ConfigureExtension()\n        {\n            try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Configure\u0022, \u0022Opening extension configuration...\u0022); } catch { MessageBox.Show(\u0022Opening"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 447,
      "Severity": 1,
      "Preview": "ate void ViewExtensionLogs()\n        {\n            try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Logs\u0022, \u0022Opening extension logs...\u0022); } catch { MessageBox.Show(\u0022Opening extension log"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 468,
      "Severity": 1,
      "Preview": "a.Message}\u0022));\n                }\n\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Browser Scan\u0022, scanReport); } catch { MessageBox.Show(scanReport, \u0022Browser Scan\u0022, Mes"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 473,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Browser scan failed: {ex.Message}\u0022); } catch { MessageBox.Show($\u0022Brow"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 479,
      "Severity": 1,
      "Preview": "vate void ClearBrowserData()\n        {\n            try { Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Clear Data\u0022, \u0022Clearing browser data...\u0022); } catch { MessageBox.Show(\u0022Clearing browser"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 15,
      "Severity": 1,
      "Preview": "iewModel\n    {\n        private readonly AntivirusOrchestrator _antivirusOrchestrator;\n        private readonly ThreatIntelligenceService _threatIntelligenceService;\n\n        private string _systemHealthStatus = \u0022Scanning...\u0022;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 15,
      "Severity": 1,
      "Preview": "ivate readonly AntivirusOrchestrator _antivirusOrchestrator;\n        private readonly ThreatIntelligenceService _threatIntelligenceService;\n\n        private string _systemHealthStatus = \u0022Scanning...\u0022;\n        private int _activeThreats;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 25,
      "Severity": 1,
      "Preview": "\u003CThreatSummary\u003E _recentThreats;\n\n        public DashboardViewModel(AntivirusOrchestrator antivirusOrchestrator, ThreatIntelligenceService threatIntelligenceService)\n        {\n            _antivirusOrchestrator = antivirusOrchestrator;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 25,
      "Severity": 1,
      "Preview": "eats;\n\n        public DashboardViewModel(AntivirusOrchestrator antivirusOrchestrator, ThreatIntelligenceService threatIntelligenceService)\n        {\n            _antivirusOrchestrator = antivirusOrchestrator;\n            _threatIntelli"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 28,
      "Severity": 1,
      "Preview": "threatIntelligenceService)\n        {\n            _antivirusOrchestrator = antivirusOrchestrator;\n            _threatIntelligenceService = threatIntelligenceService;\n            _recentThreats = new ObservableCollection\u003CThreatSummary\u003E()"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 28,
      "Severity": 1,
      "Preview": "{\n            _antivirusOrchestrator = antivirusOrchestrator;\n            _threatIntelligenceService = threatIntelligenceService;\n            _recentThreats = new ObservableCollection\u003CThreatSummary\u003E();\n\n            QuickScanCo"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 129,
      "Severity": 1,
      "Preview": "{\n                    var notifier = PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                    notifier?.ShowError(\u0022Dashboard Error\u0022, $\u0022Error loading dashboard: {ex.Mess"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 144,
      "Severity": 1,
      "Preview": "boardDataAsync();\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Scan Complete\u0022, $\u0022Quick scan completed. Found {result.TotalThreatsDetected} threats.\u0022"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 148,
      "Severity": 1,
      "Preview": "x)\n            {\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Quick scan failed: {ex.Message}\u0022); } catch { }\n                Syste"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 161,
      "Severity": 1,
      "Preview": "boardDataAsync();\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Scan Complete\u0022, $\u0022Full scan completed. Found {result.TotalThreatsDetected} threats.\u0022)"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 165,
      "Severity": 1,
      "Preview": "x)\n            {\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Full scan failed: {ex.Message}\u0022); } catch { }\n                System"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 174,
      "Severity": 1,
      "Preview": "private async Task UpdateThreatIntelligence()\n        {\n            try\n            {\n                await _threatIntelligenceService.UpdateThreatFeedsAsync();\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 175,
      "Severity": 1,
      "Preview": "reatFeedsAsync();\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Update Complete\u0022, \u0022Threat intelligence database updated successfully.\u0022); } catch { }"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 179,
      "Severity": 1,
      "Preview": "x)\n            {\n                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Update Error\u0022, $\u0022Failed to update threat intelligence: {ex.Message}\u0022); } catch { }"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 159,
      "Severity": 1,
      "Preview": "}\n            finally\n            {\n                IsScanning = false;\n            }\n        }\n\n        private async Task RunCriticalScanAsync()\n        {\n            if (IsScanning) return;\n\n            try\n            {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 235,
      "Severity": 1,
      "Preview": "}\n            finally\n            {\n                IsScanning = false;\n            }\n        }\n\n        private async Task RunCustomScanAsync()\n        {\n            if (IsScanning) return;\n\n            try\n            {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 13,
      "Severity": 1,
      "Preview": "ce PcFutureShield.UI.ViewModels\n{\n    public class EventLogViewModel : BaseViewModel\n    {\n        private readonly IEventLogService _eventLogService;\n\n        public ObservableCollection\u003CRealtimeEvent\u003E Events { get; } = new();"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 60,
      "Severity": 1,
      "Preview": "ntsCommand { get; }\n\n        public EventLogViewModel()\n        {\n            _eventLogService = ServiceLocator.Get\u003CIEventLogService\u003E();\n            _availableSeverities = new ObservableCollection\u003Cstring\u003E(AvailableSeverities);"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 94,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Load Error\u0022, $\u0022Error loading events: {ex.Message}\u0022); } catch { System.Windows.Messag"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 130,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Filter Error\u0022, $\u0022Error applying filter: {ex.Message}\u0022); } catch { System.Windows.Mes"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 148,
      "Severity": 1,
      "Preview": "();\n                    Events.Clear();\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Events Cleared\u0022, \u0022Event log cleared successfully.\u0022); } catch { System.Windows.Message"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 152,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n                {\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Clear Error\u0022, $\u0022Error clearing events: {ex.Message}\u0022); } catch { System.Windows.Mess"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 162,
      "Severity": 1,
      "Preview": "xportPath = _eventLogService.ExportEvents();\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Export Complete\u0022, $\u0022Events exported successfully to: {exportPath}\u0022); } catch { System"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 166,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Export Error\u0022, $\u0022Error exporting events: {ex.Message}\u0022); } catch { System.Windows.Me"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 180,
      "Severity": 1,
      "Preview": "$\u0022File: {SelectedEvent.File}\u0022;\n\n            try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Event Details\u0022, details); } catch { System.Windows.MessageBox.Show(details, \u0022Event De"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 108,
      "Severity": 1,
      "Preview": "verity != \u0022All\u0022 \u0026\u0026 !evt.Severity.Equals(SelectedSeverity, StringComparison.OrdinalIgnoreCase))\n                        return false;\n\n                    // Filter by text\n                    if (!string.IsNullOrWhiteSpace(FilterText))"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 119,
      "Severity": 1,
      "Preview": "evt.Severity.ToLower().Contains(searchText);\n                    }\n\n                    return true;\n                }).OrderByDescending(e =\u003E e.Time).ToList();\n\n                Events.Clear();"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 142,
      "Severity": 1,
      "Preview": "sage}\u0022, \u0022Data Load Error\u0022, MessageBoxButton.OK, MessageBoxImage.Error)); }\n            }\n        }\n\n        private async Task ScanForCasinos()\n        {\n            try\n            {\n                // Scan known suspicious casino"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 242,
      "Severity": 1,
      "Preview": ".\u0022, \u0022Fraud Reports\u0022, MessageBoxButton.OK, MessageBoxImage.Information)); }\n            }\n        }\n\n        private async Task UpdateProtectionSettings()\n        {\n            try\n            {\n                // Save settings to a"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 138,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Data Load Error\u0022, $\u0022Error loading gaming protection data: {ex.Message}\u0022); } catch {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 166,
      "Severity": 1,
      "Preview": "f (scanResults.Any())\n                {\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowWarning(\u0022Scan Complete\u0022, $\u0022Casino Scan Complete. Detected issues:\\n{string.Join(\u0022\\n\u0022, scanR"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 170,
      "Severity": 1,
      "Preview": "else\n                {\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Scan Complete\u0022, \u0022Casino scan complete. No high-risk casinos detected.\u0022); } catch { Sy"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 177,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Casino scan failed: {ex.Message}\u0022); } catch { System.Windows.Applicat"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 213,
      "Severity": 1,
      "Preview": "if (rigResults.Any())\n                {\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowWarning(\u0022Scan Complete\u0022, $\u0022Rig Scan Complete. Detected issues:\\n{string.Join(\u0022\\n\u0022, rigResul"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 217,
      "Severity": 1,
      "Preview": "else\n                {\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Scan Complete\u0022, \u0022Rig scan complete. No suspicious gaming processes detected.\u0022); } cat"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 224,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Rig scan failed: {ex.Message}\u0022); } catch { System.Windows.Application"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 234,
      "Severity": 1,
      "Preview": "{r.Description} (Severity: {r.Severity})\u0022));\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowWarning(\u0022Fraud Reports\u0022, $\u0022Fraud Reports:\\n{reportText}\u0022); } catch { System.Windows.Applica"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 238,
      "Severity": 1,
      "Preview": "}\n            else\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Fraud Reports\u0022, \u0022No recent fraud reports.\u0022); } catch { System.Windows.Application.Cur"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 261,
      "Severity": 1,
      "Preview": "ile.WriteAllTextAsync(settingsPath, json);\n\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Settings Updated\u0022, \u0022Gaming protection settings updated successfully.\u0022); } catch { Sys"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 265,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Update Error\u0022, $\u0022Failed to update settings: {ex.Message}\u0022); } catch { System.Windows"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 141,
      "Severity": 1,
      "Preview": "MessageBoxImage.Warning);\n                return;\n            }\n\n            try\n            {\n                // Simulate license activation (in real implementation, this would call a service)\n                await Task.Delay(1000);"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 142,
      "Severity": 1,
      "Preview": "late license activation (in real implementation, this would call a service)\n                await Task.Delay(1000); // Simulate network call\n\n                // For demo purposes, accept any key that looks valid\n                if (Prod"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 173,
      "Severity": 1,
      "Preview": "== System.Windows.MessageBoxResult.Yes)\n            {\n                try\n                {\n                    // Simulate license deactivation\n                    await Task.Delay(1000);\n\n                    LicenseStatus = \u0022Inact"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 193,
      "Severity": 1,
      "Preview": "}\n\n        private async Task RefreshLicenseAsync()\n        {\n            try\n            {\n                // Simulate license refresh\n                await Task.Delay(1000);\n\n                LoadLicenseData(); // Refresh all d"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 213,
      "Severity": 1,
      "Preview": "== System.Windows.MessageBoxResult.Yes)\n            {\n                try\n                {\n                    // Simulate license transfer\n                    await Task.Delay(1000);\n\n                    LicenseStatus = \u0022Transferr"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 131,
      "Severity": 1,
      "Preview": "tection\u0022, Status = \u0022Enabled\u0022, Description = \u0022Gaming-specific security\u0022 });\n            }\n        }\n\n        private async Task ActivateLicenseAsync()\n        {\n            if (string.IsNullOrWhiteSpace(ProductKey))\n            {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 15,
      "Severity": 1,
      "Preview": "yChanged\n    {\n        private readonly AntivirusOrchestrator _antivirusOrchestrator;\n        private readonly ThreatIntelligenceService _threatIntelligenceService;\n        private readonly ParentalControlService _parentalControlService"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 15,
      "Severity": 1,
      "Preview": "ivate readonly AntivirusOrchestrator _antivirusOrchestrator;\n        private readonly ThreatIntelligenceService _threatIntelligenceService;\n        private readonly ParentalControlService _parentalControlService;\n        private readonly"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 73,
      "Severity": 1,
      "Preview": "_antivirusOrchestrator = PcFutureShield.UI.Services.ServiceLocator.Get\u003CAntivirusOrchestrator\u003E();\n            _threatIntelligenceService = PcFutureShield.UI.Services.ServiceLocator.Get\u003CThreatIntelligenceService\u003E();\n            _parental"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 73,
      "Severity": 1,
      "Preview": "AntivirusOrchestrator\u003E();\n            _threatIntelligenceService = PcFutureShield.UI.Services.ServiceLocator.Get\u003CThreatIntelligenceService\u003E();\n            _parentalControlService = PcFutureShield.UI.Services.ServiceLocator.Get\u003CParentalCon"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 113,
      "Severity": 1,
      "Preview": "{\n                            var dashboardVm = new DashboardViewModel(_antivirusOrchestrator, _threatIntelligenceService);\n                            CurrentView = dashboardVm; // DataTemplate will render DashboardView"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 127,
      "Severity": 1,
      "Preview": "var virusScannerAdapter = PcFutureShield.UI.Services.ServiceLocator.Get\u003CPcFutureShield.UI.ViewModels.IVirusScannerService\u003E();\n                            virusScanView.DataContext = new PcFutureShield.UI.ViewModels.Virus"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 351,
      "Severity": 1,
      "Preview": "{\n                            var notifier = PcFutureShield.UI.Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                            notifier.ShowError(\u0022Navigation Error\u0022, $\u0022Unknown view: {viewName}\u0022"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\MainViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 365,
      "Severity": 1,
      "Preview": "{\n                    var notifier = PcFutureShield.UI.Services.ServiceLocator.Get\u003CPcFutureShield.UI.Services.INotificationService\u003E();\n                    notifier.ShowError(\u0022Navigation Error\u0022, $\u0022Failed to navigate to {viewName}:"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 231,
      "Severity": 1,
      "Preview": "parentalControlService.RemoveUserProfile(profile.Name);\n                }\n            }\n        }\n\n        private async Task UpdateFilters()\n        {\n            try\n            {\n                // Update content filters through"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 224,
      "Severity": 1,
      "Preview": "finally\n            {\n                IsAnalyzing = false;\n            }\n        }\n\n        private async Task PerformOptimizationAsync()\n        {\n            if (IsOptimizing) return;\n\n            IsOptimizing = tr"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 267,
      "Severity": 1,
      "Preview": "ons\n            return await _optimizationService.PerformDeepOptimizationAsync(options);\n        }\n\n        private async Task PerformRepairAsync()\n        {\n            if (IsRepairing) return;\n\n            IsRepairing = true;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 363,
      "Severity": 1,
      "Preview": "stalling = false;\n            }\n        }\n\n        // Implementations for XAML referenced commands\n        private async Task PerformCleanTempFilesAsync(CancellationToken cancellationToken)\n        {\n            OptimizationStatus ="
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 383,
      "Severity": 1,
      "Preview": "orary file cleanup completed.\u0022;\n            }\n\n            OptimizationProgress = 100;\n        }\n\n        private async Task PerformDefragmentAsync(CancellationToken cancellationToken)\n        {\n            OptimizationStatus = \u0022Def"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 409,
      "Severity": 1,
      "Preview": "file cleanup in service\n            await PerformCleanTempFilesAsync(cancellationToken);\n        }\n\n        private async Task PerformOptimizeStartupAsync(CancellationToken cancellationToken)\n        {\n            OptimizationStatus ="
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 429,
      "Severity": 1,
      "Preview": "artup optimization completed.\u0022;\n            }\n\n            OptimizationProgress = 100;\n        }\n\n        private async Task PerformUpdateDriversAsync(CancellationToken cancellationToken)\n        {\n            OptimizationStatus = \u0022"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 449,
      "Severity": 1,
      "Preview": "river update check completed.\u0022;\n            }\n\n            OptimizationProgress = 100;\n        }\n\n        private async Task PerformFullOptimizationAsync(CancellationToken cancellationToken)\n        {\n            OptimizationStatus"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 470,
      "Severity": 1,
      "Preview": "imizationStatus = \u0022Full optimization complete.\u0022;\n            OptimizationProgress = 100;\n        }\n\n        private async Task GenerateReportAsync()\n        {\n            try\n            {\n                var report = new"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R032",
      "Message": "Async method defined but never awaited or invoked",
      "Line": 403,
      "Severity": 1,
      "Preview": "= \u0022Defragmentation completed.\u0022;\n            }\n\n            OptimizationProgress = 100;\n        }\n\n        private async Task PerformClearCacheAsync(CancellationToken cancellationToken)\n        {\n            // Clear cache is part of"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 216,
      "Severity": 0,
      "Preview": "{\n                // Log error to console as fallback; UI should surface via logger/notification\n                Console.WriteLine($\u0022Error analyzing system: {ex.Message}\u0022);\n            }\n            finally\n            {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 251,
      "Severity": 0,
      "Preview": "OptimizationProgress = 100;\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error performing optimization: {ex.Message}\u0022);\n                OptimizationStatus = \u0022Optimization f"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 289,
      "Severity": 0,
      "Preview": "d.\u0022 : \u0022Repair completed with issues.\u0022;\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error performing repair: {ex.Message}\u0022);\n                OptimizationStatus = \u0022Repair failed.\u0022;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 312,
      "Severity": 0,
      "Preview": "OptimizationProgress = 100;\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error boosting performance: {ex.Message}\u0022);\n                OptimizationStatus = \u0022Boost failed.\u0022;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs",
      "RuleId": "R012",
      "Message": "Method only writes to Console",
      "Line": 353,
      "Severity": 0,
      "Preview": "OptimizationProgress = 100;\n            }\n            catch (Exception ex)\n            {\n                Console.WriteLine($\u0022Error performing uninstall: {ex.Message}\u0022);\n                OptimizationStatus = \u0022Uninstall failed."
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\RealtimeProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 19,
      "Severity": 1,
      "Preview": "timeEvent\u003E Events { get; } = new();\n\n        public ICommand ViewEventsCommand { get; }\n\n\n        private readonly IRealtimeProtectionService _protectionService;\n        private readonly IEventLogService _eventLogService;\n\n        p"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\RealtimeProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 20,
      "Severity": 1,
      "Preview": "Command { get; }\n\n\n        private readonly IRealtimeProtectionService _protectionService;\n        private readonly IEventLogService _eventLogService;\n\n        public RealtimeProtectionViewModel()\n        {\n            // Dependency"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\RealtimeProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 25,
      "Severity": 1,
      "Preview": "{\n            // Dependency injection or service locator\n            _protectionService = ServiceLocator.Get\u003CIRealtimeProtectionService\u003E();\n            _eventLogService = ServiceLocator.Get\u003CIEventLogService\u003E();\n\n            Vi"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\RealtimeProtectionViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 26,
      "Severity": 1,
      "Preview": "rotectionService = ServiceLocator.Get\u003CIRealtimeProtectionService\u003E();\n            _eventLogService = ServiceLocator.Get\u003CIEventLogService\u003E();\n\n            ViewEventsCommand = new RelayCommand(ViewEvents);\n\n            // Subscribe to rea"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 42,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Initialization Error\u0022, $\u0022ScannerViewModel initialization failed: {ex.Message}\\n{ex.S"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 69,
      "Severity": 1,
      "Preview": "});\n\n                Progress = 100;\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Scan Complete\u0022, $\u0022Scan completed. Found {Results.Count(r =\u003E r.IsMalicious)} threats.\u0022"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 73,
      "Severity": 1,
      "Preview": "catch (Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Scan Error\u0022, $\u0022Scan failed: {ex.Message}\u0022); } catch { System.Windows.Application.Cur"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SettingsViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 225,
      "Severity": 1,
      "Preview": "t(\u0022DataRetentionDays\u0022, DataRetentionDays);\n\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Settings Saved\u0022, \u0022Settings saved successfully!\u0022); } catch { System.Windows.Applicatio"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SettingsViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 229,
      "Severity": 1,
      "Preview": "catch (System.Exception ex)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowError(\u0022Save Error\u0022, $\u0022Failed to save settings: {ex.Message}\u0022); } catch { System.Windows.App"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SettingsViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 262,
      "Severity": 1,
      "Preview": "private void CheckForUpdates()\n        {\n            try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Update Check\u0022, \u0022Checking for updates...\u0022); } catch { System.Windows.Application.Curre"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SettingsViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 274,
      "Severity": 1,
      "Preview": "al implementation, this would clear all data\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Data Cleared\u0022, \u0022All data cleared successfully.\u0022); } catch { System.Windows.Applicatio"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SettingsViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 286,
      "Severity": 1,
      "Preview": "rMemberName] string? propertyName = null)\n        {\n            if (EqualityComparer\u003CT\u003E.Default.Equals(field, value)) return false;\n            field = value;\n            OnPropertyChanged(propertyName);\n            return true;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SettingsViewModel.cs",
      "RuleId": "R011",
      "Message": "Method returns constant true/false/0",
      "Line": 289,
      "Severity": 1,
      "Preview": "als(field, value)) return false;\n            field = value;\n            OnPropertyChanged(propertyName);\n            return true;\n        }\n    }\n}"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 120,
      "Severity": 1,
      "Preview": "agnostics failed\u0022;\n                RepairStatus = $\u0022Error: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task FixRegistryAsync()\n        {\n            try\n            {\n                CurrentRepairOperation = \u0022Fixi"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 151,
      "Severity": 1,
      "Preview": "{\n                RepairStatus = $\u0022Registry repair failed: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task RepairSystemFilesAsync()\n        {\n            try\n            {\n                CurrentRepairOperation ="
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 181,
      "Severity": 1,
      "Preview": "RepairStatus = $\u0022System file repair failed: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task CleanStartupAsync()\n        {\n            try\n            {\n                CurrentRepairOperation = \u0022Cle"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 212,
      "Severity": 1,
      "Preview": "{\n                RepairStatus = $\u0022Startup cleanup failed: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task FixNetworkAsync()\n        {\n            try\n            {\n                CurrentRepairOperation = \u0022Fixin"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 242,
      "Severity": 1,
      "Preview": "{\n                RepairStatus = $\u0022Network repair failed: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task RepairWindowsUpdateAsync()\n        {\n            try\n            {\n                CurrentRepairOperation"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 330,
      "Severity": 1,
      "Preview": "RepairStatus = $\u0022Report generation failed: {ex.Message}\u0022;\n            }\n        }\n\n        private async Task EmergencyRepairAsync()\n        {\n            try\n            {\n                CurrentRepairOperation = \u0022"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 177,
      "Severity": 1,
      "Preview": "UpdateProgress = 0;\n                UpdateStatus = \u0022Contacting update server...\u0022;\n\n                // Simulate network delay\n                await Task.Delay(2000);\n                UpdateProgress = 50;"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R004",
      "Message": "simulate/stub/fake/mock/placeholder",
      "Line": 339,
      "Severity": 1,
      "Preview": "UpdateProgress = 0;\n                    UpdateStatus = \u0022Installing update files...\u0022;\n\n                    // Simulate installation progress\n                    for (int i = 0; i \u003C= 100; i \u002B= 5)\n                    {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 212,
      "Severity": 1,
      "Preview": "essage}\u0022;\n                CurrentUpdateOperation = \u0022Update check failed\u0022;\n            }\n        }\n\n        private async Task DownloadUpdatesAsync()\n        {\n            if (UpdatesAvailable == 0)\n            {\n                try"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R009",
      "Message": "Async method has no await",
      "Line": 310,
      "Severity": 1,
      "Preview": "}\n            finally\n            {\n                cts.Dispose();\n            }\n        }\n\n        private async Task InstallUpdatesAsync()\n        {\n            if (UpdatesAvailable == 0)\n            {\n                try"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 216,
      "Severity": 1,
      "Preview": "if (UpdatesAvailable == 0)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022No Updates\u0022, \u0022No updates available to download.\u0022); } catch { System.Windows.Applicati"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 314,
      "Severity": 1,
      "Preview": "if (UpdatesAvailable == 0)\n            {\n                try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022No Updates\u0022, \u0022No updates available to install.\u0022); } catch { System.Windows.Applicatio"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 354,
      "Severity": 1,
      "Preview": "on = \u0022Updates installed successfully\u0022;\n\n                    try { ServiceLocator.GetOrNull\u003CPcFutureShield.UI.Services.INotificationService\u003E()?.ShowInfo(\u0022Installation Complete\u0022, \u0022Updates installed successfully! Please restart the applicati"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\VirusScanViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 15,
      "Severity": 1,
      "Preview": "PcFutureShield.Engine.VirusScanner;\nusing System.Linq;\n\nnamespace PcFutureShield.UI.ViewModels\n{\n    // Define the IVirusScannerService interface if not already defined elsewhere\n    public interface IVirusScannerService\n    {"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\VirusScanViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 16,
      "Severity": 1,
      "Preview": ".ViewModels\n{\n    // Define the IVirusScannerService interface if not already defined elsewhere\n    public interface IVirusScannerService\n    {\n        Task\u003CScanResult\u003E ScanAsync(string[] paths, CancellationToken cancellationToken);"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\VirusScanViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 22,
      "Severity": 1,
      "Preview": "}\n\n    // Adapter to wrap Engine.VirusScannerService for use in UI\n    public class VirusScannerServiceAdapter : IVirusScannerService\n    {\n        private readonly VirusScannerService _engineService;\n\n        public VirusScanner"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\VirusScanViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 67,
      "Severity": 1,
      "Preview": "set; } = false;\n    }\n\n    public class VirusScanViewModel : INotifyPropertyChanged\n    {\n        private readonly IVirusScannerService _scannerService;\n        private CancellationTokenSource? _cts;\n\n        public ObservableCollec"
    },
    {
      "File": ".\\PcFutureShield.UI\\ViewModels\\VirusScanViewModel.cs",
      "RuleId": "R031",
      "Message": "DI interface referenced but not registered",
      "Line": 83,
      "Severity": 1,
      "Preview": "mand StartScanCommand { get; }\n        public ICommand CancelScanCommand { get; }\n\n        public VirusScanViewModel(IVirusScannerService scannerService)\n        {\n            _scannerService = scannerService;\n            StartScanCom"
    }
  ],
  "Rules": [
    {
      "Id": "R001",
      "Title": "Not Implemented",
      "Description": "Throws NotImplementedException",
      "Severity": 2,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R002",
      "Title": "TODO Placeholder",
      "Description": "Contains TODO/FIXME/HACK comment",
      "Severity": 1,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R003",
      "Title": "Stub ViewModel",
      "Description": "StubViewModel or \u0027Functionality not available\u0027 present",
      "Severity": 2,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R004",
      "Title": "Simulated Logic",
      "Description": "simulate/stub/fake/mock/placeholder",
      "Severity": 1,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R005",
      "Title": "Async Task.CompletedTask",
      "Description": "Async method returns Task.CompletedTask",
      "Severity": 1,
      "Pattern": {
        "Options": 8,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R006",
      "Title": "Empty Method Body",
      "Description": "Method has empty body (stub)",
      "Severity": 1,
      "Pattern": {
        "Options": 8,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R007",
      "Title": "Return default/null",
      "Description": "Method returns default or null",
      "Severity": 1,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R008",
      "Title": "Random-based detection",
      "Description": "Random.Next() used in antivirus logic",
      "Severity": 2,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R009",
      "Title": "Async without await",
      "Description": "Async method has no await",
      "Severity": 1,
      "Pattern": {
        "Options": 8,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R010",
      "Title": "Hardcoded Bloatware List",
      "Description": "Hardcoded vendor list detected",
      "Severity": 0,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R030",
      "Title": "Unbound XAML Binding",
      "Description": "Binding/Command in XAML has no corresponding property in ViewModel",
      "Severity": 2,
      "Pattern": {
        "Options": 9,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R031",
      "Title": "Interface Not Registered",
      "Description": "DI interface referenced but not registered",
      "Severity": 1,
      "Pattern": {
        "Options": 8,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    },
    {
      "Id": "R032",
      "Title": "Async Task Never Started",
      "Description": "Async method defined but never awaited or invoked",
      "Severity": 1,
      "Pattern": {
        "Options": 8,
        "RightToLeft": false,
        "MatchTimeout": "-00:00:00.0010000"
      }
    }
  ]
}