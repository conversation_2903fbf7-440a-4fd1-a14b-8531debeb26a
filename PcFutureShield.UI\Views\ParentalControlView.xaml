<UserControl x:Class="PcFutureShield.UI.Views.ParentalControlView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="20">
                <TextBlock Text="Parental Control Center" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- Main Controls -->
                <UniformGrid Columns="2" Rows="3" Margin="0,0,0,24">
                    <!-- Blocking Level Control -->
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Protection Level" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Current Level:" FontSize="14" Margin="0,0,0,4"/>
                            <ComboBox ItemsSource="{Binding BlockingLevels}"
                                      SelectedItem="{Binding CurrentBlockingLevel}"
                                      FontSize="16" FontWeight="Bold"
                                      Margin="0,0,0,12"/>
                            <TextBlock Text="{Binding BlockingLevelDescription}"
                                       FontSize="12"
                                       Foreground="{DynamicResource SecondaryFontBrush}"
                                       TextWrapping="Wrap"
                                       Margin="0,0,0,12"/>

                            <!-- Emergency Controls -->
                            <StackPanel Orientation="Horizontal" Margin="0,8,0,0">
                                <Button Content="Emergency Disable"
                                        Command="{Binding EmergencyDisableCommand}"
                                        Style="{DynamicResource ChromeButtonStyle}"
                                        Background="#FF6B6B"
                                        Width="120" Height="32"
                                        Margin="0,0,8,0"/>
                                <Button Content="Re-Enable"
                                        Command="{Binding ReEnableProtectionCommand}"
                                        Style="{DynamicResource ChromeButtonStyle}"
                                        Background="#51CF66"
                                        Width="80" Height="32"
                                        Visibility="{Binding IsEmergencyDisableVisible, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Traditional Controls -->
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Individual Controls" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <CheckBox Content="Enable Parental Controls" IsChecked="{Binding IsEnabled}" Margin="0,0,0,8"/>
                            <CheckBox Content="Block Social Media" IsChecked="{Binding BlockSocialMedia}" Margin="0,0,0,8"/>
                            <CheckBox Content="Block Adult Content" IsChecked="{Binding BlockAdultContent}" Margin="0,0,0,8"/>
                            <CheckBox Content="Block Gambling Sites" IsChecked="{Binding BlockGambling}" Margin="0,0,0,8"/>
                            <CheckBox Content="Monitor Chat Activity" IsChecked="{Binding MonitorChatActivity}" Margin="0,0,0,8"/>
                            <CheckBox Content="Enable Safe Search" IsChecked="{Binding EnableSafeSearch}" Margin="0,0,0,8"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Screen Time Limits" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Daily Limit:" FontSize="16" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding DailyScreenTimeLimit, StringFormat=hh\\:mm}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryColorBrush}" Margin="0,0,0,16"/>
                            <TextBlock Text="Current User Profile:" FontSize="16" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding CurrentUserProfile}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="User Profiles" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <ListBox ItemsSource="{Binding UserProfiles}" SelectedItem="{Binding SelectedUserProfile}" Height="120" Margin="0,0,0,12">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding Name}" FontSize="14"/>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            <StackPanel Orientation="Horizontal">
                                <Button Content="Add Profile" Command="{Binding AddUserProfileCommand}" Style="{DynamicResource ChromeButtonStyle}" Width="100" Height="32" Margin="0,0,8,0"/>
                                <Button Content="Remove" Command="{Binding RemoveUserProfileCommand}" CommandParameter="{Binding SelectedUserProfile}" Style="{DynamicResource ChromeButtonStyle}" Width="80" Height="32"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Recent Blocks" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <ListBox ItemsSource="{Binding RecentBlocks}" Height="120" Margin="0,0,0,12">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Content}" FontSize="12" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding BlockedTime, StringFormat=g}" FontSize="10" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                            <Button Content="View All Blocked" Command="{Binding ViewBlockedContentCommand}" Style="{DynamicResource GlassButtonStyle}" Width="140" Height="36"/>
                        </StackPanel>
                    </Border>

                    <!-- Status Panel -->
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Protection Status" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <TextBlock Text="Status: " FontSize="14"/>
                                <TextBlock Text="{Binding IsEnabled, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='Active|Disabled'}"
                                           FontSize="14" FontWeight="Bold"
                                           Foreground="{Binding IsEnabled, Converter={StaticResource BooleanToBrushConverter}}"/>
                            </StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                <TextBlock Text="Emergency Mode: " FontSize="14"/>
                                <TextBlock Text="{Binding EmergencyDisableMode, Converter={StaticResource BooleanToStringConverter}, ConverterParameter='ON|OFF'}"
                                           FontSize="14" FontWeight="Bold"
                                           Foreground="{Binding EmergencyDisableMode, Converter={StaticResource BooleanToBrushConverter}, ConverterParameter='#FF6B6B|#51CF66'}"/>
                            </StackPanel>
                            <Button Content="Test Protection" Command="{Binding TestBlockingCommand}" Style="{DynamicResource GlassButtonStyle}" Width="140" Height="36" Margin="0,8,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Quick Actions Panel -->
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Quick Actions" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Need to quickly adjust protection?" FontSize="12" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,8"/>
                            <StackPanel Orientation="Horizontal" Margin="0,8,0,8">
                                <Button Content="Off" Command="{Binding SetBlockingLevelCommand}" CommandParameter="Off" Style="{DynamicResource ChromeButtonStyle}" Width="50" Height="32" Margin="0,0,4,0"/>
                                <Button Content="Low" Command="{Binding SetBlockingLevelCommand}" CommandParameter="Low" Style="{DynamicResource ChromeButtonStyle}" Width="50" Height="32" Margin="0,0,4,0"/>
                                <Button Content="Med" Command="{Binding SetBlockingLevelCommand}" CommandParameter="Medium" Style="{DynamicResource ChromeButtonStyle}" Width="50" Height="32" Margin="0,0,4,0"/>
                                <Button Content="High" Command="{Binding SetBlockingLevelCommand}" CommandParameter="High" Style="{DynamicResource ChromeButtonStyle}" Width="50" Height="32" Margin="0,0,4,0"/>
                                <Button Content="Max" Command="{Binding SetBlockingLevelCommand}" CommandParameter="Maximum" Style="{DynamicResource ChromeButtonStyle}" Width="50" Height="32"/>
                            </StackPanel>
                            <TextBlock Text="Current: " FontSize="12" Margin="0,4,0,0"/>
                            <TextBlock Text="{Binding CurrentBlockingLevel}" FontSize="14" FontWeight="Bold" Foreground="{DynamicResource PrimaryColorBrush}"/>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,24,0,0">
                    <Button Content="Update Filters" Command="{Binding UpdateFiltersCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="160" Height="48" Margin="0,0,20,0"/>
                    <Button Content="Save Settings" Style="{DynamicResource GlassButtonStyle}" Width="160" Height="48"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
