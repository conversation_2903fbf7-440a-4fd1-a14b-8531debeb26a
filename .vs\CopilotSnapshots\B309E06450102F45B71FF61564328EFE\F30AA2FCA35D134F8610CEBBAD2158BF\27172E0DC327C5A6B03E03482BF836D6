﻿using PcFutureShield.UI.ViewModels;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;

namespace PcFutureShield.UI.ViewModels
{
    public class QuarantineViewModel : INotifyPropertyChanged
    {
        public ObservableCollection<string> QuarantinedFiles { get; } = new ObservableCollection<string>();

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private RelayCommand quarantineCommand;
        public ICommand QuarantineCommand => quarantineCommand ??= new RelayCommand(Quarantine);

        private void Quarantine(object commandParameter)
        {
        }

        private RelayCommand pcOptimizationCommand;
        public ICommand PcOptimizationCommand => pcOptimizationCommand ??= new RelayCommand(PcOptimization);

        private void PcOptimization(object commandParameter)
        {
        }
    }
}
