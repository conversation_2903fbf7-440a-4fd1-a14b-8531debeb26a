﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class RealtimeProtectionViewModel : INotifyPropertyChanged
    {
        private bool _isProtectionEnabled = true;

        public ObservableCollection<RealtimeEvent> Events { get; } = new();

        public ICommand ViewEventsCommand { get; }


        private readonly IRealtimeProtectionService _protectionService;
        private readonly IEventLogService _eventLogService;

        public RealtimeProtectionViewModel()
        {
            // Dependency injection or service locator
            _protectionService = ServiceLocator.Get<IRealtimeProtectionService>();
            _eventLogService = ServiceLocator.Get<IEventLogService>();

            ViewEventsCommand = new RelayCommand(ViewEvents);

            // Subscribe to real-time protection events
            _protectionService.ProtectionEvent += OnProtectionEvent;
            _protectionService.ProtectionStatusChanged += OnProtectionStatusChanged;
            IsProtectionEnabled = _protectionService.IsEnabled;

            // Load historical events from event log
            foreach (var evt in _eventLogService.GetRecentEvents())
                Events.Add(evt);
        }

        private void OnProtectionEvent(object? sender, RealtimeEvent evt)
        {
            App.Current.Dispatcher.Invoke(() => Events.Insert(0, evt));
        }

        private void OnProtectionStatusChanged(object? sender, bool enabled)
        {
            if (_isProtectionEnabled != enabled)
            {
                _isProtectionEnabled = enabled;
                OnPropertyChanged(nameof(IsProtectionEnabled));
            }
        }

        public bool IsProtectionEnabled
        {
            get => _isProtectionEnabled;
            set
            {
                if (_isProtectionEnabled != value)
                {
                    _isProtectionEnabled = value;
                    _protectionService.SetEnabled(value);
                    OnPropertyChanged();
                }
            }
        }

        private void ViewEvents()
        {
            // Open a real event log window
            _eventLogService.ShowEventLogWindow();
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class RealtimeEvent
    {
        public DateTime Time { get; set; }
        public string Event { get; set; } = string.Empty;
        public string File { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty;
    }
}

