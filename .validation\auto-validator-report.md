﻿# PcFutureShield Auto-Validator Report
Generated: 9/1/2025 10:01:07 AM -04:00
Files scanned: 237
Findings: 422

- **Warning** [R031] .\PcFutureShield.Common\Interfaces\IVirusScannerService.cs:L45 — DI interface referenced but not registered
  
  ```
/// <summary>
    /// Provides virus scanning and threat detection services
    /// </summary>
    public interface IVirusScannerService : IDisposable
    {
        /// <summary>
        /// Event raised when a threat is detected
  ```

- **Warning** [R031] .\PcFutureShield.Common\Interfaces\QuarantineTypes.cs:L6 — DI interface referenced but not registered
  
  ```
contained duplicate definitions of QuarantineItem and QuarantineResult
    // These types are now properly defined in IVirusScannerService.cs and IQuarantineManager.cs
    // This file can be removed or left empty to avoid build conflict
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L9 — DI interface referenced but not registered
  
  ```
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Security.Principal;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Text.Json;

namespace PcFutureShield.Common.Services
{
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L42 — Async method has no await
  
  ```
cipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }

        public async Task<AdminRequestResult> RequestAdminAccessAsync(string reason, TimeSpan duration)
        {
            var res
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L83 — Async method has no await
  
  ```
ailed to request admin access: {ex.Message}";
            }

            return result;
        }

        public async Task<ElevationResult> ElevateProcessAsync(string processPath, string arguments = "")
        {
            var r
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L120 — Async method has no await
  
  ```
= $"Failed to elevate process: {ex.Message}";
            }

            return result;
        }

        public async Task<AdminSessionResult> CreateTemporaryAdminSessionAsync(string reason, TimeSpan duration)
        {
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L148 — Method returns constant true/false/0
  
  ```
sion))
            {
                if (session.ExpiresAt > DateTime.UtcNow)
                {
                    return true;
                }
                else
                {
                    // Session expired, remove
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L157 — Method returns constant true/false/0
  
  ```
veSessions.Remove(sessionId);
                    SaveActiveSessions();
                }
            }
            return false;
        }

        public void EndAdminSession(string sessionId)
        {
            if (_activeSes
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L173 — Async method has no await
  
  ```
return _pendingRequests.Where(r => r.Status == AdminRequestStatus.Pending).ToList();
        }

        public async Task ApproveAdminRequestAsync(string requestId, string approvedBy)
        {
            var request = _pendingR
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L190 — Async method has no await
  
  ```
request.SessionId = sessionId;

                SavePendingRequests();
            }
        }

        public async Task DenyAdminRequestAsync(string requestId, string deniedBy, string reason)
        {
            var request =
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L239 — Async method has no await
  
  ```
eRemaining = s.ExpiresAt - DateTime.UtcNow
                })
                .ToList();
        }

        public async Task<SecurityAuditResult> PerformSecurityAuditAsync()
        {
            var result = new SecurityAuditResult
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L378 — Method only writes to Console
  
  ```
File.WriteAllText(_adminDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving active sessions: {ex.Message}");
            }
        }

        private void Save
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\AdminOverrideService.cs:L392 — Method only writes to Console
  
  ```
File.WriteAllText(requestsPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving pending requests: {ex.Message}");
            }
        }
    }

    // Supporting
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L22 — DI interface referenced but not registered
  
  ```
d AI-powered threat detection service with machine learning capabilities
    /// </summary>
    public class AdvancedAIDetectionService
    {
        private readonly MLContext _mlContext;
        private ITransformer? _trainedModel;
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L56 — DI interface referenced but not registered
  
  ```
PackedFloat { get; set; }
            public float HasImportsFloat { get; set; }
        }

        public AdvancedAIDetectionService()
        {
            _mlContext = new MLContext(seed: 0);
            var appData = Environment.
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L83 — Method only writes to Console
  
  ```
rainInitialModel();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"AI Model loading failed: {ex.Message}");
                TrainInitialModel();
            }
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L157 — Async method has no await
  
  ```
Label = true
                });
            }

            return data;
        }

        public async Task<AIAnalysisResult> AnalyzeFileAsync(string filePath)
        {
            var result = new AIAnalysisResult
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L244 — Method only writes to Console
  
  ```
= CountSuspiciousStrings(filePath);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"Feature extraction error: {ex.Message}");
            }

            return features;
        }
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L291 — Method returns constant true/false/0
  
  ```
ntains(pattern, StringComparison.OrdinalIgnoreCase));
            }
            catch
            {
                return 0;
            }
        }

        private async Task<List<string>> AnalyzeBehavioralPatternsAsync(string fi
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L295 — Async method has no await
  
  ```
}
            catch
            {
                return 0;
            }
        }

        private async Task<List<string>> AnalyzeBehavioralPatternsAsync(string filePath)
        {
            var indicators = new Li
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L378 — Async method has no await
  
  ```
return ThreatRisk.Safe;
        }

        // Enhanced system analysis with AI insights
        public async Task<AIEnhancedAnalysisResult> AnalyzeSystemAsync()
        {
            var result = new AIEnhancedAnalysisRes
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L477 — Async method has no await
  
  ```
result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        private async Task<List<string>> GetAutorunEntriesAsync()
        {
            var autorunEntries = new List<string>();
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L548 — Method returns constant true/false/0
  
  ```
y.Cryptography.X509Certificates.X509Certificate2(filePath);
                publisher = cert.Subject;
                return true;
            }
            catch { return false; }
        }

        private static string ThreatIntel
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AdvancedAIDetectionService.cs:L550 — Method returns constant true/false/0
  
  ```
filePath);
                publisher = cert.Subject;
                return true;
            }
            catch { return false; }
        }

        private static string ThreatIntelLookup(string sha256, string name, string path)
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AIDetectionService.cs:L8 — DI interface referenced but not registered
  
  ```
g System.Linq;
using PcFutureShield.Common.Services;

namespace PcFutureShield.Common.Services
{
    public class AIDetectionService
    {
        public string AnalyzeSystem()
        {
            // --- REAL AI/SECURITY ANALYSIS
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AIDetectionService.cs:L145 — Method returns constant true/false/0
  
  ```
graphy.X509Certificates.X509Certificate2(filePath);
                    publisher = cert.Subject;
                    return true;
                }
                catch { return false; }
            }

            static string Thr
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AIDetectionService.cs:L147 — Method returns constant true/false/0
  
  ```
publisher = cert.Subject;
                    return true;
                }
                catch { return false; }
            }

            static string ThreatIntelLookup(string sha256, string name, string path)
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L20 — DI interface referenced but not registered
  
  ```
estrator
    {
        private readonly ZeroDayDetectionService _zeroDayDetection;
        private readonly AdvancedAIDetectionService _aiDetection;
        private readonly BehavioralAnalysisService _behavioralAnalysis;
        privat
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L22 — DI interface referenced but not registered
  
  ```
_aiDetection;
        private readonly BehavioralAnalysisService _behavioralAnalysis;
        private readonly ThreatIntelligenceService _threatIntelligence;
        private readonly string _scanResultsPath;
        private readonly Mi
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L29 — DI interface referenced but not registered
  
  ```
;

        public AntivirusOrchestrator(
            ZeroDayDetectionService zeroDayDetection,
            AdvancedAIDetectionService aiDetection,
            BehavioralAnalysisService behavioralAnalysis,
            ThreatIntelligenc
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L31 — DI interface referenced but not registered
  
  ```
AdvancedAIDetectionService aiDetection,
            BehavioralAnalysisService behavioralAnalysis,
            ThreatIntelligenceService threatIntelligence,
            Microsoft.Extensions.Logging.ILogger<AntivirusOrchestrator> logger)
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L48 — Async method has no await
  
  ```
ombine(resultsPath, "scan_history.json");

            _scanHistory = LoadScanHistory();
        }

        public async Task<ComprehensiveScanResult> PerformComprehensiveScanAsync(string filePath)
        {
            var result =
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L109 — Async method has no await
  
  ```
mprehensive"
            };
            SaveScanHistory();

            return result;
        }

        public async Task<ComprehensiveScanResult> PerformProcessScanAsync(Process process)
        {
            var result = new Co
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L168 — Async method has no await
  
  ```
= "Process"
            };
            SaveScanHistory();

            return result;
        }

        public async Task<SystemScanResult> PerformSystemScanAsync()
        {
            var result = new SystemScanResult
  ```

- **Warning** [R007] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L192 — Method returns default or null
  
  ```
return scanResult;
                    }
                    catch
                    {
                        return null;
                    }
                });

                var processResults = await Task.WhenAll(proc
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L221 — Async method has no await
  
  ```
}

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        public async Task<ComprehensiveScanResult> PerformURLScanAsync(string url)
        {
            var result = new Comprehensi
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L320 — Method returns constant true/false/0
  
  ```
/ Threat if any service detects with high confidence
            if (result.ZeroDayResult?.IsPotentialZeroDay == true) return true;
            if (result.AIResult?.IsMalicious == true && result.AIResult.Confidence > 0.7) return true;
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L321 — Method returns constant true/false/0
  
  ```
eroDay == true) return true;
            if (result.AIResult?.IsMalicious == true && result.AIResult.Confidence > 0.7) return true;
            if (result.ThreatIntelResult?.IsMalicious == true) return true;
            if (result.Behavi
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L322 — Method returns constant true/false/0
  
  ```
true && result.AIResult.Confidence > 0.7) return true;
            if (result.ThreatIntelResult?.IsMalicious == true) return true;
            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.Medium) return true;

            // Ov
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L323 — Method returns constant true/false/0
  
  ```
ntelResult?.IsMalicious == true) return true;
            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.Medium) return true;

            // Overall score threshold
            return result.OverallThreatScore > 0.6;
        }
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L331 — Method returns constant true/false/0
  
  ```
ssVerdict(ComprehensiveScanResult result)
        {
            if (result.ZeroDayResult?.IsPotentialZeroDay == true) return true;
            if (result.AIResult?.IsMalicious == true && result.AIResult.Confidence > 0.6) return true;
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L332 — Method returns constant true/false/0
  
  ```
eroDay == true) return true;
            if (result.AIResult?.IsMalicious == true && result.AIResult.Confidence > 0.6) return true;
            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.Medium) return true;

            retur
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\AntivirusOrchestrator.cs:L333 — Method returns constant true/false/0
  
  ```
esult.AIResult.Confidence > 0.6) return true;
            if (result.BehavioralResult?.RiskLevel >= ThreatRisk.Medium) return true;

            return result.OverallThreatScore > 0.5;
        }

        private double CalculateOveral
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L31 — Async method has no await
  
  ```
ehaviorPath, "behavior_db.json");
            _behaviorDatabase = LoadBehaviorDatabase();
        }

        public async Task<BehavioralAnalysisResult> AnalyzeProcessAsync(Process process)
        {
            var result = new Behav
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L100 — Async method has no await
  
  ```
d($"Memory analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<NetworkAnalysis> AnalyzeNetworkActivityAsync(Process process)
        {
            var analysis = new Netw
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L126 — Async method has no await
  
  ```
($"Network analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<FileSystemAnalysis> AnalyzeFileSystemActivityAsync(Process process)
        {
            var analysis = ne
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L160 — Async method has no await
  
  ```
ile system analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<RegistryAnalysis> AnalyzeRegistryActivityAsync(Process process)
        {
            var analysis = new Re
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L194 — Async method has no await
  
  ```
$"Registry analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<ApiAnalysis> AnalyzeApiCallsAsync(Process process)
        {
            var analysis = new ApiAnalysis();
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L278 — Async method has no await
  
  ```
}

            return patterns;
        }

        // Helper methods (simplified implementations)
        private async Task<MemoryInfo> GetMemoryInfoAsync(Process process)
        {
            // Simplified memory analysis
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L294 — Method returns constant true/false/0
  
  ```
rk check
            try
            {
                // This would need actual network monitoring
                return false;
            }
            catch
            {
                return false;
            }
        }
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L298 — Method returns constant true/false/0
  
  ```
ual network monitoring
                return false;
            }
            catch
            {
                return false;
            }
        }

        private int GetConnectionCount(Process process)
        {
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L305 — Method returns constant true/false/0
  
  ```
private int GetConnectionCount(Process process)
        {
            // Simplified connection count
            return 0;
        }

        private List<int> DetectSuspiciousPorts(Process process)
        {
            // Simp
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\BehavioralAnalysisService.cs:L378 — Method only writes to Console
  
  ```
e.WriteAllText(_behaviorDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving behavior database: {ex.Message}");
            }
        }
    }

    // Supportin
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L42 — Async method has no await
  
  ```
ector = new PhishingDetector();
            _malwareDetector = new MalwareSiteDetector();
        }

        public async Task<UrlAnalysisResult> AnalyzeUrlAsync(string url, string browserType, string userId)
        {
            var
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L86 — Async method has no await
  
  ```
Block = true; // Default to blocking on error
            }

            return result;
        }

        public async Task<ContentAnalysisResult> AnalyzePageContentAsync(string url, string content, string browserType, string userId)
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L128 — Async method has no await
  
  ```
;
                result.ShouldBlock = true;
            }

            return result;
        }

        public async Task<DownloadAnalysisResult> AnalyzeDownloadAsync(string url, string filename, long fileSize, string browserType,
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L224 — Async method has no await
  
  ```
;
                result.IsHealthy = false;
            }

            return result;
        }

        private async Task<ContentAnalysis> AnalyzeContentAsync(string content, BrowserProfile profile)
        {
            var anal
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L251 — Async method has no await
  
  ```
analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<ScriptAnalysis> AnalyzeScriptsAsync(string content)
        {
            var analysis = new ScriptAnalysis
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L281 — Async method has no await
  
  ```
analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<FormAnalysis> AnalyzeFormsAsync(string content)
        {
            var analysis = new FormAnalysis();
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L331 — Async method has no await
  
  ```
00 * 1024 * 1024) return RiskLevel.High; // > 500MB

            return RiskLevel.Safe;
        }

        private async Task<RiskLevel> AnalyzeDownloadSourceAsync(string url)
        {
            try
            {
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L487 — Method returns constant true/false/0
  
  ```
UrlAnalysisResult result, BrowserProfile profile)
        {
            if (result.OverallRisk >= RiskLevel.Critical) return true;
            if (result.OverallRisk >= RiskLevel.High && profile.Settings.BlockHighRiskSites) return true;
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L488 — Method returns constant true/false/0
  
  ```
el.Critical) return true;
            if (result.OverallRisk >= RiskLevel.High && profile.Settings.BlockHighRiskSites) return true;
            if (result.PhishingResult.RiskLevel >= RiskLevel.Medium) return true;
            if (result.
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L489 — Method returns constant true/false/0
  
  ```
profile.Settings.BlockHighRiskSites) return true;
            if (result.PhishingResult.RiskLevel >= RiskLevel.Medium) return true;
            if (result.MalwareResult.RiskLevel >= RiskLevel.Medium) return true;

            return fal
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L490 — Method returns constant true/false/0
  
  ```
Result.RiskLevel >= RiskLevel.Medium) return true;
            if (result.MalwareResult.RiskLevel >= RiskLevel.Medium) return true;

            return false;
        }

        private bool ShouldBlockContent(ContentAnalysisResult re
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L492 — Method returns constant true/false/0
  
  ```
l.Medium) return true;
            if (result.MalwareResult.RiskLevel >= RiskLevel.Medium) return true;

            return false;
        }

        private bool ShouldBlockContent(ContentAnalysisResult result, BrowserProfile profile
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L497 — Method returns constant true/false/0
  
  ```
entAnalysisResult result, BrowserProfile profile)
        {
            if (result.OverallRisk >= RiskLevel.Critical) return true;
            if (result.ScriptAnalysis.RiskLevel >= RiskLevel.High) return true;
            if (result.Co
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L498 — Method returns constant true/false/0
  
  ```
ult.OverallRisk >= RiskLevel.Critical) return true;
            if (result.ScriptAnalysis.RiskLevel >= RiskLevel.High) return true;
            if (result.ContentAnalysis.InappropriateContent.Count > 0 && profile.Settings.StrictContentFil
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L499 — Method returns constant true/false/0
  
  ```
n true;
            if (result.ContentAnalysis.InappropriateContent.Count > 0 && profile.Settings.StrictContentFilter) return true;

            return false;
        }

        private bool ShouldBlockDownload(DownloadAnalysisResult
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L501 — Method returns constant true/false/0
  
  ```
ult.ContentAnalysis.InappropriateContent.Count > 0 && profile.Settings.StrictContentFilter) return true;

            return false;
        }

        private bool ShouldBlockDownload(DownloadAnalysisResult result, BrowserProfile profi
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L506 — Method returns constant true/false/0
  
  ```
oadAnalysisResult result, BrowserProfile profile)
        {
            if (result.OverallRisk >= RiskLevel.Critical) return true;
            if (result.ExtensionRisk >= RiskLevel.High && profile.Settings.BlockExecutableDownloads) retur
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L507 — Method returns constant true/false/0
  
  ```
cal) return true;
            if (result.ExtensionRisk >= RiskLevel.High && profile.Settings.BlockExecutableDownloads) return true;
            if (result.SourceRisk >= RiskLevel.High) return true;

            return false;
        }
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L508 — Method returns constant true/false/0
  
  ```
l.High && profile.Settings.BlockExecutableDownloads) return true;
            if (result.SourceRisk >= RiskLevel.High) return true;

            return false;
        }

        private List<string> GenerateUrlRecommendations(UrlAnaly
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L510 — Method returns constant true/false/0
  
  ```
lockExecutableDownloads) return true;
            if (result.SourceRisk >= RiskLevel.High) return true;

            return false;
        }

        private List<string> GenerateUrlRecommendations(UrlAnalysisResult result)
        {
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L670 — Method only writes to Console
  
  ```
.WriteAllText(_extensionDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving browser profiles: {ex.Message}");
            }
        }

        private Task<boo
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L732 — Method returns constant true/false/0
  
  ```
Folder.LocalApplicationData), "PcFutureShield", "BrowserExtension");
            if (!Directory.Exists(extensionPath)) return false;

            // Check if we can read/write to the extension directory
            try
            {
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L740 — Method returns constant true/false/0
  
  ```
await File.WriteAllTextAsync(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L744 — Method returns constant true/false/0
  
  ```
File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    // Supporting classes for Browser Extension
    public class UrlA
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L899 — Async method has no await
  
  ```
ed,
        MalwareDetected
    }

    // Internal engines
    public class WebFilterEngine
    {
        public async Task<WebFilterResult> CheckUrlAsync(string url, BrowserProfile profile)
        {
            var result = new W
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L932 — Async method has no await
  
  ```
}

            return result;
        }
    }

    public class PhishingDetector
    {
        public async Task<PhishingAnalysisResult> AnalyzeUrlAsync(string url)
        {
            var result = new PhishingAnalysis
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\BrowserExtensionService.cs:L968 — Async method has no await
  
  ```
}

            return result;
        }
    }

    public class MalwareSiteDetector
    {
        public async Task<MalwareAnalysisResult> CheckMalwareAsync(string url)
        {
            var result = new MalwareAnalysis
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L44 — Async method has no await
  
  ```
dEngine = new FraudDetectionEngine();
            _casinoAnalyzer = new CasinoAnalyzer();
        }

        public async Task<GamingAnalysisResult> AnalyzeGameAsync(string gameName, string gamePath, Process? gameProcess = null)
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L87 — Async method has no await
  
  ```
kLevel.High; // Default to high risk on error
            }

            return result;
        }

        public async Task<CasinoAnalysisResult> AnalyzeCasinoAsync(string casinoUrl, string casinoName)
        {
            var res
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L164 — Async method has no await
  
  ```
ReportedBy = Environment.UserName
            });
            SaveCasinoProfiles();
        }

        private async Task<ExecutableAnalysis> AnalyzeGameExecutableAsync(string gamePath)
        {
            var analysis = new Ex
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L202 — Async method has no await
  
  ```
tors.Add($"Analysis error: {ex.Message}");
            }

            return analysis;
        }

        private async Task<ModificationCheck> CheckGameModificationsAsync(string gamePath, GameProfile profile)
        {
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L238 — Async method has no await
  
  ```
{
                check.Error = ex.Message;
            }

            return check;
        }

        private async Task<GameNetworkAnalysis> AnalyzeGameNetworkAsync(Process gameProcess)
        {
            var analysis = new
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L273 — Async method has no await
  
  ```
analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<GamblingDetection> DetectGamblingElementsAsync(string gamePath, string gameName)
        {
            var
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L346 — Async method has no await
  
  ```
analysis.IsAccessible = false;
            }

            return analysis;
        }

        private async Task<LicensingCheck> CheckCasinoLicensingAsync(string casinoUrl)
        {
            var check = new Licensing
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L401 — Async method has no await
  
  ```
{
                check.Error = ex.Message;
            }

            return check;
        }

        private async Task<FairnessAnalysis> AnalyzeGameFairnessAsync(string casinoUrl, CasinoProfile profile)
        {
            v
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L428 — Async method has no await
  
  ```
analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<ScamDetection> DetectCasinoScamsAsync(string casinoUrl, CasinoProfile profile)
        {
            var de
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L458 — Async method has no await
  
  ```
detection.Error = ex.Message;
            }

            return detection;
        }

        private async Task<FinancialRisk> AssessFinancialRiskAsync(CasinoProfile profile)
        {
            var risk = new Financi
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L489 — Async method has no await
  
  ```
{
                risk.Error = ex.Message;
            }

            return risk;
        }

        private async Task<List<string>> CheckForMalwarePatternsAsync(string gamePath)
        {
            // This would integrate w
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L495 — Async method has no await
  
  ```
// This would integrate with the antivirus engine
            return new List<string>();
        }

        private async Task<List<string>> CheckForKnownCheatsAsync(string gamePath, string gameName)
        {
            // This woul
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L651 — Method only writes to Console
  
  ```
bPath)!, "game_profiles.json"), json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving game profiles: {ex.Message}");
            }
        }

        private void SaveCa
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L664 — Method only writes to Console
  
  ```
ath)!, "casino_profiles.json"), json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving casino profiles: {ex.Message}");
            }
        }
    }

    // Supporting
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L828 — Async method has no await
  
  ```
string ReportedBy { get; set; } = string.Empty;
    }

    public class FraudDetectionEngine
    {
        public async Task<FraudAlertResult> AnalyzeTransactionAsync(string transactionData, decimal amount, string gameOrCasino)
  ```

- **Warning** [R004] .\PcFutureShield.Common\Services\GamingProtectionService.cs:L844 — simulate/stub/fake/mock/placeholder
  
  ```
ion history analysis

                // Check for known scam patterns
                if (transactionData.Contains("fake") || transactionData.Contains("test"))
                    fraudIndicators.Add("Suspicious transaction data");
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\HashingService.cs:L66 — Method returns constant true/false/0
  
  ```
using var fs = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            if (fs.Length < 2) return false;
            fs.Read(hdr);
            return hdr[0] == (byte)'M' && hdr[1] == (byte)'Z';
        }
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L41 — Async method has no await
  
  ```
serProfiles = LoadUserProfiles();
            _behaviorAnalyzer = new BehaviorAnalyzer();
        }

        public async Task<ParentalAnalysisResult> AnalyzeContentAsync(string content, string url, UserProfile userProfile)
        {
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L112 — Async method has no await
  
  ```
erProfiles.TryGetValue(userId, out var profile) ? profile : CreateDefaultProfile(userId);
        }

        private async Task<RiskLevel> AnalyzePredatorContentAsync(string content, string url)
        {
            var riskScore = 0.
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L141 — Async method has no await
  
  ```
)
                riskScore += 0.2;

            return DetermineRiskLevel(riskScore);
        }

        private async Task<RiskLevel> AnalyzeInappropriateContentAsync(string content)
        {
            var riskScore = 0.0;
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L165 — Async method has no await
  
  ```
+
                riskScore += 0.2;

            return DetermineRiskLevel(riskScore);
        }

        private async Task<RiskLevel> AnalyzeViolenceContentAsync(string content)
        {
            var riskScore = 0.0;
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L184 — Async method has no await
  
  ```
riskScore += 0.3;
            }

            return DetermineRiskLevel(riskScore);
        }

        private async Task<RiskLevel> AnalyzeBullyingContentAsync(string content)
        {
            var riskScore = 0.0;
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L206 — Async method has no await
  
  ```
riskScore += 0.4;
            }

            return DetermineRiskLevel(riskScore);
        }

        private async Task<RiskLevel> AnalyzeSuicideIndicatorsAsync(string content)
        {
            var riskScore = 0.0;
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ParentalControlService.cs:L255 — Method returns constant true/false/0
  
  ```
file profile)
        {
            // Always block critical content
            if (result.OverallRiskScore >= 0.8) return true;

            // Check user profile restrictions
            if (profile.Age < 13 && result.OverallRiskSc
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ParentalControlService.cs:L258 — Method returns constant true/false/0
  
  ```
// Check user profile restrictions
            if (profile.Age < 13 && result.OverallRiskScore >= 0.3) return true;
            if (profile.Age < 16 && result.OverallRiskScore >= 0.5) return true;

            // Block b
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ParentalControlService.cs:L259 — Method returns constant true/false/0
  
  ```
13 && result.OverallRiskScore >= 0.3) return true;
            if (profile.Age < 16 && result.OverallRiskScore >= 0.5) return true;

            // Block based on specific risk types
            if (result.SuicideRisk >= RiskLevel.High)
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ParentalControlService.cs:L262 — Method returns constant true/false/0
  
  ```
eturn true;

            // Block based on specific risk types
            if (result.SuicideRisk >= RiskLevel.High) return true;
            if (result.PredatorRisk >= RiskLevel.Medium) return true;

            return false;
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ParentalControlService.cs:L263 — Method returns constant true/false/0
  
  ```
if (result.SuicideRisk >= RiskLevel.High) return true;
            if (result.PredatorRisk >= RiskLevel.Medium) return true;

            return false;
        }

        private List<string> GenerateParentalAlerts(ParentalAnal
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ParentalControlService.cs:L265 — Method returns constant true/false/0
  
  ```
k >= RiskLevel.High) return true;
            if (result.PredatorRisk >= RiskLevel.Medium) return true;

            return false;
        }

        private List<string> GenerateParentalAlerts(ParentalAnalysisResult result)
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ParentalControlService.cs:L350 — Method only writes to Console
  
  ```
e.WriteAllText(_parentalDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving user profiles: {ex.Message}");
            }
        }

        private void SaveCo
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ParentalControlService.cs:L363 — Method only writes to Console
  
  ```
ath)!, "content_filters.json"), json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving content filters: {ex.Message}");
            }
        }
    }

    // Supporting
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L430 — Async method has no await
  
  ```
r
    {
        private readonly Dictionary<string, UserBehaviorPattern> _behaviorPatterns = new();

        public async Task UpdateBehaviorPatternsAsync(UserProfile profile, ParentalAnalysisResult result)
        {
            if (!
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ParentalControlService.cs:L446 — Async method has no await
  
  ```
+;
            if (result.PredatorRisk >= RiskLevel.Medium) pattern.PredatorEncounters++;
        }

        public async Task<BehaviorAnalysisResult> AnalyzeBehaviorAsync(UserProfile profile, List<string> recentActivities)
        {
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L9 — DI interface referenced but not registered
  
  ```
neric;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Management;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using Microsoft.Win32;
using System.Threading;

namespace PcFutureSh
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L76 — Async method has no await
  
  ```
report.OverallHealthScore = 0;
            }

            return report;
        }

        public async Task<OptimizationResult> PerformDeepOptimizationAsync(OptimizationOptions options)
        {
            var res
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L137 — Async method has no await
  
  ```
result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<RepairResult> PerformSystemRepairAsync(RepairOptions options)
        {
            var result = new Repair
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L192 — Async method has no await
  
  ```
result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<PerformanceBoostResult> BoostPerformanceAsync()
        {
            var result = new PerformanceBoostResu
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L237 — Async method has no await
  
  ```
result.EndTime = DateTime.UtcNow;
            }

            return result;
        }

        public async Task<UninstallResult> PerformSmartUninstallAsync(List<string> programsToRemove)
        {
            var result
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L607 — Async method has no await
  
  ```
ew();
    }

    // System analysis and optimization engines
    public class SystemAnalyzer
    {
        public async Task<SystemInformation> GetSystemInformationAsync()
        {
            var info = new SystemInformation();
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L626 — Method only writes to Console
  
  ```
on ex)
            {
                // Log to console as fallback; callers should handle exceptions
                Console.WriteLine($"Error getting system information: {ex.Message}");
            }

            return info;
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L682 — Async method has no await
  
  ```
);
                health.HealthScore = 0.5;
            }

            return health;
        }

        public async Task<MemoryHealth> AnalyzeMemoryHealthAsync()
        {
            var health = new MemoryHealth();
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L707 — Async method has no await
  
  ```
);
                health.HealthScore = 0.5;
            }

            return health;
        }

        public async Task<DiskHealth> AnalyzeDiskHealthAsync()
        {
            var health = new DiskHealth();

            tr
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L772 — Method only writes to Console
  
  ```
metrics.Timestamp = DateTime.UtcNow;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting performance metrics: {ex.Message}");
            }

            return metrics;
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1048 — Async method has no await
  
  ```
result.Success = false;
            }

            return Task.FromResult(result);
        }

        public async Task<DefragResult> DefragmentDrivesAsync()
        {
            var result = new DefragResult();
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1135 — Async method has no await
  
  ```
result.Success = false;
            }

            return Task.FromResult(result);
        }

        public async Task<RegistryResult> CleanRegistryAsync()
        {
            var result = new RegistryResult();
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1294 — Async method has no await
  
  ```
ge);
                result.Success = false;
            }

            return result;
        }

        public async Task<NetworkResult> OptimizeNetworkAsync()
        {
            var result = new NetworkResult();
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1316 — Async method has no await
  
  ```
ge);
                result.Success = false;
            }

            return result;
        }

        public async Task<UninstallOperation> UninstallProgramAsync(string programName)
        {
            var result = new Uninst
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1440 — Method returns constant true/false/0
  
  ```
too new (might be in use)
                if (fileInfo.LastWriteTime > DateTime.Now.AddHours(-1))
                    return false;

                // Don't delete system files
                var systemFiles = new[] { "desktop.ini",
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1445 — Method returns constant true/false/0
  
  ```
.ini", "thumbs.db" };
                if (systemFiles.Contains(fileInfo.Name.ToLowerInvariant()))
                    return false;

                // Only delete certain file types
                var safeExtensions = new[] { ".tmp",
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1454 — Method returns constant true/false/0
  
  ```
ns.Contains(fileInfo.Extension.ToLowerInvariant());

            }
            catch
            {
                return false;
            }
        }

        private IEnumerable<string> GetInstalledPrograms()
        {
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1512 — Async method has no await
  
  ```
rivate static extern int EmptyWorkingSet(IntPtr hwProc);
    }

    public class RepairEngine
    {
        public async Task<RepairOperation> RepairSystemFilesAsync()
        {
            var result = new RepairOperation { Operatio
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1545 — Async method has no await
  
  ```
ge);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> FixWindowsCorruptionAsync()
        {
            var result = new RepairOperation { Opera
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1578 — Async method has no await
  
  ```
ge);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> RepairBootIssuesAsync()
        {
            var result = new RepairOperation { Operation
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1615 — Async method has no await
  
  ```
ge);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> RepairDriversAsync()
        {
            var result = new RepairOperation { Operation =
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\PcOptimizationService.cs:L1642 — Async method has no await
  
  ```
ge);
                result.Success = false;
            }

            return result;
        }

        public async Task<RepairOperation> RepairNetworkAsync()
        {
            var result = new RepairOperation { Operation =
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L17 — DI interface referenced but not registered
  
  ```
y>
    /// Advanced threat intelligence service with multiple threat feeds
    /// </summary>
    public class ThreatIntelligenceService
    {
        private readonly HttpClient _httpClient;
        private readonly string _threatDbP
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L24 — DI interface referenced but not registered
  
  ```
g, ThreatIndicator> _threatIndicators;
        private readonly List<ThreatFeed> _threatFeeds;

        public ThreatIntelligenceService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpa
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L38 — Async method has no await
  
  ```
tIndicators = LoadThreatIndicators();
            _threatFeeds = InitializeThreatFeeds();
        }

        public async Task<ThreatIntelligenceResult> CheckHashAsync(string hash)
        {
            var result = new ThreatIntellig
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L90 — Method only writes to Console
  
  ```
}
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error checking feed {feed.Name}: {ex.Message}");
                        }
                    }
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L106 — Async method has no await
  
  ```
{
                result.Error = ex.Message;
            }

            return result;
        }

        public async Task<ThreatIntelligenceResult> CheckDomainAsync(string domain)
        {
            var result = new ThreatInte
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L130 — Method only writes to Console
  
  ```
}
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error checking domain feed {feed.Name}: {ex.Message}");
                    }
                }
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L145 — Async method has no await
  
  ```
{
                result.Error = ex.Message;
            }

            return result;
        }

        public async Task<ThreatIntelligenceResult> CheckIPAddressAsync(string ipAddress)
        {
            var result = new Thre
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L169 — Method only writes to Console
  
  ```
}
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error checking IP feed {feed.Name}: {ex.Message}");
                    }
                }
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L186 — Method only writes to Console
  
  ```
return result;
        }

        public async Task UpdateThreatFeedsAsync()
        {
            Console.WriteLine("Updating threat intelligence feeds...");

            foreach (var feed in _threatFeeds)
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L200 — Method only writes to Console
  
  ```
}
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error updating feed {feed.Name}: {ex.Message}");
                }
            }

            Sa
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L205 — Method only writes to Console
  
  ```
feed {feed.Name}: {ex.Message}");
                }
            }

            SaveThreatIndicators();
            Console.WriteLine($"Updated {_threatIndicators.Count} threat indicators");
        }

        public ThreatStatistics
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L268 — Async method has no await
  
  ```
alse,
                    ApiKey = null // Public API
                }
            };
        }

        private async Task<ThreatIntelligenceResult> CheckFeedAsync(ThreatFeed feed, string hash)
        {
            var result = n
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L291 — Async method has no await
  
  ```
licious = false;
                    break;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckDomainFeedAsync(ThreatFeed feed, string domain)
        {
            var re
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L311 — Async method has no await
  
  ```
licious = false;
                    break;
            }

            return result;
        }

        private async Task<ThreatIntelligenceResult> CheckIPFeedAsync(ThreatFeed feed, string ipAddress)
        {
            var res
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L332 — Async method has no await
  
  ```
}

            return result;
        }

        // Simplified feed checking implementations
        private async Task<ThreatIntelligenceResult> CheckMalwareBazaarAsync(string hash)
        {
            // Simplified implemen
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L343 — Async method has no await
  
  ```
alse, // Would check actual API
                Source = "MalwareBazaar"
            };
        }

        private async Task<ThreatIntelligenceResult> CheckVirusTotalAsync(string hash)
        {
            // Simplified implementat
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L354 — Async method has no await
  
  ```
= false, // Would check actual API
                Source = "VirusTotal"
            };
        }

        private async Task<ThreatIntelligenceResult> CheckVirusTotalDomainAsync(string domain)
        {
            return new Threat
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L364 — Async method has no await
  
  ```
IsMalicious = false,
                Source = "VirusTotal"
            };
        }

        private async Task<ThreatIntelligenceResult> CheckPhishTankAsync(string domain)
        {
            return new ThreatIntelli
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L374 — Async method has no await
  
  ```
IsMalicious = false,
                Source = "PhishTank"
            };
        }

        private async Task<ThreatIntelligenceResult> CheckAbuseIPDBAsync(string ipAddress)
        {
            return new ThreatInte
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L384 — Async method has no await
  
  ```
IsMalicious = false,
                Source = "AbuseIPDB"
            };
        }

        private async Task<ThreatIntelligenceResult> CheckVirusTotalIPAsync(string ipAddress)
        {
            return new ThreatI
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L394 — Async method has no await
  
  ```
IsMalicious = false,
                Source = "VirusTotal"
            };
        }

        private async Task<List<ThreatIndicator>> DownloadFeedAsync(ThreatFeed feed)
        {
            // Simplified implementatio
  ```

- **Info** [R012] .\PcFutureShield.Common\Services\ThreatIntelligenceService.cs:L429 — Method only writes to Console
  
  ```
ile.WriteAllText(_threatDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving threat indicators: {ex.Message}");
            }
        }

        private DateTim
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L21 — DI interface referenced but not registered
  
  ```
Service
    {
        private readonly BehavioralAnalysisService _behavioralAnalysis;
        private readonly ThreatIntelligenceService _threatIntelligence;
        private readonly string _zeroDayDbPath;
        private readonly Dict
  ```

- **Warning** [R031] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L27 — DI interface referenced but not registered
  
  ```
public ZeroDayDetectionService(
            BehavioralAnalysisService behavioralAnalysis,
            ThreatIntelligenceService threatIntelligence)
        {
            _behavioralAnalysis = behavioralAnalysis;
            _
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L40 — Async method has no await
  
  ```
yPath, "zero_day_patterns.json");

            _zeroDayPatterns = LoadZeroDayPatterns();
        }

        public async Task<ZeroDayAnalysisResult> AnalyzeFileAsync(string filePath)
        {
            var result = new ZeroDayAnal
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L74 — Async method has no await
  
  ```
}

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        public async Task<ZeroDayAnalysisResult> AnalyzeProcessAsync(Process process)
        {
            var result = new ZeroDayA
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L125 — Async method has no await
  
  ```
}

            result.EndTime = DateTime.UtcNow;
            return result;
        }

        private async Task<AnomalyDetectionResult> DetectAnomaliesAsync(string filePath)
        {
            var result = new Anomal
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L167 — Async method has no await
  
  ```
d($"Anomaly detection error: {ex.Message}");
            }

            return result;
        }

        private async Task<CodeAnalysisResult> AnalyzeCodePatternsAsync(string filePath)
        {
            var result = new CodeAn
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L249 — Async method has no await
  
  ```
result.Error = ex.Message;
            }

            return result;
        }

        private async Task<SignatureEvasionResult> DetectSignatureEvasionAsync(string filePath)
        {
            var result = new
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L318 — Async method has no await
  
  ```
result.Error = ex.Message;
            }

            return result;
        }

        private async Task<MemoryAnalysisResult> AnalyzeProcessMemoryAsync(Process process)
        {
            var result = new Mem
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L342 — Async method has no await
  
  ```
result.Error = ex.Message;
            }

            return result;
        }

        private async Task<NetworkAnalysisResult> AnalyzeProcessNetworkAsync(Process process)
        {
            var result = new N
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L430 — Method returns constant true/false/0
  
  ```
{
            // Simple junk code detection - look for repetitive patterns
            if (content.Length < 100) return false;

            int consecutiveSame = 0;
            for (int i = 1; i < content.Length; i++)
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L439 — Method returns constant true/false/0
  
  ```
eSame++;
                    if (consecutiveSame > 50) // More than 50 consecutive same bytes
                        return true;
                }
                else
                {
                    consecutiveSame = 0;
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L447 — Method returns constant true/false/0
  
  ```
else
                {
                    consecutiveSame = 0;
                }
            }

            return false;
        }

        private bool DetectCodeObfuscation(byte[] content)
        {
            // Check
  ```

- **Warning** [R009] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L476 — Async method has no await
  
  ```
var entropy = CalculateByteEntropy(content);
            return entropy > 7.5;
        }

        private async Task<bool> IsFileSignedAsync(string filePath)
        {
            try
            {
                var cert
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L481 — Method returns constant true/false/0
  
  ```
var cert = new System.Security.Cryptography.X509Certificates.X509Certificate2(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L485 — Method returns constant true/false/0
  
  ```
Certificate2(filePath);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private double CalculateByteEntropy(byte[] data)
        {
  ```

- **Warning** [R011] .\PcFutureShield.Common\Services\ZeroDayDetectionService.cs:L491 — Method returns constant true/false/0
  
  ```
}
        }

        private double CalculateByteEntropy(byte[] data)
        {
            if (data.Length == 0) return 0;

            var frequencies = new int[256];
            foreach (var b in data) frequencies[b]++;
  ```

- **Warning** [R031] .\PcFutureShield.Engine\Scanning\IScannerService.cs:L10 — DI interface referenced but not registered
  
  ```
// Service for scanning files, directories, and processes for potential threats
    /// </summary>
    public interface IScannerService
    {
        /// <summary>
        /// Scans a single file for threats
        /// </summary>
        T
  ```

- **Warning** [R031] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L8 — DI interface referenced but not registered
  
  ```
t;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Threading;
using System.Threading.Tasks;
using Microso
  ```

- **Warning** [R031] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L17 — DI interface referenced but not registered
  
  ```
Microsoft.Extensions.DependencyInjection;

namespace PcFutureShield.Engine.Scanning
{
    public class ScannerService : IScannerService, IDisposable, IHostedService
    {
        private const int MaxConcurrentScans = 8;
        private con
  ```

- **Warning** [R031] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L17 — DI interface referenced but not registered
  
  ```
yInjection;

namespace PcFutureShield.Engine.Scanning
{
    public class ScannerService : IScannerService, IDisposable, IHostedService
    {
        private const int MaxConcurrentScans = 8;
        private const int MaxFileSizeBytes = 200
  ```

- **Warning** [R031] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L37 — DI interface referenced but not registered
  
  ```
_serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        // IHostedService implementation is provided at the end of the file

        public async Task<ScanResult> ScanFileAsync(st
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L39 — Async method has no await
  
  ```
rviceProvider));
        }

        // IHostedService implementation is provided at the end of the file

        public async Task<ScanResult> ScanFileAsync(string filePath, CancellationToken ct = default)
        {
            var result =
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L208 — Async method has no await
  
  ```
oseAsync();
                }
                result.EndTime = DateTime.UtcNow;
            }
        }

        public async Task<ScanResult> ScanDirectoryAsync(string directoryPath, bool recursive = true, CancellationToken ct = default)
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L295 — Async method has no await
  
  ```
or scanning directory: {DirectoryPath}", directoryPath);
                throw;
            }
        }

        public async Task<ScanResult> ScanProcessAsync(int processId, CancellationToken ct = default)
        {
            var result
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L467 — Async method has no await
  
  ```
ask.FromResult(((bool, string?))(false, $"Verification error: {ex.Message}"));
            }
        }

        private async Task<List<ThreatDetection>> ScanProcessMemoryAsync(Process process, CancellationToken ct)
        {
            va
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L602 — Async method has no await
  
  ```
alAs(UnmanagedType.Bool)]
            public static extern bool CloseHandle(IntPtr hObject);
        }

        private async Task<List<MemoryRegionInfo>> FindSuspiciousMemoryRegionsAsync(Process process, CancellationToken ct)
        {
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L686 — Method returns constant true/false/0
  
  ```
ContainsShellcodePatterns(byte[] buffer)
        {
            if (buffer == null || buffer.Length < 4)
                return false;

            // Common shellcode patterns (simplified for example)
            var patterns = new (byte[]
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L704 — Method returns constant true/false/0
  
  ```
if (++consecutiveNops > 10) // More than 10 consecutive NOPs is suspicious
                        return true;
                }
                else
                {
                    consecutiveNops = 0;
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L731 — Method returns constant true/false/0
  
  ```
break;
                        }
                    }
                    if (match)
                        return true;
                }
            }

            return false;
        }

        private async Task<List<Threa
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L735 — Method returns constant true/false/0
  
  ```
}
                    if (match)
                        return true;
                }
            }

            return false;
        }

        private async Task<List<ThreatDetection>> ScanForMemoryPatternsAsync(Process process, C
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L738 — Async method has no await
  
  ```
return true;
                }
            }

            return false;
        }

        private async Task<List<ThreatDetection>> ScanForMemoryPatternsAsync(Process process, CancellationToken ct)
        {
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1002 — Async method has no await
  
  ```
verity Severity { get; set; }
            public string Reason { get; set; } = string.Empty;
        }

        private async Task<List<ThreatDetection>> ScanProcessHandlesAsync(Process process, CancellationToken ct)
        {
            v
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1063 — Async method has no await
  
  ```
ing handles of process {ProcessId}", process.Id);
            }

            return threats;
        }

        private async Task<HeuristicAnalysisResult> PerformHeuristicAnalysisAsync(string filePath, CancellationToken ct)
        {
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1190 — Method returns constant true/false/0
  
  ```
uffer[i]]++;
                }
                totalBytes += bytesRead;
            }

            if (totalBytes == 0) return 0;

            for (var i = 0; i < 256; i++)
            {
                if (frequencies[i] > 0)
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1213 — Async method has no await
  
  ```
ic IntPtr HandleValue { get; set; }
            public ThreatSeverity Severity { get; set; }
        }

        private async Task<List<HandleInfo>> EnumerateProcessHandlesAsync(int processId, CancellationToken ct)
        {
            var
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1270 — Method returns constant true/false/0
  
  ```
sensitiveProcesses.Any(p => handle.Name.Contains(p, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // Check for handles to sensitive files/registry
            var sensitivePaths
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1277 — Method returns constant true/false/0
  
  ```
if (sensitivePaths.Any(p => handle.Name.Contains(p, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // Check for high-privilege access
            if (handle.Access.Contains("ALL_A
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1284 — Method returns constant true/false/0
  
  ```
||
                handle.Access.Contains("DELETE") || handle.Access.Contains("MODIFY"))
            {
                return true;
            }

            return false;
        }

        private ThreatSeverity DetermineHandleSeverity(
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1287 — Method returns constant true/false/0
  
  ```
ns("DELETE") || handle.Access.Contains("MODIFY"))
            {
                return true;
            }

            return false;
        }

        private ThreatSeverity DetermineHandleSeverity(string type, string access, string name)
  ```

- **Warning** [R007] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1361 — Method returns default or null
  
  ```
shal.AllocHGlobal((int)bufferSize);
                    }

                    if (status != 0)
                        return null;

                    // Parse the SYSTEM_HANDLE_INFORMATION_EX structure
                    var handleCoun
  ```

- **Warning** [R007] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1389 — Method returns default or null
  
  ```
tion ex)
            {
                _logger.LogError(ex, "Error getting system handle information");
                return null;
            }

            return handles;
        }

        private string GetHandleTypeName(ushort objec
  ```

- **Warning** [R031] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1597 — DI interface referenced but not registered
  
  ```
itHandle,
            uint dwOptions);

        private const uint DUPLICATE_SAME_ACCESS = 0x00000002;

        #region IHostedService Implementation

        public async Task StartAsync(CancellationToken cancellationToken)
        {
  ```

- **Warning** [R032] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1599 — Async method defined but never awaited or invoked
  
  ```
private const uint DUPLICATE_SAME_ACCESS = 0x00000002;

        #region IHostedService Implementation

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Scanner Service
  ```

- **Warning** [R005] .\PcFutureShield.Engine\Scanning\ScannerService.cs:L1619 — Async method returns Task.CompletedTask
  
  ```
ignore
            }
            Dispose();
            _logger.LogInformation("Scanner Service stopped");
            return Task.CompletedTask;
        }

        #endregion

        #region IDisposable Implementation

        public voi
  ```

- **Warning** [R009] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L36 — Async method has no await
  
  ```
directory))
            {
                Directory.CreateDirectory(directory);
            }
        }

        public async Task<bool> InitializeAsync(CancellationToken ct = default)
        {
            try
            {
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L44 — Method returns constant true/false/0
  
  ```
await _connection.OpenAsync(ct);

                await CreateTablesIfNotExistAsync(ct);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed t
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L49 — Method returns constant true/false/0
  
  ```
tion ex)
            {
                _logger.LogError(ex, "Failed to initialize signature database");
                return false;
            }
        }

        public async Task<bool> UpdateAsync(CancellationToken ct = default)
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L74 — Method returns constant true/false/0
  
  ```
_logger.LogInformation("Updated {Count} signatures", signatures.Count);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed t
  ```

- **Warning** [R011] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L79 — Method returns constant true/false/0
  
  ```
xception ex)
            {
                _logger.LogError(ex, "Failed to update signature database");
                return false;
            }
        }

        public async Task<ThreatSignature?> LookupSignatureAsync(string signature
  ```

- **Warning** [R007] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L108 — Method returns default or null
  
  ```
Severity = (ThreatSeverity)reader.GetInt32(2)
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error lo
  ```

- **Warning** [R007] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L113 — Method returns default or null
  
  ```
catch (Exception ex)
            {
                _logger.LogError(ex, "Error looking up signature");
                return null;
            }
        }

        private async Task CreateTablesIfNotExistAsync(CancellationToken ct)
  ```

- **Warning** [R032] .\PcFutureShield.Engine\Scanning\SqliteSignatureDatabase.cs:L117 — Async method defined but never awaited or invoked
  
  ```
ogger.LogError(ex, "Error looking up signature");
                return null;
            }
        }

        private async Task CreateTablesIfNotExistAsync(CancellationToken ct)
        {
            if (_connection == null) return;
  ```

- **Warning** [R007] .\PcFutureShield.Engine\VirusScanner\SignatureDatabase.cs:L88 — Method returns default or null
  
  ```
tring hash, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(hash)) return null;
            hash = hash.ToLowerInvariant();
            await using var conn = new SqliteConnection($"Dat
  ```

- **Warning** [R031] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L13 — DI interface referenced but not registered
  
  ```
sing System.Threading.Tasks;

namespace PcFutureShield.Engine.VirusScanner
{
    public class VirusScannerService : IVirusScannerService, IDisposable
    {
        private readonly ILogger<VirusScannerService> _logger;
        privat
  ```

- **Warning** [R009] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L110 — Async method has no await
  
  ```
atch.Stop();
            result.Duration = stopwatch.Elapsed;
            return result;
        }

        public async Task<IReadOnlyList<QuarantineItem>> GetQuarantineAsync(CancellationToken cancellationToken = default)
        {
  ```

- **Warning** [R009] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L139 — Async method has no await
  
  ```
IsQuarantined = true
                });
            }
            return items;
        }

        public async Task<bool> RemoveFromQuarantineAsync(string quarantineId, CancellationToken cancellationToken = default)
  ```

- **Warning** [R011] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L143 — Method returns constant true/false/0
  
  ```
Domain.BaseDirectory, "Quarantine", "quarantine.db");
            if (!File.Exists(quarantineDbPath))
                return false;

            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={quarantineDbPath
  ```

- **Warning** [R009] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L179 — Async method has no await
  
  ```
n if file deletion fails
                }
            }

            return rows > 0;
        }

        public async Task<bool> RestoreFromQuarantineAsync(string quarantineId, string? targetPath = null, CancellationToken cancellati
  ```

- **Warning** [R011] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L183 — Method returns constant true/false/0
  
  ```
Domain.BaseDirectory, "Quarantine", "quarantine.db");
            if (!File.Exists(quarantineDbPath))
                return false;

            using var conn = new Microsoft.Data.Sqlite.SqliteConnection($"Data Source={quarantineDbPath
  ```

- **Warning** [R011] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L195 — Method returns constant true/false/0
  
  ```
md.ExecuteReaderAsync(cancellationToken);
            if (!await reader.ReadAsync(cancellationToken))
                return false;

            var originalPath = reader.GetString(0);
            var quarantinePath = reader.GetString(
  ```

- **Warning** [R007] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L284 — Method returns default or null
  
  ```
etectedAt = DateTime.UtcNow,
                    IsQuarantined = false
                };
            }
            return null;
        }

        private async Task<string?> GetThreatNameFromSignatureDbAsync(string hash, Cancellati
  ```

- **Warning** [R007] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L297 — Method returns default or null
  
  ```
and log
                _logger.LogWarning("Signature DB not configured; skipping signature lookup");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erro
  ```

- **Warning** [R007] .\PcFutureShield.Engine\VirusScanner\VirusScannerService.cs:L302 — Method returns default or null
  
  ```
ex)
            {
                _logger.LogError(ex, "Error querying threat signature database");
                return null;
            }
        }
        #region IDisposable Implementation

        public void Dispose()
  ```

- **Warning** [R009] .\PcFutureShield.ExtensionHost\Hubs\DetectionHub.cs:L18 — Async method has no await
  
  ```
/// Server will re-broadcast to all connected clients as ReceiveDetection.
        /// </summary>
        public async Task PublishDetection(DetectionEvent detection)
        {
            _logger.LogInformation("PublishDetection f
  ```

- **Warning** [R009] .\PcFutureShield.RealtimeScanner\FileMonitor\FileWatcher.cs:L51 — Async method has no await
  
  ```
ueue.Add(path);
            }
            catch { /* swallow noisy race conditions */ }
        }

        private async Task ProcessLoopAsync()
        {
            foreach (var path in _queue.GetConsumingEnumerable(_cts.Token))
  ```

- **Warning** [R011] .\PcFutureShield.RealtimeScanner\Program.cs:L41 — Method returns constant true/false/0
  
  ```
nWithExitCode(this IHost host)
        {
            try
            {
                host.Run();
                return 0;
            }
            catch (Exception ex)
            {
                var logger = host.Services.Ge
  ```

- **Warning** [R009] .\PcFutureShield.RealtimeScanner\Worker.cs:L28 — Async method has no await
  
  ```
{
            _logger = logger;
            _options = options.Value;
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("RealtimeScann
  ```

- **Warning** [R011] .\PcFutureShield.Tools.AutoValidator\Program.cs:L129 — Method returns constant true/false/0
  
  ```
ports written to: {output}");
        if (failOnFindings && report.Findings.Count > 0)
            return 2;
        return 0;
    }

    private static int GetLineNumber(string text, int index)
    {
        return text[..Math.Min(
  ```

- **Info** [R012] .\PcFutureShield.Tools.AutoValidator\Program.cs:L148 — Method only writes to Console
  
  ```
Files Scanned[/]", report.FilesScanned.ToString(), "[bold]Findings[/]", report.Findings.Count.ToString());
        AnsiConsole.Write(grid);

        var table = new Table().Border(TableBorder.Rounded);
        table.AddColumn("Rule");
  ```

- **Info** [R012] .\PcFutureShield.Tools.AutoValidator\Program.cs:L167 — Method only writes to Console
  
  ```
.Markup.Escape("```" + ex + "```");
            table.AddRow(title, severity, count, example);
        }
        AnsiConsole.Write(table);

        var paths = new Tree("[bold]Top Offenders (by file)[/]");
        foreach (var fileGro
  ```

- **Info** [R012] .\PcFutureShield.Tools.AutoValidator\Program.cs:L176 — Method only writes to Console
  
  ```
node.AddNode(Spectre.Console.Markup.Escape($"L{f.Line}: {f.RuleId} — {f.Message}"));
        }
        AnsiConsole.Write(paths);
    }

    private static void WriteArtifacts(ScanReport report, string outputDir)
    {
  ```

- **Warning** [R009] .\PcFutureShield.Tools.AutoValidator\Program.cs:L217 — Async method has no await
  
  ```
var text = File.ReadAllText(file);
                var edited = text;

                // Simple regex: find 'async Task' method headers and check for 'await' presence
                var methodPattern = new Regex(@"async\s+Task(
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L12 — simulate/stub/fake/mock/placeholder
  
  ```
row\s+new\s+NotImplementedException", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R002", "TODO Placeholder", "Contains TODO/FIXME/HACK comment", Severity.Warning, new Regex(@"//\s*(TODO|FIXME|HACK)|/\*\s*(TODO|FIXME
  ```

- **Error** [R003] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L13 — StubViewModel or 'Functionality not available' present
  
  ```
\*\s*(TODO|FIXME|HACK)", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R003", "Stub ViewModel", "StubViewModel or 'Functionality not available' present", Severity.Error, new Regex(@"StubViewModel|Functionality\s+not\s
  ```

- **Error** [R003] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L13 — StubViewModel or 'Functionality not available' present
  
  ```
ACK)", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R003", "Stub ViewModel", "StubViewModel or 'Functionality not available' present", Severity.Error, new Regex(@"StubViewModel|Functionality\s+not\s+available", Regex
  ```

- **Error** [R003] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L13 — StubViewModel or 'Functionality not available' present
  
  ```
new("R003", "Stub ViewModel", "StubViewModel or 'Functionality not available' present", Severity.Error, new Regex(@"StubViewModel|Functionality\s+not\s+available", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R00
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L13 — simulate/stub/fake/mock/placeholder
  
  ```
TODO|FIXME|HACK)|/\*\s*(TODO|FIXME|HACK)", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R003", "Stub ViewModel", "StubViewModel or 'Functionality not available' present", Severity.Error, new Regex(@"StubViewModel|Fun
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L13 — simulate/stub/fake/mock/placeholder
  
  ```
\*\s*(TODO|FIXME|HACK)", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R003", "Stub ViewModel", "StubViewModel or 'Functionality not available' present", Severity.Error, new Regex(@"StubViewModel|Functionality\s+not\s
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L13 — simulate/stub/fake/mock/placeholder
  
  ```
new("R003", "Stub ViewModel", "StubViewModel or 'Functionality not available' present", Severity.Error, new Regex(@"StubViewModel|Functionality\s+not\s+available", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R00
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
iewModel|Functionality\s+not\s+available", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placehold
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
ity\s+not\s+available", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.C
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
\s+available", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled |
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
ailable", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | Rege
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
le", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOpti
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOptions.I
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
ase)),
            new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R005", "Async
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R005", "Async Task.Com
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R005", "Async Task.Complete
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
new("R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R005", "Async Task.CompletedTask
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L14 — simulate/stub/fake/mock/placeholder
  
  ```
"R004", "Simulated Logic", "simulate/stub/fake/mock/placeholder", Severity.Warning, new Regex(@"simulate|stub|fake|mock|placeholder", RegexOptions.Compiled | RegexOptions.IgnoreCase)),
            new("R005", "Async Task.CompletedTask", "A
  ```

- **Warning** [R004] .\PcFutureShield.Tools.AutoValidator\ValidationRules.cs:L16 — simulate/stub/fake/mock/placeholder
  
  ```
ask\.CompletedTask\s*;", RegexOptions.Compiled)),
            new("R006", "Empty Method Body", "Method has empty body (stub)", Severity.Warning, new Regex(@"\b(public|private|protected|internal)\s+(async\s+)?[\w<>\[\]]+\s+\w+\s*\([^)]*\)\s
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L67 — DI interface referenced but not registered
  
  ```
ineManager>(new PcFutureShield.Engine.Quarantine.QuarantineManager());
                        ServiceLocator.Register<IRealtimeProtectionService>(new RealtimeProtectionService());
                        ServiceLocator.Register<IEventLog
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L68 — DI interface referenced but not registered
  
  ```
Register<IRealtimeProtectionService>(new RealtimeProtectionService());
                        ServiceLocator.Register<IEventLogService>(new EventLogService());
                        ServiceLocator.Register<ILicenseManager>(new LicenseM
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L72 — DI interface referenced but not registered
  
  ```
seManager());

                        Update(35, "Starting analysis engines...");
                        var threatIntelligenceService = new ThreatIntelligenceService();
                        var behavioralAnalysisService = new Beha
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L72 — DI interface referenced but not registered
  
  ```
Update(35, "Starting analysis engines...");
                        var threatIntelligenceService = new ThreatIntelligenceService();
                        var behavioralAnalysisService = new BehavioralAnalysisService();
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L74 — DI interface referenced but not registered
  
  ```
();
                        var zeroDayDetectionService = new ZeroDayDetectionService(behavioralAnalysisService, threatIntelligenceService);
                        var aiDetectionService = new AdvancedAIDetectionService();
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L75 — DI interface referenced but not registered
  
  ```
e(behavioralAnalysisService, threatIntelligenceService);
                        var aiDetectionService = new AdvancedAIDetectionService();

                        ServiceLocator.Register<ThreatIntelligenceService>(threatIntelligenceSer
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L77 — DI interface referenced but not registered
  
  ```
var aiDetectionService = new AdvancedAIDetectionService();

                        ServiceLocator.Register<ThreatIntelligenceService>(threatIntelligenceService);
                        ServiceLocator.Register<BehavioralAnalysisServ
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L77 — DI interface referenced but not registered
  
  ```
= new AdvancedAIDetectionService();

                        ServiceLocator.Register<ThreatIntelligenceService>(threatIntelligenceService);
                        ServiceLocator.Register<BehavioralAnalysisService>(behavioralAnalysisServ
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L80 — DI interface referenced but not registered
  
  ```
r.Register<ZeroDayDetectionService>(zeroDayDetectionService);
                        ServiceLocator.Register<AdvancedAIDetectionService>(aiDetectionService);

                        var antivirusOrchestrator = new AntivirusOrchestrator
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L86 — DI interface referenced but not registered
  
  ```
aiDetectionService,
                            behavioralAnalysisService,
                            threatIntelligenceService,
                            Microsoft.Extensions.Logging.Abstractions.NullLogger<AntivirusOrchestr
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L102 — DI interface referenced but not registered
  
  ```
irusScannerService(virusScannerLogger);

                        ServiceLocator.Register<PcFutureShield.UI.ViewModels.IVirusScannerService>(
                            new PcFutureShield.UI.ViewModels.VirusScannerServiceAdapter(virusSca
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L107 — DI interface referenced but not registered
  
  ```
// Register notification service for background tasks
                        ServiceLocator.Register<INotificationService>(new WpfNotificationService());

                        Update(85, "Finalizing startup...");
  ```

- **Warning** [R031] .\PcFutureShield.UI\App.xaml.cs:L134 — DI interface referenced but not registered
  
  ```
registered notifier
                                var notifier = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                                notifier?.ShowError("Startup Error", $"Failed to initialize ap
  ```

- **Warning** [R011] .\PcFutureShield.UI\Converters\BooleanToVisibilityConverter.cs:L20 — Method returns constant true/false/0
  
  ```
lture)
        {
            if (value is Visibility v)
                return v == Visibility.Visible;
            return false;
        }
    }
}
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainViewModel.cs:L60 — DI interface referenced but not registered
  
  ```
var behavioralAnalysis = new BehavioralAnalysisService();
                var threatIntelligence = new ThreatIntelligenceService();

                _antivirusOrchestrator = new AntivirusOrchestrator(
                    new Z
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainViewModel.cs:L64 — DI interface referenced but not registered
  
  ```
new ZeroDayDetectionService(behavioralAnalysis, threatIntelligence),
                    new AdvancedAIDetectionService(),
                    behavioralAnalysis,
                    threatIntelligence,
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainViewModel.cs:L87 — DI interface referenced but not registered
  
  ```
try
            {
                DashboardViewModel = new DashboardViewModel(_antivirusOrchestrator, new ThreatIntelligenceService());
                ParentalControlViewModel = new ParentalControlViewModel(_parentalControlService
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainWindow.xaml.cs:L82 — DI interface referenced but not registered
  
  ```
nViewModel: {ex.Message}", ex);
                    var notifier = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                    if (notifier != null)
                        notifier.ShowError("Initial
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainWindow.xaml.cs:L119 — DI interface referenced but not registered
  
  ```
led to initialize MainWindow", ex);
                var notifier = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                if (notifier != null)
                    notifier.ShowError("Startup Error",
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainWindow.xaml.cs:L207 — DI interface referenced but not registered
  
  ```
tion will continue with the current theme.";
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Theme Loading Error", errorMessage); } catch { System.Windows.Application.Current?
  ```

- **Warning** [R031] .\PcFutureShield.UI\MainWindow.xaml.cs:L227 — DI interface referenced but not registered
  
  ```
w", "Failed to open log viewer", ex);
                var notify = ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                if (notify != null) notify.ShowError("Error", $"Failed to open log viewer: {ex
  ```

- **Warning** [R031] .\PcFutureShield.UI\Scan\ScanManager.cs:L42 — DI interface referenced but not registered
  
  ```
try
                {
                    var notifier = Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>();
                    notifier.ShowError("PcFutureShield", $"ScanManager initialization failed:
  ```

- **Warning** [R031] .\PcFutureShield.UI\Services\EventLogService.cs:L8 — DI interface referenced but not registered
  
  ```
IO;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Services
{
    public class EventLogService : IEventLogService
    {
        private readonly List<RealtimeEvent> _events = new();
        public IEnumerable<Realt
  ```

- **Warning** [R031] .\PcFutureShield.UI\Services\IEventLogService.cs:L6 — DI interface referenced but not registered
  
  ```
lections.Generic;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Services
{
    public interface IEventLogService
    {
        IEnumerable<RealtimeEvent> GetRecentEvents();
        void ShowEventLogWindow();
  ```

- **Warning** [R031] .\PcFutureShield.UI\Services\INotificationService.cs:L5 — DI interface referenced but not registered
  
  ```
using System;

namespace PcFutureShield.UI.Services
{
    public interface INotificationService
    {
        void ShowInfo(string title, string message);
        void ShowWarning(string title, string message);
        void ShowErro
  ```

- **Warning** [R031] .\PcFutureShield.UI\Services\IRealtimeProtectionService.cs:L5 — DI interface referenced but not registered
  
  ```
using System;

namespace PcFutureShield.UI.Services
{
    public interface IRealtimeProtectionService
    {
        event EventHandler<UI.ViewModels.RealtimeEvent> ProtectionEvent;
        bool IsEnabled { get; }
        void SetEna
  ```

- **Info** [R012] .\PcFutureShield.UI\Services\LoggingService.cs:L76 — Method only writes to Console
  
  ```
coding.UTF8);
                }

                // Also output to console in debug mode
#if DEBUG
                Console.WriteLine(logEntry);
#endif
            }
            catch
            {
                // If logging fai
  ```

- **Warning** [R031] .\PcFutureShield.UI\Services\RealtimeProtectionService.cs:L6 — DI interface referenced but not registered
  
  ```
PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Services
{
    public class RealtimeProtectionService : IRealtimeProtectionService
    {
        public event EventHandler<RealtimeEvent>? ProtectionEvent;
        public bo
  ```

- **Warning** [R007] .\PcFutureShield.UI\Services\ServiceLocator.cs:L62 — Method returns default or null
  
  ```
typeof(T), out var service))
                    return (T)service;
            }
            catch { }
            return null;
        }
    }
}
  ```

- **Warning** [R009] .\PcFutureShield.UI\Services\UpdateService.cs:L33 — Async method has no await
  
  ```
ic record UpdateFeedEntry(string Version, string Url, string Sha256, bool IsCritical, string? Notes);

        public async Task<IReadOnlyList<UpdateFeedEntry>> GetAvailableUpdatesAsync(CancellationToken cancellationToken = default)
  ```

- **Warning** [R031] .\PcFutureShield.UI\Services\WpfNotificationService.cs:L6 — DI interface referenced but not registered
  
  ```
ng System;
using System.Windows;

namespace PcFutureShield.UI.Services
{
    public class WpfNotificationService : INotificationService
    {
        public void ShowInfo(string title, string message)
        {
            Applicat
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L154 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Data Load Error", $"Error loading admin override data: {ex.Message}"); } catch { Mess
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L158 — Async method has no await
  
  ```
ssage}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task RequestOverride()
        {
            if (string.IsNullOrWhiteSpace(OverrideReason))
            {
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L162 — DI interface referenced but not registered
  
  ```
hiteSpace(OverrideReason))
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowWarning("Reason Required", "Please provide a reason for the override request."); } catch { M
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L173 — DI interface referenced but not registered
  
  ```
dAdminOverrideData(); // Refresh data
                    try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Access Granted", "Admin access granted successfully."); } catch { MessageBox.Show("Adm
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L177 — DI interface referenced but not registered
  
  ```
else
                {
                    try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Request Submitted", $"Admin access request submitted: {result.Message}"); } catch { Me
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L182 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Request Error", $"Override request failed: {ex.Message}"); } catch { MessageBox.Show(
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L195 — DI interface referenced but not registered
  
  ```
LoadAdminOverrideData(); // Refresh data
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Request Approved", "Admin request approved."); } catch { MessageBox.Show("Admin reques
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L199 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Approval Error", $"Approval failed: {ex.Message}"); } catch { MessageBox.Show($"Appro
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L212 — DI interface referenced but not registered
  
  ```
LoadAdminOverrideData(); // Refresh data
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Request Denied", "Admin request denied."); } catch { MessageBox.Show("Admin request de
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L216 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("Deny Error", $"Deny failed: {ex.Message}"); } catch { MessageBox.Show($"Deny failed:
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L220 — Async method has no await
  
  ```
ex.Message}", "Deny Error", MessageBoxButton.OK, MessageBoxImage.Error); }
            }
        }

        private async Task EndOverride()
        {
            try
            {
                // End all active sessions
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L233 — DI interface referenced but not registered
  
  ```
LoadAdminOverrideData(); // Refresh data
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Sessions Ended", "All admin sessions ended successfully."); } catch { MessageBox.Show(
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L237 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowError("End Error", $"Failed to end sessions: {ex.Message}"); } catch { MessageBox.Show($"Fai
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L247 — DI interface referenced but not registered
  
  ```
{l.Action} by {l.User} - {l.Details}"));
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Admin Logs", $"Admin Activity Logs:\n{logText}"); } catch { MessageBox.Show($"Admin Ac
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs:L251 — DI interface referenced but not registered
  
  ```
}
            else
            {
                try { Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>().ShowInfo("Admin Logs", "No recent admin activity."); } catch { MessageBox.Show("No recent admin
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AIDashboardViewModel.cs:L23 — DI interface referenced but not registered
  
  ```
ble _systemRiskScore = 0.0;
        private string _aiModelStatus = "Loading...";

        private readonly AdvancedAIDetectionService _aiService;

        public AIDashboardViewModel()
        {
            _aiService = ServiceLocat
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\AIDashboardViewModel.cs:L27 — DI interface referenced but not registered
  
  ```
e _aiService;

        public AIDashboardViewModel()
        {
            _aiService = ServiceLocator.Get<AdvancedAIDetectionService>();
            AIStatus = "Ready";
            LastAnalysis = "Never";
            AIModelStatus =
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\AIDashboardViewModel.cs:L94 — Async method has no await
  
  ```
"Analysis Failed";
                LastAnalysis = $"Error: {ex.Message}";
            }
        }

        private async Task PerformQuickScan()
        {
            try
            {
                AIStatus = "Quick AI Scan...";
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\AIDashboardViewModel.cs:L145 — Method returns constant true/false/0
  
  ```
{
            try
            {
                var cert = new X509Certificate2(filePath);
                return true;
            }
            catch { return false; }
        }
    }
}
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\AIDashboardViewModel.cs:L147 — Method returns constant true/false/0
  
  ```
var cert = new X509Certificate2(filePath);
                return true;
            }
            catch { return false; }
        }
    }
}
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\BaseViewModel.cs:L20 — Method returns constant true/false/0
  
  ```
rMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\BaseViewModel.cs:L23 — Method returns constant true/false/0
  
  ```
als(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }

    public class RelayCommand : ICommand
    {
        private readonly Action _exec
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L258 — simulate/stub/fake/mock/placeholder
  
  ```
a.Type == PcFutureShield.Common.Services.AlertType.DownloadBlocked && a.Message.Contains("safe"));
                // Placeholder for tracking cookies blocked - would require browser integration
                TrackingCookiesBlocked = 0
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L263 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Data Load Error", $"Error loading browser extension data: {ex.Message}"); } catch {
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L291 — Async method has no await
  
  ```
return parts[1].Trim();
            }
            return string.Empty;
        }

        private async Task ScanForMaliciousSites()
        {
            try
            {
                // Scan some known suspic
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L315 — DI interface referenced but not registered
  
  ```
try
                {
                    var notifier = Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                    if (scanResults.Any()) notifier?.ShowInfo("Scan Complete", $"Scan Complete
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L325 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Malicious site scan failed: {ex.Message}"); } catch { }
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L334 — DI interface referenced but not registered
  
  ```
try
            {
                var notifier = Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                if (alerts.Any())
                {
                    var alertText = stri
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L348 — Async method has no await
  
  ```
blocked downloads or alerts.");
                }
            }
            catch { }
        }

        private async Task UpdateExtensionSettings()
        {
            try
            {
                var settings = new Brow
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L362 — DI interface referenced but not registered
  
  ```
Browser, "current_user", settings);
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Settings Updated", "Browser extension settings updated successfully."); } catch { }
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L366 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Failed to update settings: {ex.Message}"); } catch { }
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L374 — DI interface referenced but not registered
  
  ```
try
            {
                var notifier = Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                // No direct confirmation dialog in notifier abstraction, fall back to Message
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L399 — DI interface referenced but not registered
  
  ```
IsInstalled)
                {
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Already Installed", "Browser extension is already installed."); } catch { MessageBox.
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L407 — DI interface referenced but not registered
  
  ```
IsExtensionInstalled = true;
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Install Complete", "Browser extension installed successfully."); } catch { MessageBox
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L412 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Install Error", $"Installation failed: {ex.Message}"); } catch { MessageBox.Show($"I
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L427 — DI interface referenced but not registered
  
  ```
ExtensionVersion = "1.0.4";
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Update Complete", "Browser extension updated successfully."); } catch { MessageBox.Sh
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L431 — DI interface referenced but not registered
  
  ```
else
                {
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("No Update Needed", "Browser extension is up to date."); } catch { MessageBox.Show("Br
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L436 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Update failed: {ex.Message}"); } catch { MessageBox.Show($"Update f
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L442 — DI interface referenced but not registered
  
  ```
te void ConfigureExtension()
        {
            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Configure", "Opening extension configuration..."); } catch { MessageBox.Show("Opening
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L447 — DI interface referenced but not registered
  
  ```
ate void ViewExtensionLogs()
        {
            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Logs", "Opening extension logs..."); } catch { MessageBox.Show("Opening extension log
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L468 — DI interface referenced but not registered
  
  ```
a.Message}"));
                }

                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Browser Scan", scanReport); } catch { MessageBox.Show(scanReport, "Browser Scan", Mes
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L473 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Browser scan failed: {ex.Message}"); } catch { MessageBox.Show($"Brow
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs:L479 — DI interface referenced but not registered
  
  ```
vate void ClearBrowserData()
        {
            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Clear Data", "Clearing browser data..."); } catch { MessageBox.Show("Clearing browser
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L15 — DI interface referenced but not registered
  
  ```
iewModel
    {
        private readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;

        private string _systemHealthStatus = "Scanning...";
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L15 — DI interface referenced but not registered
  
  ```
ivate readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;

        private string _systemHealthStatus = "Scanning...";
        private int _activeThreats;
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L25 — DI interface referenced but not registered
  
  ```
<ThreatSummary> _recentThreats;

        public DashboardViewModel(AntivirusOrchestrator antivirusOrchestrator, ThreatIntelligenceService threatIntelligenceService)
        {
            _antivirusOrchestrator = antivirusOrchestrator;
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L25 — DI interface referenced but not registered
  
  ```
eats;

        public DashboardViewModel(AntivirusOrchestrator antivirusOrchestrator, ThreatIntelligenceService threatIntelligenceService)
        {
            _antivirusOrchestrator = antivirusOrchestrator;
            _threatIntelli
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L28 — DI interface referenced but not registered
  
  ```
threatIntelligenceService)
        {
            _antivirusOrchestrator = antivirusOrchestrator;
            _threatIntelligenceService = threatIntelligenceService;
            _recentThreats = new ObservableCollection<ThreatSummary>()
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L28 — DI interface referenced but not registered
  
  ```
{
            _antivirusOrchestrator = antivirusOrchestrator;
            _threatIntelligenceService = threatIntelligenceService;
            _recentThreats = new ObservableCollection<ThreatSummary>();

            QuickScanCo
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L129 — DI interface referenced but not registered
  
  ```
{
                    var notifier = PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                    notifier?.ShowError("Dashboard Error", $"Error loading dashboard: {ex.Mess
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L144 — DI interface referenced but not registered
  
  ```
boardDataAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", $"Quick scan completed. Found {result.TotalThreatsDetected} threats."
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L148 — DI interface referenced but not registered
  
  ```
x)
            {
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Quick scan failed: {ex.Message}"); } catch { }
                Syste
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L161 — DI interface referenced but not registered
  
  ```
boardDataAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", $"Full scan completed. Found {result.TotalThreatsDetected} threats.")
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L165 — DI interface referenced but not registered
  
  ```
x)
            {
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Full scan failed: {ex.Message}"); } catch { }
                System
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L174 — DI interface referenced but not registered
  
  ```
private async Task UpdateThreatIntelligence()
        {
            try
            {
                await _threatIntelligenceService.UpdateThreatFeedsAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L175 — DI interface referenced but not registered
  
  ```
reatFeedsAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Update Complete", "Threat intelligence database updated successfully."); } catch { }
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\DashboardViewModel.cs:L179 — DI interface referenced but not registered
  
  ```
x)
            {
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Failed to update threat intelligence: {ex.Message}"); } catch { }
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\EnhancedScanViewModel.cs:L159 — Async method has no await
  
  ```
}
            finally
            {
                IsScanning = false;
            }
        }

        private async Task RunCriticalScanAsync()
        {
            if (IsScanning) return;

            try
            {
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\EnhancedScanViewModel.cs:L235 — Async method has no await
  
  ```
}
            finally
            {
                IsScanning = false;
            }
        }

        private async Task RunCustomScanAsync()
        {
            if (IsScanning) return;

            try
            {
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L13 — DI interface referenced but not registered
  
  ```
ce PcFutureShield.UI.ViewModels
{
    public class EventLogViewModel : BaseViewModel
    {
        private readonly IEventLogService _eventLogService;

        public ObservableCollection<RealtimeEvent> Events { get; } = new();
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L60 — DI interface referenced but not registered
  
  ```
ntsCommand { get; }

        public EventLogViewModel()
        {
            _eventLogService = ServiceLocator.Get<IEventLogService>();
            _availableSeverities = new ObservableCollection<string>(AvailableSeverities);
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L94 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Load Error", $"Error loading events: {ex.Message}"); } catch { System.Windows.Messag
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L108 — Method returns constant true/false/0
  
  ```
verity != "All" && !evt.Severity.Equals(SelectedSeverity, StringComparison.OrdinalIgnoreCase))
                        return false;

                    // Filter by text
                    if (!string.IsNullOrWhiteSpace(FilterText))
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L119 — Method returns constant true/false/0
  
  ```
evt.Severity.ToLower().Contains(searchText);
                    }

                    return true;
                }).OrderByDescending(e => e.Time).ToList();

                Events.Clear();
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L130 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Filter Error", $"Error applying filter: {ex.Message}"); } catch { System.Windows.Mes
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L148 — DI interface referenced but not registered
  
  ```
();
                    Events.Clear();
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Events Cleared", "Event log cleared successfully."); } catch { System.Windows.Message
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L152 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Clear Error", $"Error clearing events: {ex.Message}"); } catch { System.Windows.Mess
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L162 — DI interface referenced but not registered
  
  ```
xportPath = _eventLogService.ExportEvents();
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Export Complete", $"Events exported successfully to: {exportPath}"); } catch { System
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L166 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Export Error", $"Error exporting events: {ex.Message}"); } catch { System.Windows.Me
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\EventLogViewModel.cs:L180 — DI interface referenced but not registered
  
  ```
$"File: {SelectedEvent.File}";

            try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Event Details", details); } catch { System.Windows.MessageBox.Show(details, "Event De
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L138 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Data Load Error", $"Error loading gaming protection data: {ex.Message}"); } catch {
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L142 — Async method has no await
  
  ```
sage}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error)); }
            }
        }

        private async Task ScanForCasinos()
        {
            try
            {
                // Scan known suspicious casino
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L166 — DI interface referenced but not registered
  
  ```
f (scanResults.Any())
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Scan Complete", $"Casino Scan Complete. Detected issues:\n{string.Join("\n", scanR
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L170 — DI interface referenced but not registered
  
  ```
else
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", "Casino scan complete. No high-risk casinos detected."); } catch { Sy
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L177 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Casino scan failed: {ex.Message}"); } catch { System.Windows.Applicat
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L213 — DI interface referenced but not registered
  
  ```
if (rigResults.Any())
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Scan Complete", $"Rig Scan Complete. Detected issues:\n{string.Join("\n", rigResul
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L217 — DI interface referenced but not registered
  
  ```
else
                {
                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", "Rig scan complete. No suspicious gaming processes detected."); } cat
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L224 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Rig scan failed: {ex.Message}"); } catch { System.Windows.Application
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L234 — DI interface referenced but not registered
  
  ```
{r.Description} (Severity: {r.Severity})"));
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning("Fraud Reports", $"Fraud Reports:\n{reportText}"); } catch { System.Windows.Applica
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L238 — DI interface referenced but not registered
  
  ```
}
            else
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Fraud Reports", "No recent fraud reports."); } catch { System.Windows.Application.Cur
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L242 — Async method has no await
  
  ```
.", "Fraud Reports", MessageBoxButton.OK, MessageBoxImage.Information)); }
            }
        }

        private async Task UpdateProtectionSettings()
        {
            try
            {
                // Save settings to a
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L261 — DI interface referenced but not registered
  
  ```
ile.WriteAllTextAsync(settingsPath, json);

                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Settings Updated", "Gaming protection settings updated successfully."); } catch { Sys
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs:L265 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Failed to update settings: {ex.Message}"); } catch { System.Windows
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\LicenseManagerViewModel.cs:L131 — Async method has no await
  
  ```
tection", Status = "Enabled", Description = "Gaming-specific security" });
            }
        }

        private async Task ActivateLicenseAsync()
        {
            if (string.IsNullOrWhiteSpace(ProductKey))
            {
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\LicenseManagerViewModel.cs:L141 — simulate/stub/fake/mock/placeholder
  
  ```
MessageBoxImage.Warning);
                return;
            }

            try
            {
                // Simulate license activation (in real implementation, this would call a service)
                await Task.Delay(1000);
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\LicenseManagerViewModel.cs:L142 — simulate/stub/fake/mock/placeholder
  
  ```
late license activation (in real implementation, this would call a service)
                await Task.Delay(1000); // Simulate network call

                // For demo purposes, accept any key that looks valid
                if (Prod
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\LicenseManagerViewModel.cs:L173 — simulate/stub/fake/mock/placeholder
  
  ```
== System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    // Simulate license deactivation
                    await Task.Delay(1000);

                    LicenseStatus = "Inact
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\LicenseManagerViewModel.cs:L193 — simulate/stub/fake/mock/placeholder
  
  ```
}

        private async Task RefreshLicenseAsync()
        {
            try
            {
                // Simulate license refresh
                await Task.Delay(1000);

                LoadLicenseData(); // Refresh all d
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\LicenseManagerViewModel.cs:L213 — simulate/stub/fake/mock/placeholder
  
  ```
== System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    // Simulate license transfer
                    await Task.Delay(1000);

                    LicenseStatus = "Transferr
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L15 — DI interface referenced but not registered
  
  ```
yChanged
    {
        private readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;
        private readonly ParentalControlService _parentalControlService
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L15 — DI interface referenced but not registered
  
  ```
ivate readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;
        private readonly ParentalControlService _parentalControlService;
        private readonly
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L73 — DI interface referenced but not registered
  
  ```
_antivirusOrchestrator = PcFutureShield.UI.Services.ServiceLocator.Get<AntivirusOrchestrator>();
            _threatIntelligenceService = PcFutureShield.UI.Services.ServiceLocator.Get<ThreatIntelligenceService>();
            _parental
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L73 — DI interface referenced but not registered
  
  ```
AntivirusOrchestrator>();
            _threatIntelligenceService = PcFutureShield.UI.Services.ServiceLocator.Get<ThreatIntelligenceService>();
            _parentalControlService = PcFutureShield.UI.Services.ServiceLocator.Get<ParentalCon
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L113 — DI interface referenced but not registered
  
  ```
{
                            var dashboardVm = new DashboardViewModel(_antivirusOrchestrator, _threatIntelligenceService);
                            CurrentView = dashboardVm; // DataTemplate will render DashboardView
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L127 — DI interface referenced but not registered
  
  ```
var virusScannerAdapter = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.ViewModels.IVirusScannerService>();
                            virusScanView.DataContext = new PcFutureShield.UI.ViewModels.Virus
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L351 — DI interface referenced but not registered
  
  ```
{
                            var notifier = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>();
                            notifier.ShowError("Navigation Error", $"Unknown view: {viewName}"
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\MainViewModel.cs:L365 — DI interface referenced but not registered
  
  ```
{
                    var notifier = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>();
                    notifier.ShowError("Navigation Error", $"Failed to navigate to {viewName}:
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\ParentalControlViewModel.cs:L231 — Async method has no await
  
  ```
parentalControlService.RemoveUserProfile(profile.Name);
                }
            }
        }

        private async Task UpdateFilters()
        {
            try
            {
                // Update content filters through
  ```

- **Info** [R012] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L216 — Method only writes to Console
  
  ```
{
                // Log error to console as fallback; UI should surface via logger/notification
                Console.WriteLine($"Error analyzing system: {ex.Message}");
            }
            finally
            {
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L224 — Async method has no await
  
  ```
finally
            {
                IsAnalyzing = false;
            }
        }

        private async Task PerformOptimizationAsync()
        {
            if (IsOptimizing) return;

            IsOptimizing = tr
  ```

- **Info** [R012] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L251 — Method only writes to Console
  
  ```
OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing optimization: {ex.Message}");
                OptimizationStatus = "Optimization f
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L267 — Async method has no await
  
  ```
ons
            return await _optimizationService.PerformDeepOptimizationAsync(options);
        }

        private async Task PerformRepairAsync()
        {
            if (IsRepairing) return;

            IsRepairing = true;
  ```

- **Info** [R012] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L289 — Method only writes to Console
  
  ```
d." : "Repair completed with issues.";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing repair: {ex.Message}");
                OptimizationStatus = "Repair failed.";
  ```

- **Info** [R012] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L312 — Method only writes to Console
  
  ```
OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error boosting performance: {ex.Message}");
                OptimizationStatus = "Boost failed.";
  ```

- **Info** [R012] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L353 — Method only writes to Console
  
  ```
OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing uninstall: {ex.Message}");
                OptimizationStatus = "Uninstall failed.
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L363 — Async method has no await
  
  ```
stalling = false;
            }
        }

        // Implementations for XAML referenced commands
        private async Task PerformCleanTempFilesAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus =
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L383 — Async method has no await
  
  ```
orary file cleanup completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformDefragmentAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "Def
  ```

- **Warning** [R032] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L403 — Async method defined but never awaited or invoked
  
  ```
= "Defragmentation completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformClearCacheAsync(CancellationToken cancellationToken)
        {
            // Clear cache is part of
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L409 — Async method has no await
  
  ```
file cleanup in service
            await PerformCleanTempFilesAsync(cancellationToken);
        }

        private async Task PerformOptimizeStartupAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus =
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L429 — Async method has no await
  
  ```
artup optimization completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformUpdateDriversAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L449 — Async method has no await
  
  ```
river update check completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformFullOptimizationAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs:L470 — Async method has no await
  
  ```
imizationStatus = "Full optimization complete.";
            OptimizationProgress = 100;
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                var report = new
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\RealtimeProtectionViewModel.cs:L19 — DI interface referenced but not registered
  
  ```
timeEvent> Events { get; } = new();

        public ICommand ViewEventsCommand { get; }


        private readonly IRealtimeProtectionService _protectionService;
        private readonly IEventLogService _eventLogService;

        p
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\RealtimeProtectionViewModel.cs:L20 — DI interface referenced but not registered
  
  ```
Command { get; }


        private readonly IRealtimeProtectionService _protectionService;
        private readonly IEventLogService _eventLogService;

        public RealtimeProtectionViewModel()
        {
            // Dependency
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\RealtimeProtectionViewModel.cs:L25 — DI interface referenced but not registered
  
  ```
{
            // Dependency injection or service locator
            _protectionService = ServiceLocator.Get<IRealtimeProtectionService>();
            _eventLogService = ServiceLocator.Get<IEventLogService>();

            Vi
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\RealtimeProtectionViewModel.cs:L26 — DI interface referenced but not registered
  
  ```
rotectionService = ServiceLocator.Get<IRealtimeProtectionService>();
            _eventLogService = ServiceLocator.Get<IEventLogService>();

            ViewEventsCommand = new RelayCommand(ViewEvents);

            // Subscribe to rea
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\ScannerViewModel.cs:L42 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Initialization Error", $"ScannerViewModel initialization failed: {ex.Message}\n{ex.S
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\ScannerViewModel.cs:L69 — DI interface referenced but not registered
  
  ```
});

                Progress = 100;
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", $"Scan completed. Found {Results.Count(r => r.IsMalicious)} threats."
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\ScannerViewModel.cs:L73 — DI interface referenced but not registered
  
  ```
catch (Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Scan failed: {ex.Message}"); } catch { System.Windows.Application.Cur
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\SettingsViewModel.cs:L225 — DI interface referenced but not registered
  
  ```
t("DataRetentionDays", DataRetentionDays);

                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Settings Saved", "Settings saved successfully!"); } catch { System.Windows.Applicatio
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\SettingsViewModel.cs:L229 — DI interface referenced but not registered
  
  ```
catch (System.Exception ex)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Save Error", $"Failed to save settings: {ex.Message}"); } catch { System.Windows.App
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\SettingsViewModel.cs:L262 — DI interface referenced but not registered
  
  ```
private void CheckForUpdates()
        {
            try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Update Check", "Checking for updates..."); } catch { System.Windows.Application.Curre
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\SettingsViewModel.cs:L274 — DI interface referenced but not registered
  
  ```
al implementation, this would clear all data
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Data Cleared", "All data cleared successfully."); } catch { System.Windows.Applicatio
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\SettingsViewModel.cs:L286 — Method returns constant true/false/0
  
  ```
rMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
  ```

- **Warning** [R011] .\PcFutureShield.UI\ViewModels\SettingsViewModel.cs:L289 — Method returns constant true/false/0
  
  ```
als(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\SmartRepairViewModel.cs:L120 — Async method has no await
  
  ```
agnostics failed";
                RepairStatus = $"Error: {ex.Message}";
            }
        }

        private async Task FixRegistryAsync()
        {
            try
            {
                CurrentRepairOperation = "Fixi
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\SmartRepairViewModel.cs:L151 — Async method has no await
  
  ```
{
                RepairStatus = $"Registry repair failed: {ex.Message}";
            }
        }

        private async Task RepairSystemFilesAsync()
        {
            try
            {
                CurrentRepairOperation =
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\SmartRepairViewModel.cs:L181 — Async method has no await
  
  ```
RepairStatus = $"System file repair failed: {ex.Message}";
            }
        }

        private async Task CleanStartupAsync()
        {
            try
            {
                CurrentRepairOperation = "Cle
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\SmartRepairViewModel.cs:L212 — Async method has no await
  
  ```
{
                RepairStatus = $"Startup cleanup failed: {ex.Message}";
            }
        }

        private async Task FixNetworkAsync()
        {
            try
            {
                CurrentRepairOperation = "Fixin
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\SmartRepairViewModel.cs:L242 — Async method has no await
  
  ```
{
                RepairStatus = $"Network repair failed: {ex.Message}";
            }
        }

        private async Task RepairWindowsUpdateAsync()
        {
            try
            {
                CurrentRepairOperation
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\SmartRepairViewModel.cs:L330 — Async method has no await
  
  ```
RepairStatus = $"Report generation failed: {ex.Message}";
            }
        }

        private async Task EmergencyRepairAsync()
        {
            try
            {
                CurrentRepairOperation = "
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L177 — simulate/stub/fake/mock/placeholder
  
  ```
UpdateProgress = 0;
                UpdateStatus = "Contacting update server...";

                // Simulate network delay
                await Task.Delay(2000);
                UpdateProgress = 50;
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L212 — Async method has no await
  
  ```
essage}";
                CurrentUpdateOperation = "Update check failed";
            }
        }

        private async Task DownloadUpdatesAsync()
        {
            if (UpdatesAvailable == 0)
            {
                try
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L216 — DI interface referenced but not registered
  
  ```
if (UpdatesAvailable == 0)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("No Updates", "No updates available to download."); } catch { System.Windows.Applicati
  ```

- **Warning** [R009] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L310 — Async method has no await
  
  ```
}
            finally
            {
                cts.Dispose();
            }
        }

        private async Task InstallUpdatesAsync()
        {
            if (UpdatesAvailable == 0)
            {
                try
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L314 — DI interface referenced but not registered
  
  ```
if (UpdatesAvailable == 0)
            {
                try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("No Updates", "No updates available to install."); } catch { System.Windows.Applicatio
  ```

- **Warning** [R004] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L339 — simulate/stub/fake/mock/placeholder
  
  ```
UpdateProgress = 0;
                    UpdateStatus = "Installing update files...";

                    // Simulate installation progress
                    for (int i = 0; i <= 100; i += 5)
                    {
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\UpdatesViewModel.cs:L354 — DI interface referenced but not registered
  
  ```
on = "Updates installed successfully";

                    try { ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Installation Complete", "Updates installed successfully! Please restart the applicati
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\VirusScanViewModel.cs:L15 — DI interface referenced but not registered
  
  ```
PcFutureShield.Engine.VirusScanner;
using System.Linq;

namespace PcFutureShield.UI.ViewModels
{
    // Define the IVirusScannerService interface if not already defined elsewhere
    public interface IVirusScannerService
    {
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\VirusScanViewModel.cs:L16 — DI interface referenced but not registered
  
  ```
.ViewModels
{
    // Define the IVirusScannerService interface if not already defined elsewhere
    public interface IVirusScannerService
    {
        Task<ScanResult> ScanAsync(string[] paths, CancellationToken cancellationToken);
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\VirusScanViewModel.cs:L22 — DI interface referenced but not registered
  
  ```
}

    // Adapter to wrap Engine.VirusScannerService for use in UI
    public class VirusScannerServiceAdapter : IVirusScannerService
    {
        private readonly VirusScannerService _engineService;

        public VirusScanner
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\VirusScanViewModel.cs:L67 — DI interface referenced but not registered
  
  ```
set; } = false;
    }

    public class VirusScanViewModel : INotifyPropertyChanged
    {
        private readonly IVirusScannerService _scannerService;
        private CancellationTokenSource? _cts;

        public ObservableCollec
  ```

- **Warning** [R031] .\PcFutureShield.UI\ViewModels\VirusScanViewModel.cs:L83 — DI interface referenced but not registered
  
  ```
mand StartScanCommand { get; }
        public ICommand CancelScanCommand { get; }

        public VirusScanViewModel(IVirusScannerService scannerService)
        {
            _scannerService = scannerService;
            StartScanCom
  ```

