using System;
using System.Windows;
namespace {}
{
    public partial class SharedStyles : Window
    {
        public SharedStyles() { 
            // Auto-wired DataContext by AutoValidationTool
DataContext = new SharedStylesViewModel();

InitializeComponent(); }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void GlassToggleButtonStyle(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}