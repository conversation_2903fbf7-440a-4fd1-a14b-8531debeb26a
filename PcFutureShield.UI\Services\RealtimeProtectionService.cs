using System;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Services
{
    public class RealtimeProtectionService : IRealtimeProtectionService
    {
        public event EventHandler<RealtimeEvent>? ProtectionEvent;
        public bool IsEnabled { get; private set; } = true;
        public event EventHandler<bool>? ProtectionStatusChanged;


        public void SetEnabled(bool enabled)
        {
            if (IsEnabled != enabled)
            {
                IsEnabled = enabled;
                ProtectionStatusChanged?.Invoke(this, enabled);
                // Raise a real protection event when status changes
                var evt = new RealtimeEvent
                {
                    Time = DateTime.Now,
                    Event = enabled ? "Protection Enabled" : "Protection Disabled",
                    File = string.Empty,
                    Severity = enabled ? "Info" : "Warning"
                };
                ProtectionEvent?.Invoke(this, evt);
            }
        }

    }
}
