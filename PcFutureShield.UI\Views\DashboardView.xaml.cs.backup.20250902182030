using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class DashboardView : UserControl
    {
        public DashboardView()
        {
            InitializeComponent();
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Center(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
