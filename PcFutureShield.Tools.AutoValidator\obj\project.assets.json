{"version": 3, "targets": {"net8.0": {"Spectre.Console/0.44.0": {"type": "package", "compile": {"lib/net6.0/Spectre.Console.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Spectre.Console.dll": {"related": ".xml"}}}}}, "libraries": {"Spectre.Console/0.44.0": {"sha512": "QMpKtn5QV1j2nklqDwuRE/XwbDn5jxww+Mt5J6Jn3EeQIzaLcuLyUhCLh/mS8giN6uXbqOV1ep5HlUGxrV1ujQ==", "type": "package", "path": "spectre.console/0.44.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Spectre.Console.dll", "lib/net5.0/Spectre.Console.xml", "lib/net6.0/Spectre.Console.dll", "lib/net6.0/Spectre.Console.xml", "lib/netstandard2.0/Spectre.Console.dll", "lib/netstandard2.0/Spectre.Console.xml", "small-logo.png", "spectre.console.0.44.0.nupkg.sha512", "spectre.console.nuspec"]}}, "projectFileDependencyGroups": {"net8.0": ["Spectre.Console >= 0.44.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Tools.AutoValidator\\PcFutureShield.Tools.AutoValidator.csproj", "projectName": "PcFutureShield.Tools.AutoValidator", "projectPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Tools.AutoValidator\\PcFutureShield.Tools.AutoValidator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Tools.AutoValidator\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Spectre.Console": {"target": "Package", "version": "[0.44.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}