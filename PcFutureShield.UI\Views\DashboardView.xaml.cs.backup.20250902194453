using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class DashboardView : UserControl
    {
        public DashboardView()
        {
            
            // Auto-wired DataContext by AutoValidationTool
DataContext = new DashboardViewModel();

InitializeComponent();
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Center(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}

		// Auto-generated event handler stub by AutoValidationTool
		private void SemiBold(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
