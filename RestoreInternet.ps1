# PowerShell script to restore normal internet access
# Run this as Administrator

Write-Host "Restoring Internet Access..." -ForegroundColor Yellow

# 1. Restore DNS settings to automatic (DHCP)
Write-Host "Restoring DNS settings..." -ForegroundColor Green

try {
    # Get all active network adapters
    $adapters = Get-NetAdapter | Where-Object { $_.Status -eq "Up" }
    
    foreach ($adapter in $adapters) {
        Write-Host "Restoring DNS for adapter: $($adapter.Name)" -ForegroundColor Cyan
        Set-DnsClientServerAddress -InterfaceAlias $adapter.Name -ResetServerAddresses
    }
    
    Write-Host "DNS settings restored successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error restoring DNS: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. Clean hosts file
Write-Host "Cleaning hosts file..." -ForegroundColor Green

try {
    $hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"
    
    # Create backup
    Copy-Item $hostsPath "$hostsPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')" -Force
    
    # Read current hosts file and remove PcFutureShield entries
    $hostsContent = Get-Content $hostsPath
    $cleanedContent = $hostsContent | Where-Object { 
        $_ -notmatch "PcFutureShield" -and 
        $_ -notmatch "# Blocked by PcFutureShield" 
    }
    
    # Write cleaned content back
    $cleanedContent | Out-File -FilePath $hostsPath -Encoding ASCII -Force
    
    Write-Host "Hosts file cleaned successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error cleaning hosts file: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. Flush DNS cache
Write-Host "Flushing DNS cache..." -ForegroundColor Green

try {
    ipconfig /flushdns | Out-Null
    Write-Host "DNS cache flushed successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error flushing DNS cache: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. Remove any firewall rules created by PcFutureShield
Write-Host "Removing firewall rules..." -ForegroundColor Green

try {
    $firewallRules = Get-NetFirewallRule | Where-Object { $_.DisplayName -like "*PcFutureShield*" }
    foreach ($rule in $firewallRules) {
        Remove-NetFirewallRule -Name $rule.Name -Confirm:$false
        Write-Host "Removed firewall rule: $($rule.DisplayName)" -ForegroundColor Cyan
    }
    
    if ($firewallRules.Count -eq 0) {
        Write-Host "No PcFutureShield firewall rules found." -ForegroundColor Yellow
    } else {
        Write-Host "Firewall rules removed successfully!" -ForegroundColor Green
    }
} catch {
    Write-Host "Error removing firewall rules: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. Test internet connectivity
Write-Host "Testing internet connectivity..." -ForegroundColor Green

try {
    $testSites = @("google.com", "microsoft.com", "github.com")
    
    foreach ($site in $testSites) {
        $result = Test-NetConnection -ComputerName $site -Port 80 -InformationLevel Quiet
        if ($result) {
            Write-Host "✓ $site is accessible" -ForegroundColor Green
        } else {
            Write-Host "✗ $site is not accessible" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Error testing connectivity: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Internet access restoration complete!" -ForegroundColor Yellow
Write-Host "Your DNS settings have been restored to automatic (DHCP)" -ForegroundColor White
Write-Host "All PcFutureShield blocking entries have been removed from hosts file" -ForegroundColor White
Write-Host "DNS cache has been flushed" -ForegroundColor White
Write-Host ""
Write-Host "If you still have issues accessing certain sites:" -ForegroundColor Cyan
Write-Host "1. Restart your browser" -ForegroundColor White
Write-Host "2. Try opening an incognito/private browsing window" -ForegroundColor White
Write-Host "3. Restart your computer if problems persist" -ForegroundColor White

Read-Host "Press Enter to exit"
