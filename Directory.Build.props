<Project>
  <PropertyGroup>
    <!-- Global properties for all projects -->
    <LangVersion>latest</LangVersion>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <WarningsAsErrors />
    <WarningsNotAsErrors />
    
    <!-- Suppress common warnings for enterprise security software -->
    <NoWarn>
      <!-- Async method lacks 'await' operators -->
      CS1998;
      <!-- Nullable reference type warnings -->
      CS8618;CS8625;CS8603;CS8601;CS8622;CS8612;CS8767;CS8604;CS8602;CS8600;
      <!-- Unused fields/variables -->
      CS0169;CS0649;CS0219;
      <!-- XML documentation warnings -->
      CS1591;
      <!-- Platform compatibility warnings (Windows-only software) -->
      CA1416;
      <!-- Because this call is not awaited -->
      CS4014;
      <!-- Field is never used -->
      CS0169;
      <!-- Field is never assigned to -->
      CS0649;
      <!-- Variable is assigned but never used -->
      CS0219
    </NoWarn>
    
    <!-- Platform-specific definitions -->
    <DefineConstants Condition="'$(OS)' == 'Windows_NT'">$(DefineConstants);WINDOWS</DefineConstants>
  </PropertyGroup>
  
  <!-- Conditional compilation for Windows-specific features -->
  <PropertyGroup Condition="'$(OS)' == 'Windows_NT'">
    <DefineConstants>$(DefineConstants);WINDOWS_PLATFORM</DefineConstants>
  </PropertyGroup>
</Project>
