using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace PcFutureShield.Engine.Scanning
{
    /// <summary>
    /// Service for scanning files, directories, and processes for potential threats
    /// </summary>
    public interface IScannerService
    {
        /// <summary>
        /// Scans a single file for threats
        /// </summary>
        Task<ScanResult> ScanFileAsync(string filePath, CancellationToken ct = default);

        /// <summary>
        /// Recursively scans a directory for threats
        /// </summary>
        Task<ScanResult> ScanDirectoryAsync(string directoryPath, bool recursive = true, CancellationToken ct = default);

        /// <summary>
        /// Scans a running process for threats
        /// </summary>
        Task<ScanResult> ScanProcessAsync(int processId, CancellationToken ct = default);
    }


    /// <summary>
    /// Represents a detected threat
    /// </summary>
    public class ThreatDetection
    {
        /// <summary>
        /// Path to the infected file
        /// </summary>
        public string FilePath { get; set; }

        /// <summary>
        /// Name of the detected threat
        /// </summary>
        public string ThreatName { get; set; } = string.Empty;

        /// <summary>
        /// Type of threat (e.g., Virus, Trojan, Ransomware)
        /// </summary>
        public string ThreatType { get; set; } = string.Empty;

        /// <summary>
        /// Severity of the threat
        /// </summary>
        public ThreatSeverity Severity { get; set; }

        /// <summary>
        /// Additional details about the threat
        /// </summary>
        public Dictionary<string, string>? Details { get; set; }
    }

    /// <summary>
    /// Represents the result of a heuristic analysis
    /// </summary>
    public class HeuristicAnalysisResult
    {
        /// <summary>
        /// Indicates if the analysis found suspicious activity
        /// </summary>
        public bool IsSuspicious { get; set; }

        /// <summary>
        /// Name of the potential threat
        /// </summary>
        public string ThreatName { get; set; } = string.Empty;

        /// <summary>
        /// Severity of the potential threat
        /// </summary>
        public ThreatSeverity Severity { get; set; }

        /// <summary>
        /// Additional details about the analysis
        /// </summary>
        public Dictionary<string, string> Details { get; } = new();
    }

    /// <summary>
    /// Represents the severity level of a detected threat
    /// </summary>
    public enum ThreatSeverity 
    { 
        /// <summary>Informational finding</summary>
        Info,
        
        /// <summary>Low risk threat</summary>
        Low, 
        
        /// <summary>Medium risk threat</summary>
        Medium, 
        
        /// <summary>High risk threat</summary>
        High, 
        
        /// <summary>Critical system threat</summary>
        Critical 
    }
}
