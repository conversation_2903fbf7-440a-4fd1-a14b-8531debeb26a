using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace PcFutureShield.Tools.AutoValidator
{
    public static class ValidationRules
    {
        /// <summary>
        /// Runs all validation rules against the file contents.
        /// </summary>
        public static List<ValidationResult> Validate(string filePath, string fileContent)
        {
            var results = new List<ValidationResult>();

            // Rule 1: Detect async methods without await (fake async)
            if (Regex.IsMatch(fileContent, @"async\s+Task\s+\w+\s*\([^)]*\)\s*{[^}]*Task\.FromResult", RegexOptions.Singleline))
            {
                results.Add(new ValidationResult(filePath, "Async method using Task.FromResult instead of real async logic."));
            }

            // Rule 2: Detect NotImplementedException placeholders
            if (fileContent.Contains("throw new NotImplementedException()"))
            {
                results.Add(new ValidationResult(filePath, "Method contains NotImplementedException (fake placeholder)."));
            }

            // Rule 3: Detect TODO / FIXME / STUB comments
            if (Regex.IsMatch(fileContent, @"//\s*(TODO|FIXME|STUB)", RegexOptions.IgnoreCase))
            {
                results.Add(new ValidationResult(filePath, "Contains TODO/FIXME/STUB (incomplete code)."));
            }

            // Rule 4: Detect empty method bodies
            if (Regex.IsMatch(fileContent, @"\)\s*{\s*}", RegexOptions.Singleline))
            {
                results.Add(new ValidationResult(filePath, "Method with empty body (likely unfinished)."));
            }

            // Rule 5: Detect missing INotifyPropertyChanged in ViewModels (XAML binding issues)
            if (filePath.EndsWith("ViewModel.cs") && !fileContent.Contains("INotifyPropertyChanged"))
            {
                results.Add(new ValidationResult(filePath, "ViewModel missing INotifyPropertyChanged implementation (bindings will not update)."));
            }

            // Rule 6: Detect XAML Binding errors (missing DataContext or Binding Path typos)
            if (filePath.EndsWith(".xaml"))
            {
                if (!fileContent.Contains("DataContext"))
                {
                    results.Add(new ValidationResult(filePath, "XAML missing DataContext (bindings may fail)."));
                }
                if (Regex.IsMatch(fileContent, @"{Binding\s+[^}]*}", RegexOptions.IgnoreCase) &&
                    fileContent.Contains("Binding") && fileContent.Contains("Path") == false)
                {
                    results.Add(new ValidationResult(filePath, "XAML Binding missing Path (binding likely broken)."));
                }
            }

            // Rule 7: Detect catch blocks swallowing exceptions
            if (Regex.IsMatch(fileContent, @"catch\s*\([^)]*\)\s*{[^}]*}", RegexOptions.Singleline) &&
                !fileContent.Contains("throw"))
            {
                results.Add(new ValidationResult(filePath, "Catch block swallowing exception without handling or logging."));
            }

            return results;
        }
    }
}

