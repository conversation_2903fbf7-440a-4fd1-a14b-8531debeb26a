<Window x:Class="PcFutureShield.UI.Views.SplashWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent" Width="500" Height="300" Topmost="True" ShowInTaskbar="False">
    <Border CornerRadius="12" Padding="18" BorderThickness="2" BorderBrush="#FF0A99A4">
        <Border.Background>
            <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                <GradientStop Color="Black"/>
                <GradientStop Color="#FF18317F" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Grid>
            <Grid.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF18317F" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="PcFutureShield" FontSize="28" FontWeight="Normal" HorizontalAlignment="Center" FontFamily="Times New Roman" Foreground="#FFEA8117"/>
                <TextBlock x:Name="MessageText" Text="Loading..." FontSize="14" Foreground="#FFCCA20B" Margin="0,8,0,0" HorizontalAlignment="Center"/>
            </StackPanel>
            <ProgressBar x:Name="ProgressBar" Grid.Row="1" Height="14" Minimum="0" Maximum="100" Value="0" Margin="0,14,0,0" Foreground="#FFFF6B04" Background="#FF1B45AB" BorderBrush="#FFEA0808"/>
        </Grid>
    </Border>
</Window>
