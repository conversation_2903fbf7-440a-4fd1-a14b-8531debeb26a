# PowerShell script to fix View code-behind files

$viewFiles = @(
    "PcFutureShield.UI\Views\LogerViewModel.cs",
    "PcFutureShield.UI\Views\LogViewerWindow.xaml.cs",
    "PcFutureShield.UI\Views\ParentalControlView.xaml.cs",
    "PcFutureShield.UI\Views\AdminOverrideView.xaml.cs",
    "PcFutureShield.UI\Views\PcOptimizationView.xaml.cs",
    "PcFutureShield.UI\Views\AIDashboardView.xaml.cs",
    "PcFutureShield.UI\Views\QuarantineView.xaml.cs",
    "PcFutureShield.UI\Views\BrowserExtensionView.xaml.cs",
    "PcFutureShield.UI\Views\RealtimeProtectionView.xaml.cs",
    "PcFutureShield.UI\Views\DashboardView.xaml.cs",
    "PcFutureShield.UI\Views\ScannerView.xaml.cs",
    "PcFutureShield.UI\Views\SettingsView.xaml.cs",
    "PcFutureShield.UI\Views\EnhancedScanView.xaml.cs",
    "PcFutureShield.UI\Views\SmartRepairView.xaml.cs",
    "PcFutureShield.UI\Views\SplashWindow.xaml.cs",
    "PcFutureShield.UI\Views\UpdatesView.xaml.cs",
    "PcFutureShield.UI\Views\VirusScanView.xaml.cs",
    "PcFutureShield.UI\Views\EventLogWindow.xaml.cs",
    "PcFutureShield.UI\Views\GamingProtectionView.xaml.cs",
    "PcFutureShield.UI\Views\LicenseManagerView.xaml.cs",
    "PcFutureShield.UI\Views\ThemeSelectorView.xaml.cs"
)

foreach ($file in $viewFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing view file $file..."
        
        $className = [System.IO.Path]::GetFileNameWithoutExtension($file) -replace '\.xaml$', ''
        $baseClass = if ($className -like "*Window") { "Window" } else { "UserControl" }
        
        $content = @"
using System;
using System.Windows;
using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class $className : $baseClass
    {
        public $className()
        {
            InitializeComponent();
        }
    }
}
"@
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed view file $file"
    }
}

# Fix MainWindow.xaml.cs separately
if (Test-Path "PcFutureShield.UI\MainWindow.xaml.cs") {
    $content = @"
using System;
using System.Windows;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            DataContext = new MainViewModel();
        }
    }
}
"@
    
    Set-Content "PcFutureShield.UI\MainWindow.xaml.cs" $content -Encoding UTF8
    Write-Host "Fixed MainWindow.xaml.cs"
}

Write-Host "All view files fixed!"
