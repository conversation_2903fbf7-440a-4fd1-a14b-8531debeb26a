# PowerShell script to fix all ViewModels by removing duplicate INotifyPropertyChanged implementations

$viewModelFiles = @(
    "PcFutureShield.UI/ViewModels/AdminOverrideViewModel.cs",
    "PcFutureShield.UI/ViewModels/BrowserExtensionViewModel.cs", 
    "PcFutureShield.UI/ViewModels/DashboardViewModel.cs",
    "PcFutureShield.UI/ViewModels/EnhancedScanViewModel.cs",
    "PcFutureShield.UI/ViewModels/EventLogViewModel.cs",
    "PcFutureShield.UI/ViewModels/GamingProtectionViewModel.cs",
    "PcFutureShield.UI/ViewModels/LicenseManagerViewModel.cs",
    "PcFutureShield.UI/ViewModels/ParentalControlViewModel.cs",
    "PcFutureShield.UI/ViewModels/PcOptimizationViewModel.cs",
    "PcFutureShield.UI/ViewModels/QuarantineViewModel.cs",
    "PcFutureShield.UI/ViewModels/RealtimeProtectionViewModel.cs",
    "PcFutureShield.UI/ViewModels/ScannerViewModel.cs",
    "PcFutureShield.UI/ViewModels/SettingsViewModel.cs",
    "PcFutureShield.UI/ViewModels/ThemeSelectorViewModel.cs",
    "PcFutureShield.UI/ViewModels/UpdatesViewModel.cs",
    "PcFutureShield.UI/ViewModels/VirusScanViewModel.cs"
)

foreach ($file in $viewModelFiles) {
    if (Test-Path $file) {
        Write-Host "Processing $file..."
        
        $content = Get-Content $file -Raw
        
        # Fix class declaration - remove duplicate INotifyPropertyChanged
        $content = $content -replace 'public class (\w+) : BaseViewModel\s*:\s*System\.ComponentModel\.INotifyPropertyChanged\s*\{', 'public class $1 : BaseViewModel
    {'
        
        # Remove duplicate PropertyChanged event and OnPropertyChanged method at the end
        $content = $content -replace '\s*public event System\.ComponentModel\.PropertyChangedEventHandler PropertyChanged;\s*protected void OnPropertyChanged\(string name\) =>\s*PropertyChanged\?\.\Invoke\(this, new System\.ComponentModel\.PropertyChangedEventArgs\(name\)\);\s*\}', '
    }'
        
        # Alternative pattern for duplicate implementations
        $content = $content -replace '\s*public event System\.ComponentModel\.PropertyChangedEventHandler\? PropertyChanged;\s*protected virtual void OnPropertyChanged\(\[CallerMemberName\] string\? propertyName = null\)\s*\{\s*PropertyChanged\?\.\Invoke\(this, new PropertyChangedEventArgs\(propertyName\)\);\s*\}', ''
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed $file"
    } else {
        Write-Host "File not found: $file"
    }
}

Write-Host "All ViewModels processed!"
