using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Linq;
using PcFutureShield.UI.Services;
using PcFutureShield.Common.Interfaces;

namespace PcFutureShield.UI.ViewModels
{
    public class QuarantineViewModel : BaseViewModel
    : System.ComponentModel.INotifyPropertyChanged
{
        private readonly PcFutureShield.Common.Interfaces.IQuarantineManager _quarantineManager;

        public ObservableCollection<PcFutureShield.UI.ViewModels.QuarantinedItem> QuarantinedItems { get; } = new();

        private PcFutureShield.UI.ViewModels.QuarantinedItem? _selectedItem;
        public PcFutureShield.UI.ViewModels.QuarantinedItem? SelectedItem
        {
            get => _selectedItem;
            set => SetProperty(ref _selectedItem, value);
        }

        private string _quarantineStatus = "Ready";
        public string QuarantineStatus
        {
            get => _quarantineStatus;
            set => SetProperty(ref _quarantineStatus, value);
        }

        private int _totalQuarantined;
        public int TotalQuarantined
        {
            get => _totalQuarantined;
            set => SetProperty(ref _totalQuarantined, value);
        }

        public ICommand RestoreCommand { get; }
        public ICommand DeleteCommand { get; }
        public ICommand ViewDetailsCommand { get; }
        public ICommand RefreshCommand { get; }

        public QuarantineViewModel()
        {
            _quarantineManager = ServiceLocator.Get<PcFutureShield.Common.Interfaces.IQuarantineManager>();

            RestoreCommand = new RelayCommand(RestoreSelected, () => SelectedItem != null);
            DeleteCommand = new RelayCommand(DeleteSelected, () => SelectedItem != null);
            ViewDetailsCommand = new RelayCommand(ViewDetails, () => SelectedItem != null);
            RefreshCommand = new RelayCommand(RefreshQuarantine);

            LoadQuarantinedItems();
        }

        private void LoadQuarantinedItems()
        {
            try
            {
                QuarantinedItems.Clear();

                // Load quarantined items from service
                var items = _quarantineManager.GetQuarantinedItems();
                foreach (var item in items)
                {
                    QuarantinedItems.Add(new PcFutureShield.UI.ViewModels.QuarantinedItem
                    {
                        Id = item.Id.ToString(),
                        FileName = System.IO.Path.GetFileName(item.OriginalPath),
                        OriginalPath = item.OriginalPath,
                        QuarantinePath = item.QuarantinePath,
                        ThreatName = item.Reason,
                        DetectionDate = item.Timestamp.DateTime,
                        FileSize = FormatFileSize(item.OriginalSize),
                        RiskLevel = "High" // Could be determined based on threat type
                    });
                }

                TotalQuarantined = QuarantinedItems.Count;
                QuarantineStatus = $"Loaded {TotalQuarantined} quarantined items";
            }
            catch (Exception ex)
            {
                QuarantineStatus = $"Error loading quarantine: {ex.Message}";
            }
        }

        private void RestoreSelected()
        {
            if (SelectedItem == null) return;

            var result = System.Windows.MessageBox.Show(
                $"Restore '{SelectedItem.FileName}' to its original location?\n\nThis may reactivate the threat.",
                "Confirm Restore",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    var restoreResult = _quarantineManager.RestoreItem(SelectedItem.Id);

                    if (restoreResult.Success)
                    {
                        QuarantinedItems.Remove(SelectedItem);
                        TotalQuarantined = QuarantinedItems.Count;
                        QuarantineStatus = $"Restored: {SelectedItem.FileName}";
                        System.Windows.MessageBox.Show("File restored successfully.", "Restore Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show($"Restore failed: {restoreResult.Message}", "Restore Failed", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"Restore error: {ex.Message}", "Restore Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private void DeleteSelected()
        {
            if (SelectedItem == null) return;

            var result = System.Windows.MessageBox.Show(
                $"Permanently delete '{SelectedItem.FileName}'?\n\nThis action cannot be undone.",
                "Confirm Delete",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                try
                {
                    var deleteResult = _quarantineManager.DeleteItem(SelectedItem.Id);

                    if (deleteResult.Success)
                    {
                        QuarantinedItems.Remove(SelectedItem);
                        TotalQuarantined = QuarantinedItems.Count;
                        QuarantineStatus = $"Deleted: {SelectedItem.FileName}";
                        System.Windows.MessageBox.Show("File deleted permanently.", "Delete Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                    }
                    else
                    {
                        System.Windows.MessageBox.Show($"Delete failed: {deleteResult.Message}", "Delete Failed", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    System.Windows.MessageBox.Show($"Delete error: {ex.Message}", "Delete Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private void ViewDetails()
        {
            if (SelectedItem == null) return;

            var details = $"File Details:\n\n" +
                         $"Name: {SelectedItem.FileName}\n" +
                         $"Original Path: {SelectedItem.OriginalPath}\n" +
                         $"Threat: {SelectedItem.ThreatName}\n" +
                         $"Risk Level: {SelectedItem.RiskLevel}\n" +
                         $"Size: {SelectedItem.FileSize}\n" +
                         $"Detection Date: {SelectedItem.DetectionDate:G}";

            System.Windows.MessageBox.Show(details, "Quarantine Item Details", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }

        private void RefreshQuarantine()
        {
            LoadQuarantinedItems();
        }

        private string GetFileSize(string filePath)
        {
            try
            {
                if (System.IO.File.Exists(filePath))
                {
                    var size = new System.IO.FileInfo(filePath).Length;
                    return FormatFileSize(size);
                }
            }
            catch { }

            return "Unknown";
        }

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:F1} {sizes[order]}";
        }
    }

    public class QuarantinedItem
    {
        public string Id { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string OriginalPath { get; set; } = string.Empty;
        public string QuarantinePath { get; set; } = string.Empty;
        public string ThreatName { get; set; } = string.Empty;
        public DateTime DetectionDate { get; set; }
        public string FileSize { get; set; } = string.Empty;
        public string RiskLevel { get; set; } = string.Empty;
    }


		public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
		protected void OnPropertyChanged(string name) =>
			PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(name));
}
