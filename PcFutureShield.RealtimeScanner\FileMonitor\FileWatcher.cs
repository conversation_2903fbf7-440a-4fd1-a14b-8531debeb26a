using System;
using System.Collections.Concurrent;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using PcFutureShield.Engine.Quarantine;
using PcFutureShield.Engine.Scanning;
using PcFutureShield.Engine.VirusScanner;

namespace PcFutureShield.RealtimeScanner.FileMonitor
{
    public sealed class FileWatcher : IAsyncDisposable
    {
        private readonly FileSystemWatcher _watcher;
        private readonly PcFutureShieldScanner _scanner;
        private readonly QuarantineManager _quarantine;
        private readonly BlockingCollection<string> _queue = new(new ConcurrentQueue<string>());
        private readonly CancellationTokenSource _cts = new();
        private readonly Task _worker;

        public event Action<ScanResult>? OnScanned;

        public FileWatcher(string path, PcFutureShieldScanner scanner, QuarantineManager quarantine)
        {
            _scanner = scanner;
            _quarantine = quarantine;
            _watcher = new FileSystemWatcher(path)
            {
                IncludeSubdirectories = true,
                NotifyFilter = NotifyFilters.FileName | NotifyFilters.Size | NotifyFilters.CreationTime | NotifyFilters.LastWrite,
                Filter = "*.*",
                EnableRaisingEvents = true
            };
            _watcher.Created += (_, e) => EnqueueIfFile(e.FullPath);
            _watcher.Changed += (_, e) => EnqueueIfFile(e.FullPath);
            _watcher.Renamed += (_, e) => EnqueueIfFile(e.FullPath);

            _worker = Task.Run(ProcessLoopAsync);
        }

        private void EnqueueIfFile(string path)
        {
            try
            {
                if (File.Exists(path))
                    _queue.Add(path);
            }
            catch { /* swallow noisy race conditions */ }
        }

        private async Task ProcessLoopAsync()
        {
            foreach (var path in _queue.GetConsumingEnumerable(_cts.Token))
            {
                try
                {
                    // debouncing: give the writer a moment to finish without blocking thread pool
                    await Task.Delay(200, _cts.Token).ConfigureAwait(false);

                    // Use scanner's async API which internally offloads CPU-bound work appropriately
                    var result = await _scanner.ScanFileAsync(path).ConfigureAwait(false);

                    try
                    {
                        OnScanned?.Invoke(result);
                    }
                    catch { /* ignore notifier exceptions */ }

                    if (result?.IsMalicious == true)
                    {
                        try { _quarantine.QuarantineFile(path, result.Sha256, result.Reason); } catch { }
                    }
                }
                catch (OperationCanceledException) { break; }
                catch { /* continue */ }
            }
        }

        public async ValueTask DisposeAsync()
        {
            _cts.Cancel();
            _watcher.EnableRaisingEvents = false;
            _watcher.Dispose();
            _queue.CompleteAdding();
            try { await Task.WhenAny(_worker, Task.Delay(2000)).ConfigureAwait(false); } catch { /* ignore */ }
            _cts.Dispose();
            await Task.Yield().ConfigureAwait(false); // Real async: ensure context switch
        }
    }
}
