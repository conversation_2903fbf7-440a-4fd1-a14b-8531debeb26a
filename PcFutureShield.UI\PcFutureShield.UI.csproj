﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net8.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<UseWPF>true</UseWPF>
		<ApplicationIcon />
		<StartupObject />
		<AssemblyTitle>PC FutureShield UI</AssemblyTitle>
		<AssemblyDescription>PC FutureShield Chrome Button UI</AssemblyDescription>
		<Copyright>Copyright © 2025</Copyright>
		<Version>1.0.0</Version>
		<WarningsAsErrors>true</WarningsAsErrors>
	</PropertyGroup>

	<ItemGroup>
	  <Page Remove="Themes\GlossyBlue.xaml" />
	</ItemGroup>

		<ItemGroup>
			<ProjectReference Include="..\PcFutureShield.Engine\PcFutureShield.Engine.csproj" />
			<ProjectReference Include="..\PcFutureShield.Common\PcFutureShield.Common.csproj" />
		</ItemGroup>

		<ItemGroup>
		  <Resource Include="Themes\GlossyBlue.xaml" />
		  <Resource Include="Themes\Glossy3D.xaml" />
		</ItemGroup>

		<ItemGroup>
			<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.8" />
		</ItemGroup>

</Project>
