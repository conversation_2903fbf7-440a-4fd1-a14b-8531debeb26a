using System;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace PcFutureShield.RealtimeScanner
{
    public static class Program
    {
        public static int Main(string[] args)
        {
            return CreateHostBuilder(args).Build().RunWithExitCode();
        }

        private static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService() // allows running as a Windows service when installed
                .ConfigureAppConfiguration((ctx, cfg) =>
                {
                    cfg.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                       .AddEnvironmentVariables()
                       .AddCommandLine(args);
                })
                .ConfigureServices((hostContext, services) =>
                {
                    IConfiguration config = hostContext.Configuration;
                    services.AddLogging();
                    services.Configure<RealtimeScannerOptions>(config.GetSection("RealtimeScanner"));
                    services.AddHostedService<Worker>();
                });
    }

    internal static class HostExtensions
    {
        public static int RunWithExitCode(this IHost host)
        {
            try
            {
                host.Run();
                return 0;
            }
            catch (Exception ex)
            {
                var logger = host.Services.GetService<ILogger<Program>>();
                logger?.LogCritical(ex, "Host terminated unexpectedly");
                return -1;
            }
        }
    }
}
