using System;
using System.Windows;

namespace PcFutureShield.UI.Services
{
    public class WpfNotificationService : INotificationService
    {
        public void ShowInfo(string title, string message)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
            });
        }

        public void ShowWarning(string title, string message)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
            });
        }

        public void ShowError(string title, string message)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
            });
        }

        public void ShowToast(string message, TimeSpan? duration = null)
        {
            // Minimal toast: use non-blocking info box window or a lightweight popup.
            // For now use MessageBox but do not block main thread.
            Application.Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                var wnd = new Window
                {
                    Width = 300,
                    Height = 80,
                    WindowStyle = WindowStyle.None,
                    AllowsTransparency = true,
                    Background = System.Windows.Media.Brushes.Transparent,
                    Content = new System.Windows.Controls.Border
                    {
                        Background = System.Windows.Media.Brushes.DimGray,
                        CornerRadius = new System.Windows.CornerRadius(6),
                        Padding = new Thickness(10),
                        Child = new System.Windows.Controls.TextBlock
                        {
                            Text = message,
                            Foreground = System.Windows.Media.Brushes.White
                        }
                    },
                    Topmost = true,
                    ShowInTaskbar = false
                };

                wnd.Loaded += (s, e) =>
                {
                    var closeAfter = duration ?? TimeSpan.FromSeconds(3);
                    var t = new System.Timers.Timer(closeAfter.TotalMilliseconds) { AutoReset = false };
                    t.Elapsed += (ss, ee) => Application.Current.Dispatcher.Invoke(() => wnd.Close());
                    t.Start();
                };

                wnd.Show();
            }));
        }
    }
}
