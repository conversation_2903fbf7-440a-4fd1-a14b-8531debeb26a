# Test script for PcFutureShield Configurable Blocking Levels
# This script helps test the new blocking level system

Write-Host "=== PcFutureShield Blocking Level Test Script ===" -ForegroundColor Cyan
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "WARNING: Not running as Administrator!" -ForegroundColor Yellow
    Write-Host "Some features may not work properly without admin privileges." -ForegroundColor Yellow
    Write-Host ""
}

# Function to test DNS resolution
function Test-DnsBlocking {
    param(
        [string]$Domain,
        [string]$Description
    )

    Write-Host "Testing $Description ($Domain)..." -NoNewline

    try {
        $result = Resolve-DnsName -Name $Domain -ErrorAction Stop
        if ($result) {
            Write-Host " ✅ ACCESSIBLE" -ForegroundColor Green
            return $false
        }
    }
    catch {
        Write-Host " ❌ BLOCKED" -ForegroundColor Red
        return $true
    }

    return $false
}

# Function to test web connectivity
function Test-WebBlocking {
    param(
        [string]$Url,
        [string]$Description
    )

    Write-Host "Testing $Description ($Url)..." -NoNewline

    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec 5 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Write-Host " ✅ ACCESSIBLE" -ForegroundColor Green
            return $false
        }
    }
    catch {
        Write-Host " ❌ BLOCKED" -ForegroundColor Red
        return $true
    }

    return $false
}

# Test domains for different categories
$testDomains = @{
    "Adult Content" = @("pornhub.com", "xvideos.com", "redtube.com")
    "Gambling" = @("bet365.com", "pokerstars.com", "casino.com")
    "Social Media" = @("facebook.com", "twitter.com", "instagram.com")
    "Violence/Weapons" = @("gunbroker.com", "armslist.com")
    "Safe Sites" = @("google.com", "microsoft.com", "wikipedia.org")
}

Write-Host "🔍 Testing Current Blocking Status..." -ForegroundColor Cyan
Write-Host ""

# Test each category
foreach ($category in $testDomains.Keys) {
    Write-Host "📂 Category: $category" -ForegroundColor Yellow

    $blockedCount = 0
    $totalCount = $testDomains[$category].Count

    foreach ($domain in $testDomains[$category]) {
        $isBlocked = Test-DnsBlocking -Domain $domain -Description "DNS"
        if ($isBlocked) {
            $blockedCount++
        }
        Start-Sleep -Milliseconds 500
    }

    $blockingRate = [math]::Round(($blockedCount / $totalCount) * 100, 1)
    Write-Host "   Blocking Rate: $blockedCount/$totalCount ($blockingRate%)" -ForegroundColor Magenta
    Write-Host ""
}

Write-Host "🌐 Testing Safe Sites (should always be accessible)..." -ForegroundColor Cyan
Write-Host ""

$safeBlocked = 0
foreach ($domain in $testDomains["Safe Sites"]) {
    $isBlocked = Test-DnsBlocking -Domain $domain -Description "Safe Site"
    if ($isBlocked) {
        $safeBlocked++
    }
    Start-Sleep -Milliseconds 500
}

if ($safeBlocked -gt 0) {
    Write-Host "⚠️  WARNING: $safeBlocked safe sites are blocked! This may indicate over-blocking." -ForegroundColor Yellow
} else {
    Write-Host "✅ All safe sites are accessible." -ForegroundColor Green
}

Write-Host ""
Write-Host "📊 Test Summary:" -ForegroundColor Cyan
Write-Host "   - Adult Content: Check if blocked based on current level"
Write-Host "   - Gambling: Should be blocked at Medium+ levels"
Write-Host "   - Social Media: Should be blocked at High+ levels"
Write-Host "   - Safe Sites: Should always be accessible"
Write-Host ""

# Check hosts file for PcFutureShield entries
Write-Host "🔧 Checking hosts file..." -ForegroundColor Cyan
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"
$pcfsEntries = Get-Content $hostsPath | Select-String "PcFutureShield"

if ($pcfsEntries) {
    Write-Host "   Found $($pcfsEntries.Count) PcFutureShield entries in hosts file" -ForegroundColor Yellow
    Write-Host "   First few entries:"
    $pcfsEntries | Select-Object -First 5 | ForEach-Object {
        Write-Host "     $_" -ForegroundColor Gray
    }
} else {
    Write-Host "   No PcFutureShield entries found in hosts file" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎯 Quick Actions:" -ForegroundColor Cyan
Write-Host "   To test different blocking levels, use the PcFutureShield UI:"
Write-Host "   1. Open PcFutureShield application"
Write-Host "   2. Go to Parental Control tab"
Write-Host "   3. Use the 'Protection Level' dropdown or Quick Actions buttons"
Write-Host "   4. Run this script again to see the changes"
Write-Host ""
Write-Host "   Emergency Disable: Use the red 'Emergency Disable' button"
Write-Host "   Re-Enable: Use the green 'Re-Enable' button when in emergency mode"
Write-Host ""

Write-Host "Test completed! Press any key to exit..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
