using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace PcFutureShield.UI.ViewModels
{
    public class BlockedContent : INotifyPropertyChanged
    {
        private string _contentId = string.Empty;
        private string _url = string.Empty;
        private string _category = string.Empty;
        private DateTime _blockedTime;
        private string _userProfile = string.Empty;
        private string _reason = string.Empty;

        public string ContentId
        {
            get => _contentId;
            set
            {
                if (_contentId != value)
                {
                    _contentId = value;
                    OnPropertyChanged(nameof(ContentId));
                }
            }
        }

        public string Url
        {
            get => _url;
            set
            {
                if (_url != value)
                {
                    _url = value;
                    OnPropertyChanged(nameof(Url));
                }
            }
        }

        public string Category
        {
            get => _category;
            set
            {
                if (_category != value)
                {
                    _category = value;
                    OnPropertyChanged(nameof(Category));
                }
            }
        }

        public DateTime BlockedTime
        {
            get => _blockedTime;
            set
            {
                if (_blockedTime != value)
                {
                    _blockedTime = value;
                    OnPropertyChanged(nameof(BlockedTime));
                }
            }
        }

        public string UserProfile
        {
            get => _userProfile;
            set
            {
                if (_userProfile != value)
                {
                    _userProfile = value;
                    OnPropertyChanged(nameof(UserProfile));
                }
            }
        }

        public string Reason
        {
            get => _reason;
            set
            {
                if (_reason != value)
                {
                    _reason = value;
                    OnPropertyChanged(nameof(Reason));
                }
            }
        }

        private string _content = string.Empty;
        public string Content
        {
            get => _content;
            set
            {
                if (_content != value)
                {
                    _content = value;
                    OnPropertyChanged(nameof(Content));
                }
            }
        }

        private string _userName = string.Empty;
        public string UserName
        {
            get => _userName;
            set
            {
                if (_userName != value)
                {
                    _userName = value;
                    OnPropertyChanged(nameof(UserName));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class UserProfile : INotifyPropertyChanged
    {
        private string _profileId = string.Empty;
        private string _name = string.Empty;
        private int _age;
        private string _restrictions = string.Empty;
        private TimeSpan _screenTimeLimit;
        private bool _isActive;

        public string ProfileId
        {
            get => _profileId;
            set
            {
                if (_profileId != value)
                {
                    _profileId = value;
                    OnPropertyChanged(nameof(ProfileId));
                }
            }
        }

        public string Name
        {
            get => _name;
            set
            {
                if (_name != value)
                {
                    _name = value;
                    OnPropertyChanged(nameof(Name));
                }
            }
        }

        public int Age
        {
            get => _age;
            set
            {
                if (_age != value)
                {
                    _age = value;
                    OnPropertyChanged(nameof(Age));
                }
            }
        }

        public string Restrictions
        {
            get => _restrictions;
            set
            {
                if (_restrictions != value)
                {
                    _restrictions = value;
                    OnPropertyChanged(nameof(Restrictions));
                }
            }
        }

        public TimeSpan ScreenTimeLimit
        {
            get => _screenTimeLimit;
            set
            {
                if (_screenTimeLimit != value)
                {
                    _screenTimeLimit = value;
                    OnPropertyChanged(nameof(ScreenTimeLimit));
                }
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class ContentFilter : INotifyPropertyChanged
    {
        private string _category = string.Empty;
        private bool _isBlocked;
        private List<string> _blockedSites = new List<string>();

        public string Category
        {
            get => _category;
            set
            {
                if (_category != value)
                {
                    _category = value;
                    OnPropertyChanged(nameof(Category));
                }
            }
        }

        public bool IsBlocked
        {
            get => _isBlocked;
            set
            {
                if (_isBlocked != value)
                {
                    _isBlocked = value;
                    OnPropertyChanged(nameof(IsBlocked));
                }
            }
        }

        public List<string> BlockedSites
        {
            get => _blockedSites;
            set
            {
                if (_blockedSites != value)
                {
                    _blockedSites = value;
                    OnPropertyChanged(nameof(BlockedSites));
                }
            }
        }

        public List<string> Keywords
        {
            get => _blockedSites;
            set
            {
                if (_blockedSites != value)
                {
                    _blockedSites = value;
                    OnPropertyChanged(nameof(Keywords));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}
