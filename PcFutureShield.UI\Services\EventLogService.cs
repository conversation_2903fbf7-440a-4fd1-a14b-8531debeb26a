using System;
using System.Collections.Generic;
using System.IO;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Services
{
    public class EventLogService : IEventLogService
    {
        private readonly List<RealtimeEvent> _events = new();
        public IEnumerable<RealtimeEvent> GetRecentEvents() => _events;
        public void AddEvent(RealtimeEvent evt)
        {
            _events.Add(evt);
            if (_events.Count > 100) _events.RemoveAt(0);
        }
        public void ShowEventLogWindow()
        {
            var window = new PcFutureShield.UI.Views.EventLogWindow();
            window.Show();
        }

        public void ClearEvents()
        {
            _events.Clear();
        }

        public string ExportEvents()
        {
            try
            {
                var exportPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                    $"PcFutureShield_Events_{DateTime.Now:yyyyMMdd_HHmmss}.txt");

                var exportContent = "PcFutureShield Event Log Export\n";
                exportContent += $"Generated: {DateTime.Now:G}\n\n";

                foreach (var evt in _events)
                {
                    exportContent += $"[{evt.Time:G}] [{evt.Severity}] {evt.Event}";
                    if (!string.IsNullOrEmpty(evt.File))
                        exportContent += $" - {evt.File}";
                    exportContent += "\n";
                }

                File.WriteAllText(exportPath, exportContent);
                return exportPath;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to export events: {ex.Message}", ex);
            }
        }
    }
}
