using System;
using System.Threading.Tasks;
using PcFutureShield.Common.Services;

namespace PcFutureShield.Testing
{
    /// <summary>
    /// Test application to verify the new configurable blocking level system
    /// </summary>
    class TestBlockingLevels
    {
        private static ParentalControlService _parentalControl;
        private static DnsFilteringService _dnsFilter;

        static async Task Main(string[] args)
        {
            Console.WriteLine("=== PcFutureShield Configurable Blocking Level Test ===\n");

            // Check if running as administrator
            if (!IsRunningAsAdministrator())
            {
                Console.WriteLine("⚠️  WARNING: Not running as Administrator!");
                Console.WriteLine("Some features may not work properly without admin privileges.\n");
            }

            _parentalControl = new ParentalControlService();
            _dnsFilter = new DnsFilteringService();

            try
            {
                await RunBlockingLevelTests();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed with error: {ex.Message}");
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        static async Task RunBlockingLevelTests()
        {
            Console.WriteLine("🔧 Testing Configurable Blocking Levels...\n");

            // Test 1: Check current blocking level
            Console.WriteLine("1. Checking current blocking level...");
            var currentLevel = _parentalControl.GetCurrentBlockingLevel();
            Console.WriteLine($"   Current level: {currentLevel}");
            Console.WriteLine($"   Emergency disabled: {_parentalControl.IsEmergencyDisabled()}\n");

            // Test 2: Test each blocking level
            var levels = new[] { BlockingLevel.Off, BlockingLevel.Low, BlockingLevel.Medium, BlockingLevel.High, BlockingLevel.Maximum };
            
            foreach (var level in levels)
            {
                Console.WriteLine($"2. Testing blocking level: {level}");
                
                var success = await _parentalControl.SetBlockingLevelAsync(level);
                if (success)
                {
                    Console.WriteLine($"   ✅ Successfully set to {level}");
                    
                    // Verify the level was set
                    var verifyLevel = _parentalControl.GetCurrentBlockingLevel();
                    if (verifyLevel == level)
                    {
                        Console.WriteLine($"   ✅ Level verified: {verifyLevel}");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ Level mismatch! Expected: {level}, Got: {verifyLevel}");
                    }
                }
                else
                {
                    Console.WriteLine($"   ❌ Failed to set level to {level}");
                }
                
                // Small delay between tests
                await Task.Delay(1000);
                Console.WriteLine();
            }

            // Test 3: Emergency disable
            Console.WriteLine("3. Testing Emergency Disable...");
            var emergencySuccess = await _parentalControl.EmergencyDisableAsync();
            if (emergencySuccess)
            {
                Console.WriteLine("   ✅ Emergency disable successful");
                Console.WriteLine($"   Emergency mode active: {_parentalControl.IsEmergencyDisabled()}");
            }
            else
            {
                Console.WriteLine("   ❌ Emergency disable failed");
            }
            
            await Task.Delay(2000);
            Console.WriteLine();

            // Test 4: Re-enable after emergency
            Console.WriteLine("4. Testing Re-enable after Emergency...");
            var reEnableSuccess = await _parentalControl.ReEnableAfterEmergencyAsync(BlockingLevel.Medium);
            if (reEnableSuccess)
            {
                Console.WriteLine("   ✅ Re-enable successful");
                Console.WriteLine($"   Current level: {_parentalControl.GetCurrentBlockingLevel()}");
                Console.WriteLine($"   Emergency mode active: {_parentalControl.IsEmergencyDisabled()}");
            }
            else
            {
                Console.WriteLine("   ❌ Re-enable failed");
            }
            
            await Task.Delay(1000);
            Console.WriteLine();

            // Test 5: Test DNS filtering status
            Console.WriteLine("5. Testing DNS Filtering Status...");
            var isFilteringActive = await _dnsFilter.IsFilteringActiveAsync();
            Console.WriteLine($"   DNS filtering active: {isFilteringActive}");
            
            // Test 6: Test category blocking based on level
            Console.WriteLine("\n6. Testing Category Blocking...");
            await TestCategoryBlocking();

            Console.WriteLine("\n🎉 All blocking level tests completed!");
            Console.WriteLine("\n📋 Summary:");
            Console.WriteLine($"   Final blocking level: {_parentalControl.GetCurrentBlockingLevel()}");
            Console.WriteLine($"   Emergency mode: {(_parentalControl.IsEmergencyDisabled() ? "ACTIVE" : "INACTIVE")}");
            Console.WriteLine($"   DNS filtering: {(await _dnsFilter.IsFilteringActiveAsync() ? "ACTIVE" : "INACTIVE")}");
        }

        static async Task TestCategoryBlocking()
        {
            var testCategories = new[] { "adult", "gambling", "violence", "social" };
            
            foreach (var category in testCategories)
            {
                Console.WriteLine($"   Testing category: {category}");
                var blockSuccess = await _parentalControl.BlockCategoryAsync(category);
                Console.WriteLine($"   Block {category}: {(blockSuccess ? "✅" : "❌")}");
                
                await Task.Delay(500);
                
                var unblockSuccess = await _parentalControl.UnblockCategoryAsync(category);
                Console.WriteLine($"   Unblock {category}: {(unblockSuccess ? "✅" : "❌")}");
            }
        }

        static bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
