using System;
using System.Collections.Generic;
using System.IO;
using PcFutureShield.Engine.Scanning;
using HashingSvc = PcFutureShield.Common.Services.HashingService;

namespace PcFutureShield.Engine.VirusScanner
{
    public sealed class PcFutureShieldScanner
    {
        private readonly SignatureDatabase _signatures;
        private static readonly HashSet<string> SuspiciousExt = new(StringComparer.OrdinalIgnoreCase)
        { ".exe", ".dll", ".scr", ".ps1", ".js", ".vbs", ".jar", ".bat", ".cmd", ".hta", ".msi" };

        public PcFutureShieldScanner(SignatureDatabase signatures) => _signatures = signatures;

        public ScanResult ScanFile(string filePath)
        {
            if (!File.Exists(filePath)) throw new FileNotFoundException("Target not found", filePath);

            // Hash & entropy are real I/O work
            var sha256 = HashingSvc.ComputeSHA256(filePath);
            var entropy = HashingSvc.ComputeFileEntropy(filePath);

            // 1) Signature match
            string? family = null;
            try
            {
                family = _signatures.GetThreatNameAsync(sha256).GetAwaiter().GetResult();
            }
            catch { family = null; }
            if (!string.IsNullOrEmpty(family))
                return new ScanResult(filePath, sha256, true, $"Signature:{family}", entropy);

            // 2) Heuristics
            var reasons = new List<string>();
            var ext = Path.GetExtension(filePath);
            if (SuspiciousExt.Contains(ext)) reasons.Add($"SuspiciousExt:{ext}");
            if (HashingSvc.LooksLikePortableExecutable(filePath)) reasons.Add("MZHeader");
            if (entropy > 7.2) reasons.Add($"HighEntropy:{entropy:F2}");

            // Check if file is under known risk directories (e.g., Downloads, AppData, Temp)
            var inRiskDir = IsUnderKnownRiskDirs(filePath);
            if (inRiskDir) reasons.Add("RiskPath");

            var isMal = reasons.Count >= 2; // threshold
            var reason = reasons.Count == 0 ? "Clean" : string.Join("|", reasons);
            return new ScanResult(filePath, sha256, isMal, reason, entropy);
        }

        public Task<ScanResult> ScanFileAsync(string filePath, System.Threading.CancellationToken ct = default)
        {
            // Offload the CPU-bound scan to a dedicated thread to avoid thread-pool starvation on large workloads.
            // Use Task.Factory.StartNew with LongRunning so the runtime may schedule appropriately.
            return Task.Factory.StartNew(() =>
            {
                ct.ThrowIfCancellationRequested();
                return ScanFile(filePath);
            }, ct, TaskCreationOptions.LongRunning, TaskScheduler.Default);
        }

        private static bool IsUnderKnownRiskDirs(string path)
        {
            var p = Path.GetFullPath(path);
            string tempDir = Path.GetTempPath().TrimEnd(Path.DirectorySeparatorChar);
            string downloadsDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), "Downloads");
            string appDataTemp = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Temp");
            string inetCache = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Microsoft", "Windows", "INetCache");
            return p.StartsWith(tempDir, StringComparison.OrdinalIgnoreCase)
                || p.StartsWith(downloadsDir, StringComparison.OrdinalIgnoreCase)
                || p.StartsWith(appDataTemp, StringComparison.OrdinalIgnoreCase)
                || p.StartsWith(inetCache, StringComparison.OrdinalIgnoreCase);
        }
    }
}
