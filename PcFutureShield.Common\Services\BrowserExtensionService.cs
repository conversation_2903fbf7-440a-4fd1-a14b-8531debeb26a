#nullable enable

using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Text.RegularExpressions;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Browser extension service for real-time web protection
    /// </summary>
    public class BrowserExtensionService
    {
        private readonly HttpClient _httpClient;
        private readonly string _extensionDbPath;
        private readonly Dictionary<string, BrowserProfile> _browserProfiles;
        private readonly WebFilterEngine _webFilter;
        private readonly PhishingDetector _phishingDetector;
        private readonly MalwareSiteDetector _malwareDetector;

        public BrowserExtensionService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(5);

            var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
            var extensionPath = Path.Combine(appData, "PcFutureShield", "BrowserExtension");
            Directory.CreateDirectory(extensionPath);
            _extensionDbPath = Path.Combine(extensionPath, "browser_data.json");

            _browserProfiles = LoadBrowserProfiles();
            _webFilter = new WebFilterEngine();
            _phishingDetector = new PhishingDetector();
            _malwareDetector = new MalwareSiteDetector();
        }

        public async Task<UrlAnalysisResult> AnalyzeUrlAsync(string url, string browserType, string userId)
        {
            var result = new UrlAnalysisResult
            {
                Url = url,
                BrowserType = browserType,
                UserId = userId,
                AnalysisTime = DateTime.UtcNow
            };

            try
            {
                // Get or create browser profile
                var profile = GetOrCreateBrowserProfile(browserType, userId);

                // Parallel analysis
                var filterTask = _webFilter.CheckUrlAsync(url, profile);
                var phishingTask = _phishingDetector.AnalyzeUrlAsync(url);
                var malwareTask = _malwareDetector.CheckMalwareAsync(url);

                await Task.WhenAll(filterTask, phishingTask, malwareTask);

                result.FilterResult = await filterTask;
                result.PhishingResult = await phishingTask;
                result.MalwareResult = await malwareTask;

                // Overall risk assessment
                result.OverallRisk = CalculateOverallRisk(result);
                result.ShouldBlock = ShouldBlockUrl(result, profile);
                result.Recommendations = GenerateUrlRecommendations(result);

                // Update profile
                UpdateBrowserProfile(profile, result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.ShouldBlock = true; // Default to blocking on error
            }

            return result;
        }

        public async Task<ContentAnalysisResult> AnalyzePageContentAsync(string url, string content, string browserType, string userId)
        {
            var result = new ContentAnalysisResult
            {
                Url = url,
                BrowserType = browserType,
                UserId = userId,
                AnalysisTime = DateTime.UtcNow
            };

            try
            {
                var profile = GetOrCreateBrowserProfile(browserType, userId);

                // Analyze content for various threats
                var contentAnalysis = await AnalyzeContentAsync(content, profile);
                result.ContentAnalysis = contentAnalysis;

                // Check for malicious scripts
                result.ScriptAnalysis = await AnalyzeScriptsAsync(content);

                // Check for suspicious forms
                result.FormAnalysis = await AnalyzeFormsAsync(content);

                // Overall content risk
                result.OverallRisk = CalculateContentRisk(result);
                result.ShouldBlock = ShouldBlockContent(result, profile);
                result.Recommendations = GenerateContentRecommendations(result);

                // Update profile
                UpdateBrowserProfileWithContent(profile, result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.ShouldBlock = true;
            }

            return result;
        }

        public async Task<DownloadAnalysisResult> AnalyzeDownloadAsync(string url, string filename, long fileSize, string browserType, string userId)
        {
            var result = new DownloadAnalysisResult
            {
                Url = url,
                Filename = filename,
                FileSize = fileSize,
                BrowserType = browserType,
                UserId = userId,
                AnalysisTime = DateTime.UtcNow
            };

            try
            {
                var profile = GetOrCreateBrowserProfile(browserType, userId);

                // Check file extension
                result.ExtensionRisk = AnalyzeFileExtension(filename);

                // Check file size
                result.SizeRisk = AnalyzeFileSize(fileSize);

                // Check download source
                result.SourceRisk = await AnalyzeDownloadSourceAsync(url);

                // Overall download risk
                result.OverallRisk = CalculateDownloadRisk(result);
                result.ShouldBlock = ShouldBlockDownload(result, profile);
                result.Recommendations = GenerateDownloadRecommendations(result);

                // Update profile
                UpdateBrowserProfileWithDownload(profile, result);

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.ShouldBlock = true;
            }

            return result;
        }

        public void UpdateBrowserSettings(string browserType, string userId, BrowserSettings settings)
        {
            var profile = GetOrCreateBrowserProfile(browserType, userId);
            profile.Settings = settings;
            SaveBrowserProfiles();
        }

        public async Task UpdateBrowserSettingsAsync(string browserType, string userId, BrowserSettings settings)
        {
            var profile = GetOrCreateBrowserProfile(browserType, userId);
            profile.Settings = settings;
            await SaveBrowserProfilesAsync().ConfigureAwait(false);
        }

        public BrowserSettings GetBrowserSettings(string browserType, string userId)
        {
            var profile = GetOrCreateBrowserProfile(browserType, userId);
            return profile.Settings;
        }

        public List<BrowserAlert> GetRecentAlerts(string browserType, string userId, int count = 10)
        {
            var profile = GetOrCreateBrowserProfile(browserType, userId);
            return profile.RecentAlerts
                .OrderByDescending(a => a.Timestamp)
                .Take(count)
                .ToList();
        }

        public async Task<ExtensionHealthResult> CheckExtensionHealthAsync()
        {
            var result = new ExtensionHealthResult();

            try
            {
                // Check if extension is properly installed
                result.IsInstalled = await CheckExtensionInstallationAsync();

                // Check extension version
                result.Version = await GetExtensionVersionAsync();

                // Check for updates
                result.UpdateAvailable = await CheckForUpdatesAsync();

                // Check permissions
                result.PermissionsValid = await ValidatePermissionsAsync();

                // Overall health
                result.IsHealthy = result.IsInstalled && result.PermissionsValid && !result.UpdateAvailable;

            }
            catch (Exception ex)
            {
                result.Error = ex.Message;
                result.IsHealthy = false;
            }

            return result;
        }

        private async Task<ContentAnalysis> AnalyzeContentAsync(string content, BrowserProfile profile)
        {
            await Task.Yield();
            var analysis = new ContentAnalysis();

            try
            {
                // Check for malicious keywords
                analysis.MaliciousKeywords = DetectMaliciousKeywords(content);

                // Check for phishing indicators
                analysis.PhishingIndicators = DetectPhishingIndicators(content);

                // Check for inappropriate content
                analysis.InappropriateContent = DetectInappropriateContent(content, profile);

                // Calculate content risk score
                analysis.RiskScore = CalculateContentRiskScore(analysis);

            }
            catch (Exception ex)
            {
                analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<ScriptAnalysis> AnalyzeScriptsAsync(string content)
        {
            await Task.Yield();
            var analysis = new ScriptAnalysis();

            try
            {
                // Extract and analyze scripts
                var scripts = ExtractScripts(content);
                analysis.ScriptCount = scripts.Count;

                foreach (var script in scripts)
                {
                    if (DetectMaliciousScript(script))
                    {
                        analysis.MaliciousScripts++;
                        analysis.SuspiciousPatterns.Add("Malicious script detected");
                    }
                }

                analysis.RiskLevel = analysis.MaliciousScripts > 0 ? RiskLevel.High : RiskLevel.Safe;

            }
            catch (Exception ex)
            {
                analysis.Error = ex.Message;
            }

            return analysis;
        }

        private async Task<FormAnalysis> AnalyzeFormsAsync(string content)
        {
            await Task.Yield();
            var analysis = new FormAnalysis();

            try
            {
                // Analyze forms for suspicious patterns
                var forms = ExtractForms(content);
                analysis.FormCount = forms.Count;

                foreach (var form in forms)
                {
                    if (IsSuspiciousForm(form))
                    {
                        analysis.SuspiciousForms++;
                        analysis.RiskFactors.Add("Suspicious form detected");
                    }
                }

            }
            catch (Exception ex)
            {
                analysis.Error = ex.Message;
            }

            return analysis;
        }

        private RiskLevel AnalyzeFileExtension(string filename)
        {
            var extension = Path.GetExtension(filename).ToLowerInvariant();

            var highRiskExtensions = new[] { ".exe", ".bat", ".cmd", ".scr", ".pif", ".com" };
            var mediumRiskExtensions = new[] { ".zip", ".rar", ".7z", ".msi", ".jar" };

            if (highRiskExtensions.Contains(extension)) return RiskLevel.Critical;
            if (mediumRiskExtensions.Contains(extension)) return RiskLevel.Medium;

            return RiskLevel.Safe;
        }

        private RiskLevel AnalyzeFileSize(long fileSize)
        {
            // Very large files might be suspicious
            if (fileSize > 100 * 1024 * 1024) return RiskLevel.Medium; // > 100MB
            if (fileSize > 500 * 1024 * 1024) return RiskLevel.High; // > 500MB

            return RiskLevel.Safe;
        }

        private async Task<RiskLevel> AnalyzeDownloadSourceAsync(string url)
        {
            await Task.Yield();
            try
            {
                var uri = new Uri(url);
                var domain = uri.Host.ToLowerInvariant();

                // Check against known suspicious domains
                var suspiciousDomains = new[] { "free-download", "crack", "keygen", "warez" };

                if (suspiciousDomains.Any(domain.Contains))
                    return RiskLevel.High;

                // Check HTTPS
                if (uri.Scheme != "https")
                    return RiskLevel.Medium;

                return RiskLevel.Safe;

            }
            catch
            {
                return RiskLevel.Medium;
            }
        }

        private List<string> DetectMaliciousKeywords(string content)
        {
            var keywords = new[]
            {
                "password", "credit card", "social security", "bank account",
                "hack", "exploit", "malware", "virus", "trojan"
            };

            return keywords.Where(k => content.Contains(k, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        private List<string> DetectPhishingIndicators(string content)
        {
            var indicators = new List<string>();

            // Check for urgent language
            if (Regex.IsMatch(content, @"(urgent|immediate|action required|account suspended)", RegexOptions.IgnoreCase))
                indicators.Add("Urgent language detected");

            // Check for suspicious links
            if (Regex.IsMatch(content, @"https?://[^\s]*\.[^\s]*\.[^\s]*", RegexOptions.IgnoreCase))
                indicators.Add("Multiple suspicious links");

            return indicators;
        }

        private List<string> DetectInappropriateContent(string content, BrowserProfile profile)
        {
            var inappropriate = new List<string>();

            if (profile.Settings.BlockAdultContent)
            {
                var adultKeywords = new[] { "porn", "adult", "xxx", "sex" };
                if (adultKeywords.Any(k => content.Contains(k, StringComparison.OrdinalIgnoreCase)))
                    inappropriate.Add("Adult content detected");
            }

            if (profile.Settings.BlockViolence)
            {
                var violenceKeywords = new[] { "violence", "gore", "kill", "murder" };
                if (violenceKeywords.Any(k => content.Contains(k, StringComparison.OrdinalIgnoreCase)))
                    inappropriate.Add("Violent content detected");
            }

            return inappropriate;
        }

        private List<string> ExtractScripts(string content)
        {
            var scripts = new List<string>();
            var scriptMatches = Regex.Matches(content, @"<script[^>]*>(.*?)</script>", RegexOptions.Singleline | RegexOptions.IgnoreCase);

            foreach (Match match in scriptMatches)
            {
                scripts.Add(match.Groups[1].Value);
            }

            return scripts;
        }

        private List<string> ExtractForms(string content)
        {
            var forms = new List<string>();
            var formMatches = Regex.Matches(content, @"<form[^>]*>(.*?)</form>", RegexOptions.Singleline | RegexOptions.IgnoreCase);

            foreach (Match match in formMatches)
            {
                forms.Add(match.Value);
            }

            return forms;
        }

        private bool DetectMaliciousScript(string script)
        {
            var maliciousPatterns = new[]
            {
                @"eval\s*\(", @"document\.write\s*\(", @"innerHTML\s*=",
                @"location\.href", @"window\.open", @"setTimeout.*eval"
            };

            return maliciousPatterns.Any(p => Regex.IsMatch(script, p, RegexOptions.IgnoreCase));
        }

        private bool IsSuspiciousForm(string form)
        {
            // Check for password fields without HTTPS
            return Regex.IsMatch(form, @"type\s*=\s*['""]password['""]", RegexOptions.IgnoreCase) &&
                   !Regex.IsMatch(form, @"https://", RegexOptions.IgnoreCase);
        }

        private RiskLevel CalculateOverallRisk(UrlAnalysisResult result)
        {
            var risks = new[] { result.FilterResult.RiskLevel, result.PhishingResult.RiskLevel, result.MalwareResult.RiskLevel };
            var maxRisk = risks.Max();

            return maxRisk;
        }

        private RiskLevel CalculateContentRisk(ContentAnalysisResult result)
        {
            if (result.ContentAnalysis.RiskScore > 0.8) return RiskLevel.Critical;
            if (result.ContentAnalysis.RiskScore > 0.6) return RiskLevel.High;
            if (result.ContentAnalysis.RiskScore > 0.4) return RiskLevel.Medium;
            if (result.ContentAnalysis.RiskScore > 0.2) return RiskLevel.Low;

            return RiskLevel.Safe;
        }

        private RiskLevel CalculateDownloadRisk(DownloadAnalysisResult result)
        {
            var risks = new[] { result.ExtensionRisk, result.SizeRisk, result.SourceRisk };
            var maxRisk = risks.Max();

            return maxRisk;
        }

        private double CalculateContentRiskScore(ContentAnalysis analysis)
        {
            var score = 0.0;

            score += analysis.MaliciousKeywords.Count * 0.2;
            score += analysis.PhishingIndicators.Count * 0.3;
            score += analysis.InappropriateContent.Count * 0.1;

            return Math.Min(score, 1.0);
        }

        private bool ShouldBlockUrl(UrlAnalysisResult result, BrowserProfile profile)
        {
            if (result.OverallRisk >= RiskLevel.Critical) return true;
            if (result.OverallRisk >= RiskLevel.High && profile.Settings.BlockHighRiskSites) return true;
            if (result.PhishingResult.RiskLevel >= RiskLevel.Medium) return true;
            if (result.MalwareResult.RiskLevel >= RiskLevel.Medium) return true;

            return false;
        }

        private bool ShouldBlockContent(ContentAnalysisResult result, BrowserProfile profile)
        {
            if (result.OverallRisk >= RiskLevel.Critical) return true;
            if (result.ScriptAnalysis.RiskLevel >= RiskLevel.High) return true;
            if (result.ContentAnalysis.InappropriateContent.Count > 0 && profile.Settings.StrictContentFilter) return true;

            return false;
        }

        private bool ShouldBlockDownload(DownloadAnalysisResult result, BrowserProfile profile)
        {
            if (result.OverallRisk >= RiskLevel.Critical) return true;
            if (result.ExtensionRisk >= RiskLevel.High && profile.Settings.BlockExecutableDownloads) return true;
            if (result.SourceRisk >= RiskLevel.High) return true;

            return false;
        }

        private List<string> GenerateUrlRecommendations(UrlAnalysisResult result)
        {
            var recommendations = new List<string>();

            if (result.ShouldBlock)
                recommendations.Add("Access to this site has been blocked for security reasons");

            if (result.PhishingResult.RiskLevel >= RiskLevel.Medium)
                recommendations.Add("This site appears to be a phishing attempt - do not enter personal information");

            if (result.MalwareResult.RiskLevel >= RiskLevel.Medium)
                recommendations.Add("This site may contain malware - exercise caution");

            return recommendations;
        }

        private List<string> GenerateContentRecommendations(ContentAnalysisResult result)
        {
            var recommendations = new List<string>();

            if (result.ShouldBlock)
                recommendations.Add("Content has been blocked due to security policy");

            if (result.ScriptAnalysis.MaliciousScripts > 0)
                recommendations.Add("Malicious scripts detected - page blocked for safety");

            if (result.ContentAnalysis.InappropriateContent.Count > 0)
                recommendations.Add("Content contains inappropriate material");

            return recommendations;
        }

        private List<string> GenerateDownloadRecommendations(DownloadAnalysisResult result)
        {
            var recommendations = new List<string>();

            if (result.ShouldBlock)
                recommendations.Add("Download has been blocked for security reasons");

            if (result.ExtensionRisk >= RiskLevel.High)
                recommendations.Add("This file type is considered high risk");

            if (result.SourceRisk >= RiskLevel.High)
                recommendations.Add("Download source is suspicious - verify legitimacy");

            return recommendations;
        }

        private void UpdateBrowserProfile(BrowserProfile profile, UrlAnalysisResult result)
        {
            profile.LastActivity = DateTime.UtcNow;
            profile.TotalUrlsAnalyzed++;

            if (result.ShouldBlock)
            {
                profile.BlockedUrls++;
                profile.RecentAlerts.Add(new BrowserAlert
                {
                    Timestamp = DateTime.UtcNow,
                    Type = AlertType.UrlBlocked,
                    Message = $"Blocked access to: {result.Url}",
                    Severity = result.OverallRisk
                });
            }

            // Keep only recent alerts
            if (profile.RecentAlerts.Count > 100)
            {
                profile.RecentAlerts = profile.RecentAlerts.OrderByDescending(a => a.Timestamp).Take(50).ToList();
            }

            SaveBrowserProfiles();
        }

        private void UpdateBrowserProfileWithContent(BrowserProfile profile, ContentAnalysisResult result)
        {
            profile.LastActivity = DateTime.UtcNow;

            if (result.ShouldBlock)
            {
                profile.BlockedContent++;
                profile.RecentAlerts.Add(new BrowserAlert
                {
                    Timestamp = DateTime.UtcNow,
                    Type = AlertType.ContentBlocked,
                    Message = $"Blocked content on: {result.Url}",
                    Severity = result.OverallRisk
                });
            }

            SaveBrowserProfiles();
        }

        private void UpdateBrowserProfileWithDownload(BrowserProfile profile, DownloadAnalysisResult result)
        {
            profile.LastActivity = DateTime.UtcNow;

            if (result.ShouldBlock)
            {
                profile.BlockedDownloads++;
                profile.RecentAlerts.Add(new BrowserAlert
                {
                    Timestamp = DateTime.UtcNow,
                    Type = AlertType.DownloadBlocked,
                    Message = $"Blocked download: {result.Filename}",
                    Severity = result.OverallRisk
                });
            }

            SaveBrowserProfiles();
        }

        private BrowserProfile GetOrCreateBrowserProfile(string browserType, string userId)
        {
            var key = $"{browserType}_{userId}";

            if (!_browserProfiles.TryGetValue(key, out var profile))
            {
                profile = new BrowserProfile
                {
                    BrowserType = browserType,
                    UserId = userId,
                    Settings = new BrowserSettings(),
                    FirstSeen = DateTime.UtcNow
                };
                _browserProfiles[key] = profile;
                SaveBrowserProfiles();
            }

            return profile;
        }

        private Dictionary<string, BrowserProfile> LoadBrowserProfiles()
        {
            if (!File.Exists(_extensionDbPath))
                return new Dictionary<string, BrowserProfile>();

            try
            {
                var json = File.ReadAllText(_extensionDbPath);
                return JsonSerializer.Deserialize<Dictionary<string, BrowserProfile>>(json) ?? new Dictionary<string, BrowserProfile>();
            }
            catch
            {
                return new Dictionary<string, BrowserProfile>();
            }
        }

        private void SaveBrowserProfiles()
        {
            try
            {
                var json = JsonSerializer.Serialize(_browserProfiles, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_extensionDbPath, json);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving browser profiles: {ex.Message}");
            }
        }

        private async Task SaveBrowserProfilesAsync()
        {
            try
            {
                var json = JsonSerializer.Serialize(_browserProfiles, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(_extensionDbPath, json).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving browser profiles: {ex.Message}");
            }
        }

        private async Task<bool> CheckExtensionInstallationAsync()
        {
            // Check if extension files exist in the expected location
            await Task.Yield();
            var extensionPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "BrowserExtension");
            return Directory.Exists(extensionPath) && File.Exists(Path.Combine(extensionPath, "manifest.json"));
        }

        private async Task<string> GetExtensionVersionAsync()
        {
            // Read version from manifest or config
            var manifestPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "BrowserExtension", "manifest.json");
            if (File.Exists(manifestPath))
            {
                try
                {
                    var json = await File.ReadAllTextAsync(manifestPath);
                    var manifest = JsonSerializer.Deserialize<Dictionary<string, object>>(json);
                    if (manifest != null && manifest.TryGetValue("version", out var version))
                    {
                        return version.ToString() ?? "1.0.0";
                    }
                }
                catch { }
            }
            return "1.0.0";
        }

        private async Task<bool> CheckForUpdatesAsync()
        {
            // Check for updates by comparing current version with latest available
            var currentVersion = await GetExtensionVersionAsync();
            // Fetch latest version from server
            var latestVersion = await FetchLatestVersionAsync();
            return string.Compare(currentVersion, latestVersion, StringComparison.Ordinal) < 0;
        }

        private async Task<string> FetchLatestVersionAsync()
        {
            try
            {
                // In production, fetch from a version endpoint or GitHub API
                // For example: var response = await _httpClient.GetStringAsync("https://api.github.com/repos/owner/repo/releases/latest");
                // Then parse the JSON for tag_name
                // For now, return a hardcoded version as fallback
                var response = await _httpClient.GetStringAsync("https://example.com/version.txt");
                return response.Trim();
            }
            catch
            {
                // Fallback to known latest version
                return "1.0.3";
            }
        }

        private async Task<bool> ValidatePermissionsAsync()
        {
            // Validate that the extension has necessary permissions
            var extensionPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "BrowserExtension");
            if (!Directory.Exists(extensionPath)) return false;

            // Check if we can read/write to the extension directory
            try
            {
                var testFile = Path.Combine(extensionPath, "test.tmp");
                await File.WriteAllTextAsync(testFile, "test");
                File.Delete(testFile);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }

    // Supporting classes for Browser Extension
    public class UrlAnalysisResult
{
    public string Url { get; set; } = string.Empty;
    public string BrowserType { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime AnalysisTime { get; set; }
    public WebFilterResult FilterResult { get; set; } = new();
    public PhishingAnalysisResult PhishingResult { get; set; } = new();
    public MalwareAnalysisResult MalwareResult { get; set; } = new();
    public RiskLevel OverallRisk { get; set; }
    public bool ShouldBlock { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class ContentAnalysisResult
{
    public string Url { get; set; } = string.Empty;
    public string BrowserType { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime AnalysisTime { get; set; }
    public ContentAnalysis ContentAnalysis { get; set; } = new();
    public ScriptAnalysis ScriptAnalysis { get; set; } = new();
    public FormAnalysis FormAnalysis { get; set; } = new();
    public RiskLevel OverallRisk { get; set; }
    public bool ShouldBlock { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class DownloadAnalysisResult
{
    public string Url { get; set; } = string.Empty;
    public string Filename { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string BrowserType { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public DateTime AnalysisTime { get; set; }
    public RiskLevel ExtensionRisk { get; set; }
    public RiskLevel SizeRisk { get; set; }
    public RiskLevel SourceRisk { get; set; }
    public RiskLevel OverallRisk { get; set; }
    public bool ShouldBlock { get; set; }
    public List<string> Recommendations { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class ExtensionHealthResult
{
    public bool IsInstalled { get; set; }
    public string Version { get; set; } = string.Empty;
    public bool UpdateAvailable { get; set; }
    public bool PermissionsValid { get; set; }
    public bool IsHealthy { get; set; }
    public string Error { get; set; } = string.Empty;
}

public class WebFilterResult
{
    public RiskLevel RiskLevel { get; set; }
    public List<string> BlockedCategories { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class PhishingAnalysisResult
{
    public RiskLevel RiskLevel { get; set; }
    public List<string> Indicators { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class MalwareAnalysisResult
{
    public RiskLevel RiskLevel { get; set; }
    public List<string> Threats { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class ContentAnalysis
{
    public List<string> MaliciousKeywords { get; set; } = new();
    public List<string> PhishingIndicators { get; set; } = new();
    public List<string> InappropriateContent { get; set; } = new();
    public double RiskScore { get; set; }
    public string Error { get; set; } = string.Empty;
}

public class ScriptAnalysis
{
    public int ScriptCount { get; set; }
    public int MaliciousScripts { get; set; }
    public List<string> SuspiciousPatterns { get; set; } = new();
    public RiskLevel RiskLevel { get; set; }
    public string Error { get; set; } = string.Empty;
}

public class FormAnalysis
{
    public int FormCount { get; set; }
    public int SuspiciousForms { get; set; }
    public List<string> RiskFactors { get; set; } = new();
    public string Error { get; set; } = string.Empty;
}

public class BrowserProfile
{
    public string BrowserType { get; set; } = string.Empty;
    public string UserId { get; set; } = string.Empty;
    public BrowserSettings Settings { get; set; } = new();
    public DateTime FirstSeen { get; set; }
    public DateTime LastActivity { get; set; }
    public int TotalUrlsAnalyzed { get; set; }
    public int BlockedUrls { get; set; }
    public int BlockedContent { get; set; }
    public int BlockedDownloads { get; set; }
    public List<BrowserAlert> RecentAlerts { get; set; } = new();
}

public class BrowserSettings
{
    public bool BlockHighRiskSites { get; set; } = true;
    public bool BlockAdultContent { get; set; } = true;
    public bool BlockViolence { get; set; } = true;
    public bool StrictContentFilter { get; set; } = false;
    public bool BlockExecutableDownloads { get; set; } = true;
    public List<string> BlockedDomains { get; set; } = new();
    public List<string> TrustedDomains { get; set; } = new();
}

public class BrowserAlert
{
    public DateTime Timestamp { get; set; }
    public AlertType Type { get; set; }
    public string Message { get; set; } = string.Empty;
    public RiskLevel Severity { get; set; }
}

public enum AlertType
{
    UrlBlocked,
    ContentBlocked,
    DownloadBlocked,
    PhishingDetected,
    MalwareDetected
}

// Internal engines
public class WebFilterEngine
{
    public async Task<WebFilterResult> CheckUrlAsync(string url, BrowserProfile profile)
    {
        await Task.Yield();
        var result = new WebFilterResult();

        try
        {
            // Check against blocked domains
            var uri = new Uri(url);
            if (profile.Settings.BlockedDomains.Contains(uri.Host, StringComparer.OrdinalIgnoreCase))
            {
                result.RiskLevel = RiskLevel.Critical;
                result.BlockedCategories.Add("Blocked Domain");
            }

            // Check for suspicious patterns
            if (url.Contains("free") && url.Contains("download"))
            {
                result.RiskLevel = RiskLevel.Medium;
                result.BlockedCategories.Add("Suspicious Download Site");
            }

        }
        catch (Exception ex)
        {
            result.Error = ex.Message;
        }

        return result;
    }
}

public class PhishingDetector
{
    public async Task<PhishingAnalysisResult> AnalyzeUrlAsync(string url)
    {
        await Task.Yield();
        var result = new PhishingAnalysisResult();

        try
        {
            // Check for phishing patterns
            var phishingPatterns = new[]
            {
                    @"paypal.*\\.com.*login",
                    @"bank.*\\.com.*secure",
                    @"login.*\\.com.*account"
                };

            foreach (var pattern in phishingPatterns)
            {
                if (Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase))
                {
                    result.RiskLevel = RiskLevel.High;
                    result.Indicators.Add("Suspicious login pattern detected");
                    break;
                }
            }

        }
        catch (Exception ex)
        {
            result.Error = ex.Message;
        }

        return result;
    }
}

public class MalwareSiteDetector
{
    public async Task<MalwareAnalysisResult> CheckMalwareAsync(string url)
    {
        await Task.Yield();
        var result = new MalwareAnalysisResult();

        try
        {
            // Check for known malware distribution patterns
            var malwarePatterns = new[]
            {
                    @"crack", @"keygen", @"warez", @"pirate",
                    @"torrent", @"download.*free.*full"
                };

            foreach (var pattern in malwarePatterns)
            {
                if (Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase))
                {
                    result.RiskLevel = RiskLevel.Medium;
                    result.Threats.Add("Potential malware distribution site");
                    break;
                }
            }

        }
        catch (Exception ex)
        {
            result.Error = ex.Message;
        }

        return result;
    }
}
}
