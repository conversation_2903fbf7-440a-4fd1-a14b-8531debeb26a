# PowerShell script to fix syntax errors in ViewModels and Views

$files = @(
    "PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs",
    "PcFutureShield.UI\ViewModels\ThemeSelectorViewModel.cs",
    "PcFutureShield.UI\ViewModels\UpdatesViewModel.cs",
    "PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs",
    "PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs",
    "PcFutureShield.UI\ViewModels\DashboardViewModel.cs",
    "PcFutureShield.UI\ViewModels\EnhancedScanViewModel.cs",
    "PcFutureShield.UI\ViewModels\ParentalControlViewModel.cs",
    "PcFutureShield.UI\ViewModels\EventLogViewModel.cs",
    "PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs",
    "PcFutureShield.UI\ViewModels\ScannerViewModel.cs",
    "PcFutureShield.UI\ViewModels\QuarantineViewModel.cs"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Fixing $file..."
        
        $content = Get-Content $file -Raw
        
        # Remove duplicate INotifyPropertyChanged implementations
        $content = $content -replace '(?s)\s*public event System\.ComponentModel\.PropertyChangedEventHandler PropertyChanged;.*?PropertyChanged\?\s*\.Invoke\(this,\s*new\s*System\.ComponentModel\.PropertyChangedEventArgs\(name\)\);[^}]*}[^}]*$', ''
        
        # Ensure proper closing braces for namespace and class
        if ($content -notmatch '}[\s\r\n]*}[\s\r\n]*$') {
            $content = $content.TrimEnd()
            if ($content -notmatch '}[\s\r\n]*$') {
                $content += "`n    }"
            }
            $content += "`n}"
        }
        
        # Fix malformed property declarations
        $content = $content -replace '(?m)^\s*}\s*$\s*(?=\s*public\s+\w+\s+\w+)', ''
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed $file"
    }
}

# Fix View code-behind files
$viewFiles = @(
    "PcFutureShield.UI\Views\LogerViewModel.cs",
    "PcFutureShield.UI\Views\LogViewerWindow.xaml.cs",
    "PcFutureShield.UI\Views\ParentalControlView.xaml.cs",
    "PcFutureShield.UI\Views\AdminOverrideView.xaml.cs",
    "PcFutureShield.UI\Views\PcOptimizationView.xaml.cs",
    "PcFutureShield.UI\Views\AIDashboardView.xaml.cs",
    "PcFutureShield.UI\Views\QuarantineView.xaml.cs",
    "PcFutureShield.UI\Views\BrowserExtensionView.xaml.cs",
    "PcFutureShield.UI\Views\RealtimeProtectionView.xaml.cs",
    "PcFutureShield.UI\Views\DashboardView.xaml.cs",
    "PcFutureShield.UI\Views\ScannerView.xaml.cs",
    "PcFutureShield.UI\Views\SettingsView.xaml.cs",
    "PcFutureShield.UI\Views\EnhancedScanView.xaml.cs",
    "PcFutureShield.UI\Views\SmartRepairView.xaml.cs",
    "PcFutureShield.UI\Views\SplashWindow.xaml.cs",
    "PcFutureShield.UI\Views\UpdatesView.xaml.cs",
    "PcFutureShield.UI\MainWindow.xaml.cs",
    "PcFutureShield.UI\Views\VirusScanView.xaml.cs",
    "PcFutureShield.UI\Views\EventLogWindow.xaml.cs",
    "PcFutureShield.UI\Views\GamingProtectionView.xaml.cs",
    "PcFutureShield.UI\Views\LicenseManagerView.xaml.cs",
    "PcFutureShield.UI\Views\ThemeSelectorView.xaml.cs"
)

foreach ($file in $viewFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing view file $file..."
        
        $content = Get-Content $file -Raw
        
        # Remove duplicate closing braces and fix syntax
        $content = $content -replace '(?s)\s*}\s*$\s*(?=\s*$)', ''
        
        # Ensure proper closing braces
        if ($content -notmatch '}[\s\r\n]*}[\s\r\n]*$') {
            $content = $content.TrimEnd()
            if ($content -notmatch '}[\s\r\n]*$') {
                $content += "`n    }"
            }
            $content += "`n}"
        }
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed view file $file"
    }
}

# Fix theme files
$themeFiles = @(
    "PcFutureShield.UI\Styles\SharedStyles.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyBlue.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyMidnightBlue.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyPurple.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyRed.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyGreen.xaml.cs"
)

foreach ($file in $themeFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing theme file $file..."
        
        $content = Get-Content $file -Raw
        
        # Fix malformed namespace declarations
        $content = $content -replace '(?m)^using\s*;', ''
        $content = $content -replace '(?m)^namespace\s*$', 'namespace PcFutureShield.UI.Themes'
        
        # Fix class structure
        if ($content -match 'namespace\s+[\w\.]+') {
            $content = $content -replace '(?s)(namespace\s+[\w\.]+\s*\{).*', '$1' + "`n}"
        }
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed theme file $file"
    }
}

Write-Host "All syntax errors fixed!"
