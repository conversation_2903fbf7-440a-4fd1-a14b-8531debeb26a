[{"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 0, "Line": 124, "Context": "return Task.Run(() =>", "Suggested": "Manual: Replace fake indicator 'Task.Run(() =>' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 0, "Line": 154, "Context": "return Task.Run(() =>", "Suggested": "Manual: Replace fake indicator 'Task.Run(() =>' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 0, "Line": 179, "Context": "return Task.Run(() =>", "Suggested": "Manual: Replace fake indicator 'Task.Run(() =>' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 0, "Line": 201, "Context": "return Task.Run(() =>", "Suggested": "Manual: Replace fake indicator 'Task.Run(() =>' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 0, "Line": 226, "Context": "return Task.Run(() =>", "Suggested": "Manual: Replace fake indicator 'Task.Run(() =>' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 1, "Line": 0, "Context": "async Task UpdateBehaviorPatternsAsync(", "Suggested": "Consider making method synchronous or use real async APIs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\ParentalControlService.cs", "Kind": 1, "Line": 0, "Context": "async Task<BehaviorAnalysisResult> AnalyzeBehaviorAsync(", "Suggested": "Consider making method synchronous or use real async APIs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Common\\Services\\PcOptimizationService.cs", "Kind": 0, "Line": 655, "Context": "var tempKelvin = Convert.ToDouble(obj[\"CurrentTemperature\"]);", "Suggested": "Manual: Replace fake indicator 'TODO' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\Scanning\\ScannerService.cs", "Kind": 0, "Line": 1627, "Context": "// introduce an actual await to make this method truly asynchronous and avoid Task.CompletedTask placeholders", "Suggested": "Manual: Replace fake indicator 'PLACEHOLDER' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.Engine\\Scanning\\ScannerService.cs", "Kind": 0, "Line": 1627, "Context": "// introduce an actual await to make this method truly asynchronous and avoid Task.CompletedTask placeholders", "Suggested": "Manual: Replace fake indicator 'Task.CompletedTask' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 0, "Line": 258, "Context": "// Placeholder for tracking cookies blocked - would require browser integration", "Suggested": "Manual: Replace fake indicator 'PLACEHOLDER' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 0, "Line": 228, "Context": "var toDownload = new System.Collections.Generic.List<UpdateService.UpdateFeedEntry>();", "Suggested": "Manual: Replace fake indicator 'TODO' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 0, "Line": 232, "Context": "toDownload.Add(e);", "Suggested": "Manual: Replace fake indicator 'TODO' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 0, "Line": 235, "Context": "if (toDownload.Count == 0)", "Suggested": "Manual: Replace fake indicator 'TODO' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 0, "Line": 242, "Context": "int total = toDownload.Count;", "Suggested": "Manual: Replace fake indicator 'TODO' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 0, "Line": 245, "Context": "var entry = toDownload[i];", "Suggested": "Manual: Replace fake indicator 'TODO' with real logic. See service templates.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.ApproveRequestCommand", "Suggested": "Add property 'DataContext.ApproveRequestCommand' with INotifyPropertyChanged to AdminOverrideViewModel.cs.", "ViewModelProperty": "DataContext.ApproveRequestCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.DenyRequestCommand", "Suggested": "Add property 'DataContext.DenyRequestCommand' with INotifyPropertyChanged to AdminOverrideViewModel.cs.", "ViewModelProperty": "DataContext.DenyRequestCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 4, "Line": 0, "Context": "ActiveSessions.Count", "Suggested": "Add property 'ActiveSessions.Count' with INotifyPropertyChanged to AdminOverrideViewModel.cs.", "ViewModelProperty": "ActiveSessions.Count"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 4, "Line": 0, "Context": "PendingRequests.Count", "Suggested": "Add property 'PendingRequests.Count' with INotifyPropertyChanged to AdminOverrideViewModel.cs.", "ViewModelProperty": "PendingRequests.Count"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AdminOverrideViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AdminOverrideViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\AIDashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in AIDashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\BrowserExtensionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in BrowserExtensionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\DashboardViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in DashboardViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EnhancedScanViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EnhancedScanViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in EventLogViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs", "Kind": 4, "Line": 0, "Context": "Time", "Suggested": "Add property 'Time' with INotifyPropertyChanged to EventLogViewModel.cs.", "ViewModelProperty": "Time"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs", "Kind": 4, "Line": 0, "Context": "Type", "Suggested": "Add property 'Type' with INotifyPropertyChanged to EventLogViewModel.cs.", "ViewModelProperty": "Type"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\EventLogViewModel.cs", "Kind": 4, "Line": 0, "Context": "Message", "Suggested": "Add property 'Message' with INotifyPropertyChanged to EventLogViewModel.cs.", "ViewModelProperty": "Message"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\GamingProtectionViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in GamingProtectionViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\LicenseManagerViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in LicenseManagerViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\Views\\LogViewerWindow.xaml", "Kind": 3, "Line": 0, "Context": "LogFile<PERSON>ath", "Suggested": "Create ViewModel LogerViewModel with property 'LogFilePath' and INotifyPropertyChanged.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\Views\\LogViewerWindow.xaml", "Kind": 3, "Line": 0, "Context": "<PERSON>g<PERSON><PERSON><PERSON>", "Suggested": "Create ViewModel LogerViewModel with property 'LogContent' and INotifyPropertyChanged.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\Views\\LogViewerWindow.xaml", "Kind": 3, "Line": 0, "Context": "RefreshCommand", "Suggested": "Create ViewModel LogerViewModel with property 'RefreshCommand' and INotifyPropertyChanged.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\Views\\LogViewerWindow.xaml", "Kind": 3, "Line": 0, "Context": "ClearLogCommand", "Suggested": "Create ViewModel LogerViewModel with property 'ClearLogCommand' and INotifyPropertyChanged.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 4, "Line": 0, "Context": "Content", "Suggested": "Add property 'Content' with INotifyPropertyChanged to ParentalControlViewModel.cs.", "ViewModelProperty": "Content"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ParentalControlViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ParentalControlViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\PcOptimizationViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in PcOptimizationViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\QuarantineViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in QuarantineViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\QuarantineViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in QuarantineViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\QuarantineViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.RestoreCommand", "Suggested": "Add property 'DataContext.RestoreCommand' with INotifyPropertyChanged to QuarantineViewModel.cs.", "ViewModelProperty": "DataContext.RestoreCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\QuarantineViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.DeleteCommand", "Suggested": "Add property 'DataContext.DeleteCommand' with INotifyPropertyChanged to QuarantineViewModel.cs.", "ViewModelProperty": "DataContext.DeleteCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\QuarantineViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.ViewDetailsCommand", "Suggested": "Add property 'DataContext.ViewDetailsCommand' with INotifyPropertyChanged to QuarantineViewModel.cs.", "ViewModelProperty": "DataContext.ViewDetailsCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs", "Kind": 4, "Line": 0, "Context": "IsMalicious", "Suggested": "Add property 'IsMalicious' with INotifyPropertyChanged to ScannerViewModel.cs.", "ViewModelProperty": "IsMalicious"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs", "Kind": 4, "Line": 0, "Context": "FilePath", "Suggested": "Add property 'FilePath' with INotifyPropertyChanged to ScannerViewModel.cs.", "ViewModelProperty": "FilePath"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs", "Kind": 4, "Line": 0, "Context": "Reason", "Suggested": "Add property 'Reason' with INotifyPropertyChanged to ScannerViewModel.cs.", "ViewModelProperty": "Reason"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs", "Kind": 4, "Line": 0, "Context": "Sha256", "Suggested": "Add property 'Sha256' with INotifyPropertyChanged to ScannerViewModel.cs.", "ViewModelProperty": "Sha256"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ScannerViewModel.cs", "Kind": 4, "Line": 0, "Context": "Entropy", "Suggested": "Add property 'Entropy' with INotifyPropertyChanged to ScannerViewModel.cs.", "ViewModelProperty": "Entropy"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\SmartRepairViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in SmartRepairViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.PreviewThemeCommand", "Suggested": "Add property 'DataContext.PreviewThemeCommand' with INotifyPropertyChanged to ThemeSelectorViewModel.cs.", "ViewModelProperty": "DataContext.PreviewThemeCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 4, "Line": 0, "Context": "DataContext.ApplyThemeCommand", "Suggested": "Add property 'DataContext.ApplyThemeCommand' with INotifyPropertyChanged to ThemeSelectorViewModel.cs.", "ViewModelProperty": "DataContext.ApplyThemeCommand"}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\ThemeSelectorViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in ThemeSelectorViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}, {"FilePath": "d:\\PcFS_Create\\PcFutureShield\\PcFutureShield.UI\\ViewModels\\UpdatesViewModel.cs", "Kind": 5, "Line": 0, "Context": null, "Suggested": "Implement INotifyPropertyChanged in UpdatesViewModel.cs.", "ViewModelProperty": null}]