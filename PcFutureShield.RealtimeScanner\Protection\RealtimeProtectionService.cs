using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using PcFutureShield.Engine.Quarantine;
using PcFutureShield.Engine.VirusScanner;

namespace PcFutureShield.RealtimeScanner.Protection
{
    public sealed class RealtimeProtectionService : IAsyncDisposable
    {
        private readonly List<FileMonitor.FileWatcher> _watchers = new();
        public event Action<string>? OnEvent;

        public RealtimeProtectionService(IEnumerable<string> paths)
        {
            var signatures = new SignatureDatabase();
            var scanner = new PcFutureShieldScanner(signatures);
            var quarantine = new QuarantineManager();

            foreach (var p in paths)
            {
                var watcher = new FileMonitor.FileWatcher(p, scanner, quarantine);
                watcher.OnScanned += r => OnEvent?.Invoke($"{(r.IsMalicious ? "⚠️ MALICIOUS" : "OK")} {r.FilePath} :: {r.Reason}");
                _watchers.Add(watcher);
            }
        }

        public ValueTask DisposeAsync()
        {
            var tasks = new List<ValueTask>();
            foreach (var w in _watchers) tasks.Add(w.DisposeAsync());
            await Task.Yield(); // Real async: ensure context switch
            // ...existing code...
        }
    }
}
