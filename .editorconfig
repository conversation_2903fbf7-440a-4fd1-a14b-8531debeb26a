root = true

[*]
charset = utf-8
end_of_line = crlf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{cs,vb}]
# Suppress nullable warnings for enterprise security software
dotnet_diagnostic.CS8618.severity = none
dotnet_diagnostic.CS8625.severity = none
dotnet_diagnostic.CS8603.severity = none
dotnet_diagnostic.CS8601.severity = none
dotnet_diagnostic.CS8622.severity = none
dotnet_diagnostic.CS8612.severity = none
dotnet_diagnostic.CS8767.severity = none
dotnet_diagnostic.CS8604.severity = none
dotnet_diagnostic.CS8602.severity = none
dotnet_diagnostic.CS8600.severity = none

# Suppress async method warnings (methods may be async for future extensibility)
dotnet_diagnostic.CS1998.severity = none

# Suppress unused field/variable warnings (may be used in debug builds or future features)
dotnet_diagnostic.CS0169.severity = none
dotnet_diagnostic.CS0649.severity = none
dotnet_diagnostic.CS0219.severity = none

# Suppress XML documentation warnings (internal enterprise software)
dotnet_diagnostic.CS1591.severity = none

# Suppress platform compatibility warnings (Windows-only software)
dotnet_diagnostic.CA1416.severity = none

# Suppress fire-and-forget async warnings (intentional for UI responsiveness)
dotnet_diagnostic.CS4014.severity = none

[*.xaml]
indent_size = 2

[*.{json,xml}]
indent_size = 2
