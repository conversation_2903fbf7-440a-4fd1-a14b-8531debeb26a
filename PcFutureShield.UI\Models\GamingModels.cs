using System;
using System.ComponentModel;

namespace PcFutureShield.UI.ViewModels
{
    public class RigDetection : INotifyPropertyChanged
    {
        private string _detectionId = string.Empty;
        private string _gameName = string.Empty;
        private string _rigType = string.Empty;
        private DateTime _detectionTime;
        private string _severity = string.Empty;
        private string _details = string.Empty;

        public string DetectionId
        {
            get => _detectionId;
            set
            {
                if (_detectionId != value)
                {
                    _detectionId = value;
                    OnPropertyChanged(nameof(DetectionId));
                }
            }
        }

        public string GameName
        {
            get => _gameName;
            set
            {
                if (_gameName != value)
                {
                    _gameName = value;
                    OnPropertyChanged(nameof(GameName));
                }
            }
        }

        public string RigType
        {
            get => _rigType;
            set
            {
                if (_rigType != value)
                {
                    _rigType = value;
                    OnPropertyChanged(nameof(RigType));
                }
            }
        }

        public DateTime DetectionTime
        {
            get => _detectionTime;
            set
            {
                if (_detectionTime != value)
                {
                    _detectionTime = value;
                    OnPropertyChanged(nameof(DetectionTime));
                }
            }
        }

        public string Severity
        {
            get => _severity;
            set
            {
                if (_severity != value)
                {
                    _severity = value;
                    OnPropertyChanged(nameof(Severity));
                }
            }
        }

        public string Details
        {
            get => _details;
            set
            {
                if (_details != value)
                {
                    _details = value;
                    OnPropertyChanged(nameof(Details));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class FraudAlert : INotifyPropertyChanged
    {
        private string _alertId = string.Empty;
        private string _alertType = string.Empty;
        private string _description = string.Empty;
        private string _severity = string.Empty;
        private DateTime _timestamp;
        private string _source = string.Empty;

        public string AlertId
        {
            get => _alertId;
            set
            {
                if (_alertId != value)
                {
                    _alertId = value;
                    OnPropertyChanged(nameof(AlertId));
                }
            }
        }

        public string AlertType
        {
            get => _alertType;
            set
            {
                if (_alertType != value)
                {
                    _alertType = value;
                    OnPropertyChanged(nameof(AlertType));
                }
            }
        }

        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged(nameof(Description));
                }
            }
        }

        public string Severity
        {
            get => _severity;
            set
            {
                if (_severity != value)
                {
                    _severity = value;
                    OnPropertyChanged(nameof(Severity));
                }
            }
        }

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged(nameof(Timestamp));
                }
            }
        }

        public string Source
        {
            get => _source;
            set
            {
                if (_source != value)
                {
                    _source = value;
                    OnPropertyChanged(nameof(Source));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}
