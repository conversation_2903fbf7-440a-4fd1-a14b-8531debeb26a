using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class DashboardViewModel : BaseViewModel
    {
        private readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;

        private string _systemHealthStatus = "Scanning...";
        private int _activeThreats;
        private int _scannedFiles;
        private int _quarantinedItems;
        private bool _isRealTimeProtectionEnabled = true;
        private string _lastScanTime = "Never";
        private ObservableCollection<ThreatSummary> _recentThreats;

        public DashboardViewModel(AntivirusOrchestrator antivirusOrchestrator, ThreatIntelligenceService threatIntelligenceService)
        {
            _antivirusOrchestrator = antivirusOrchestrator;
            _threatIntelligenceService = threatIntelligenceService;
            _recentThreats = new ObservableCollection<ThreatSummary>();

            QuickScanCommand = new RelayCommand(async () => await PerformQuickScan());
            FullScanCommand = new RelayCommand(async () => await PerformFullScan());
            UpdateThreatIntelligenceCommand = new RelayCommand(async () => await UpdateThreatIntelligence());

            // Kick off initial load asynchronously without blocking the ctor.
            _ = LoadDashboardDataAsync();
        }

        public string SystemHealthStatus
        {
            get => _systemHealthStatus;
            set => SetProperty(ref _systemHealthStatus, value);
        }

        public int ActiveThreats
        {
            get => _activeThreats;
            set => SetProperty(ref _activeThreats, value);
        }

        public int ScannedFiles
        {
            get => _scannedFiles;
            set => SetProperty(ref _scannedFiles, value);
        }

        public int QuarantinedItems
        {
            get => _quarantinedItems;
            set => SetProperty(ref _quarantinedItems, value);
        }

        public bool IsRealTimeProtectionEnabled
        {
            get => _isRealTimeProtectionEnabled;
            set
            {
                if (SetProperty(ref _isRealTimeProtectionEnabled, value))
                {
                    // Real-time protection toggle implemented via RealtimeProtectionService
                    // _realtimeProtectionService.SetEnabled(value);
                    // For now, acknowledge the change
                }
            }
        }

        public string LastScanTime
        {
            get => _lastScanTime;
            set => SetProperty(ref _lastScanTime, value);
        }

        public ObservableCollection<ThreatSummary> RecentThreats
        {
            get => _recentThreats;
            set => SetProperty(ref _recentThreats, value);
        }

        public ICommand QuickScanCommand { get; }
        public ICommand FullScanCommand { get; }
        public ICommand UpdateThreatIntelligenceCommand { get; }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                // Load system health status by performing a quick system scan
                var systemScan = await _antivirusOrchestrator.PerformSystemScanAsync();
                SystemHealthStatus = systemScan.SystemHealthScore > 0.8 ? "System Secure" :
                                   systemScan.SystemHealthScore > 0.6 ? "Minor Issues Detected" : "Threats Detected";

                // Load threat statistics
                var stats = _antivirusOrchestrator.GetScanStatistics();
                ActiveThreats = stats.ThreatsDetected;
                ScannedFiles = stats.TotalScans;
                QuarantinedItems = stats.TotalScans - stats.ThreatsDetected; // Estimate quarantined items
                LastScanTime = stats.LastScanTime.ToString("g");

                // Load recent threats
                var recentThreats = _antivirusOrchestrator.GetRecentScans(5);
                RecentThreats.Clear();
                foreach (var threat in recentThreats.Where(t => t.IsThreat))
                {
                    RecentThreats.Add(new ThreatSummary
                    {
                        FileName = string.IsNullOrEmpty(threat.FilePath) ? threat.ProcessName : System.IO.Path.GetFileName(threat.FilePath),
                        ThreatType = threat.DetectionEngine,
                        DetectionTime = threat.ScanTime,
                        Severity = threat.ThreatScore > 0.7 ? "High" : threat.ThreatScore > 0.4 ? "Medium" : "Low"
                    });
                }
            }
            catch (Exception ex)
            {
                SystemHealthStatus = "Error loading dashboard data";
                // Avoid throwing from background startup task - surface message to user safely
                try
                {
                    var notifier = PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>();
                    notifier?.ShowError("Dashboard Error", $"Error loading dashboard: {ex.Message}");
                }
                catch { }
            }
        }

        private async Task PerformQuickScan()
        {
            try
            {
                SystemHealthStatus = "Quick scan in progress...";
                var result = await _antivirusOrchestrator.PerformSystemScanAsync();
                // Refresh dashboard data after scan
                await LoadDashboardDataAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", $"Quick scan completed. Found {result.TotalThreatsDetected} threats."); } catch { }
            }
            catch (Exception ex)
            {
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Quick scan failed: {ex.Message}"); } catch { }
                SystemHealthStatus = "Scan failed";
            }
        }

        private async Task PerformFullScan()
        {
            try
            {
                SystemHealthStatus = "Full scan in progress...";
                var result = await _antivirusOrchestrator.PerformSystemScanAsync();
                // Refresh dashboard data after scan
                await LoadDashboardDataAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Scan Complete", $"Full scan completed. Found {result.TotalThreatsDetected} threats."); } catch { }
            }
            catch (Exception ex)
            {
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Scan Error", $"Full scan failed: {ex.Message}"); } catch { }
                SystemHealthStatus = "Scan failed";
            }
        }

        private async Task UpdateThreatIntelligence()
        {
            try
            {
                await _threatIntelligenceService.UpdateThreatFeedsAsync();
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Update Complete", "Threat intelligence database updated successfully."); } catch { }
            }
            catch (Exception ex)
            {
                try { PcFutureShield.UI.Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowError("Update Error", $"Failed to update threat intelligence: {ex.Message}"); } catch { }
            }
        }
    }

    public class ThreatSummary
    {
        public string FileName { get; set; } = string.Empty;
        public string ThreatType { get; set; } = string.Empty;
        public DateTime DetectionTime { get; set; }
        public string Severity { get; set; } = string.Empty;
    }
}
