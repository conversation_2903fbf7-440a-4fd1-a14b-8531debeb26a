using System;
using System.IO;
using System.Text;

namespace PcFutureShield.UI.Services
{
    public class LoggingService
    {
        private static LoggingService? _instance;
        private static readonly object _lock = new object();
        private readonly string _logDirectory;
        private readonly string _logFilePath;

        public static LoggingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new LoggingService();
                    }
                }
                return _instance;
            }
        }

        private LoggingService()
        {
            _logDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "Logs");
            _logFilePath = Path.Combine(_logDirectory, $"PcFutureShield_{DateTime.Now:yyyy-MM-dd}.log");

            try
            {
                Directory.CreateDirectory(_logDirectory);
            }
            catch (Exception ex)
            {
                // If we can't create the log directory, try to log to temp
                _logDirectory = Path.GetTempPath();
                _logFilePath = Path.Combine(_logDirectory, $"PcFutureShield_{DateTime.Now:yyyy-MM-dd}.log");
                Log(LogLevel.Warning, "LoggingService", $"Could not create log directory, using temp: {ex.Message}");
            }
        }

        public enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error,
            Fatal
        }

        public void Log(LogLevel level, string category, string message, Exception? exception = null)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var levelStr = level.ToString().ToUpper().PadRight(8);
                var logEntry = $"[{timestamp}] [{levelStr}] [{category}] {message}";

                if (exception != null)
                {
                    logEntry += $"\nException: {exception.Message}\nStackTrace: {exception.StackTrace}";
                }

                lock (_lock)
                {
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
                }

                // Also output to console in debug mode
#if DEBUG
                Console.WriteLine(logEntry);
#endif
            }
            catch
            {
                // If logging fails, we don't want to cause more issues
                // Just silently fail to avoid recursive error loops
            }
        }

        public void LogDebug(string category, string message) => Log(LogLevel.Debug, category, message);
        public void LogInfo(string category, string message) => Log(LogLevel.Info, category, message);
        public void LogWarning(string category, string message) => Log(LogLevel.Warning, category, message);
        public void LogError(string category, string message, Exception? exception = null) => Log(LogLevel.Error, category, message, exception);
        public void LogFatal(string category, string message, Exception? exception = null) => Log(LogLevel.Fatal, category, message, exception);

        public string GetLogFilePath() => _logFilePath;

        public string GetLogContent()
        {
            try
            {
                return File.ReadAllText(_logFilePath);
            }
            catch
            {
                return "Unable to read log file.";
            }
        }

        public void ClearLog()
        {
            try
            {
                File.WriteAllText(_logFilePath, "");
                LogInfo("LoggingService", "Log file cleared");
            }
            catch (Exception ex)
            {
                LogError("LoggingService", "Failed to clear log file", ex);
            }
        }
    }
}
