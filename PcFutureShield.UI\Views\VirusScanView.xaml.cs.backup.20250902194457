using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class VirusScanView : UserControl
    {
        public VirusScanView()
        {
            InitializeComponent();
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void BooleanToVisibilityConverter(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
