using System;
using System.Collections.Generic;
using System.IO;
using System.Security.AccessControl;
using System.Security.Cryptography;
using System.Text.Json;
using PcFutureShield.Common.Interfaces;

namespace PcFutureShield.Engine.Quarantine
{
    /// <summary>
    /// AES-GCM encrypted quarantine with index in %ProgramData%\PcFutureShield\quarantine\index.json
    /// </summary>
    public sealed class QuarantineManager : IQuarantineManager
    {
        private readonly string _root;
        private readonly string _indexPath;
        private readonly Dictionary<Guid, PcFutureShield.Common.Interfaces.QuarantineItem> _index = new();
        private readonly byte[] _key; // 256-bit key, persisted

        public QuarantineManager()
        {
            // Use LocalApplicationData for user-writable quarantine storage
            var baseDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "quarantine");
            EnsureDirectoryAccess(baseDir);
            Directory.CreateDirectory(baseDir);
            _root = baseDir;
            _indexPath = Path.Combine(baseDir, "index.json");
            _key = LoadOrCreateKey(Path.Combine(baseDir, "qkey.bin"));
            LoadIndex();
            // LockDownAcl(_root); // Commented out - too restrictive
        }

        public IReadOnlyCollection<PcFutureShield.Common.Interfaces.QuarantineItem> Items => _index.Values;

        public PcFutureShield.Common.Interfaces.QuarantineItem QuarantineFile(string sourcePath, string sha256, string reason)
        {
            if (!File.Exists(sourcePath)) throw new FileNotFoundException("File to quarantine not found.", sourcePath);

            var id = Guid.NewGuid();
            var encPath = Path.Combine(_root, id.ToString("N") + ".qf");
            EncryptFile(sourcePath, encPath, _key, out long originalSize);

            // Overwrite source on disk to prevent accidental execution, then delete.
            try { SecureWipeAndDelete(sourcePath); } catch { /* best effort */ }

            var item = new PcFutureShield.Common.Interfaces.QuarantineItem
            {
                Id = id,
                OriginalPath = sourcePath,
                QuarantinePath = encPath,
                OriginalSha256 = sha256,
                Timestamp = DateTimeOffset.UtcNow,
                Reason = reason,
                OriginalSize = originalSize,
                OriginalFileName = System.IO.Path.GetFileName(sourcePath),
                FilePath = sourcePath,
                ThreatName = reason,
                DetectedAt = DateTimeOffset.UtcNow.DateTime,
                IsQuarantined = true
            };

            _index[item.Id] = item;
            SaveIndex();
            return item;
        }

        public string Restore(Guid id, string destinationPath = null)
        {
            if (!_index.TryGetValue(id, out var item)) throw new InvalidOperationException("Quarantine item not found.");
            if (!File.Exists(item.QuarantinePath)) throw new FileNotFoundException("Encrypted blob missing.", item.QuarantinePath);

            var target = destinationPath != null ? destinationPath : item.OriginalPath;
            var targetDir = Path.GetDirectoryName(target)!;
            Directory.CreateDirectory(targetDir);

            DecryptFile(item.QuarantinePath, target, _key);

            // Validate integrity
            var restoredHash = Common.Services.HashingService.ComputeSHA256(target);
            if (!restoredHash.Equals(item.OriginalSha256, StringComparison.OrdinalIgnoreCase))
                throw new CryptographicException("Restored file hash mismatch. Aborting restore.");

            return target;
        }

        public void Delete(Guid id)
        {
            if (!_index.TryGetValue(id, out var item)) return;
            try { if (File.Exists(item.QuarantinePath)) File.Delete(item.QuarantinePath); } catch { /* best effort */ }
            _index.Remove(id);
            SaveIndex();
            QuarantineChanged?.Invoke(this, EventArgs.Empty);
        }

        public int GetQuarantinedCount() => _index.Count;

        public IReadOnlyCollection<PcFutureShield.Common.Interfaces.QuarantineItem> GetQuarantinedItems() => (IReadOnlyCollection<PcFutureShield.Common.Interfaces.QuarantineItem>)Items;

        public PcFutureShield.Common.Interfaces.QuarantineResult RestoreItem(string itemId)
        {
            try
            {
                if (!Guid.TryParse(itemId, out var id))
                    return new PcFutureShield.Common.Interfaces.QuarantineResult { Success = false, Message = "Invalid item ID" };

                var restoredPath = Restore(id);
                QuarantineChanged?.Invoke(this, EventArgs.Empty);
                return new PcFutureShield.Common.Interfaces.QuarantineResult { Success = true, Message = $"Restored to {restoredPath}" };
            }
            catch (Exception ex)
            {
                return new PcFutureShield.Common.Interfaces.QuarantineResult { Success = false, Message = ex.Message };
            }
        }

        public PcFutureShield.Common.Interfaces.QuarantineResult DeleteItem(string itemId)
        {
            try
            {
                if (!Guid.TryParse(itemId, out var id))
                    return new PcFutureShield.Common.Interfaces.QuarantineResult { Success = false, Message = "Invalid item ID" };

                Delete(id);
                return new PcFutureShield.Common.Interfaces.QuarantineResult { Success = true, Message = "Item deleted successfully" };
            }
            catch (Exception ex)
            {
                return new PcFutureShield.Common.Interfaces.QuarantineResult { Success = false, Message = ex.Message };
            }
        }

        public event EventHandler? QuarantineChanged;

        private static void EncryptFile(string inputPath, string outputPath, byte[] key, out long originalSize)
        {
            originalSize = new FileInfo(inputPath).Length;
            using var input = File.OpenRead(inputPath);
            using var output = File.Open(outputPath, FileMode.Create, FileAccess.ReadWrite, FileShare.None);

            // nonce 12 bytes, tag 16 bytes
            Span<byte> nonce = stackalloc byte[12];
            RandomNumberGenerator.Fill(nonce);
            output.Write(nonce);

            using var ms = new MemoryStream();
            input.CopyTo(ms);
            var plaintext = ms.ToArray();
            var ciphertext = new byte[plaintext.Length];
            var tag = new byte[16];

            using var gcm = new AesGcm(key, 16);
            gcm.Encrypt(nonce, plaintext, ciphertext, tag, associatedData: null);

            output.Write(ciphertext);
            output.Write(tag);
        }

        private static void DecryptFile(string inputPath, string outputPath, byte[] key)
        {
            using var input = File.OpenRead(inputPath);
            using var output = File.Open(outputPath, FileMode.Create, FileAccess.ReadWrite, FileShare.None);

            Span<byte> nonce = stackalloc byte[12];
            if (input.Read(nonce) != 12) throw new CryptographicException("Invalid quarantine header.");

            var remaining = input.Length - 12;
            if (remaining < 16) throw new CryptographicException("Invalid ciphertext.");
            var ctLen = remaining - 16;

            var ciphertext = new byte[ctLen];
            var tag = new byte[16];

            input.ReadExactly(ciphertext, 0, ciphertext.Length);
            input.ReadExactly(tag, 0, tag.Length);

            var plaintext = new byte[ctLen];
            using var gcm = new AesGcm(key, 16);
            gcm.Decrypt(nonce, ciphertext, tag, plaintext, associatedData: null);

            output.Write(plaintext);
        }

        private static byte[] LoadOrCreateKey(string path)
        {
            try
            {
                if (File.Exists(path))
                    return File.ReadAllBytes(path);
            }
            catch (UnauthorizedAccessException)
            {
                // If we can't read the existing file, we'll try to recreate it
            }

            try
            {
                var key = new byte[32];
                RandomNumberGenerator.Fill(key);
                var directory = Path.GetDirectoryName(path)!;
                Directory.CreateDirectory(directory);
                File.WriteAllBytes(path, key);
                return key;
            }
            catch (UnauthorizedAccessException ex)
            {
                throw new InvalidOperationException($"Cannot create quarantine key file at {path}. Please ensure the application has write access to the directory.", ex);
            }
        }

        private void LoadIndex()
        {
            try
            {
                if (!File.Exists(_indexPath))
                {
                    SaveIndex();
                    return;
                }
                var json = File.ReadAllText(_indexPath);
                var list = JsonSerializer.Deserialize<List<PcFutureShield.Common.Interfaces.QuarantineItem>>(json);
                _index.Clear();
                if (list != null)
                    foreach (var item in list) _index[item.Id] = item;
            }
            catch
            {
                // If we can't load the index, start with an empty one
                _index.Clear();
                // Try to save a new empty index
                try { SaveIndex(); } catch { /* ignore */ }
            }
        }

        private void SaveIndex()
        {
            try
            {
                var list = new List<PcFutureShield.Common.Interfaces.QuarantineItem>(_index.Values);
                var json = JsonSerializer.Serialize(list, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_indexPath, json);
            }
            catch
            {
                // If we can't save the index, it's not fatal but we should log it
                // For now, we'll just ignore it since this is called during initialization
            }
        }

        private static void SecureWipeAndDelete(string path)
        {
            if (!File.Exists(path)) return;
            var fi = new FileInfo(path);
            using var fs = new FileStream(path, FileMode.Open, FileAccess.ReadWrite, FileShare.None);
            var buffer = new byte[8192];
            long remaining = fi.Length;
            var rng = RandomNumberGenerator.Create();
            while (remaining > 0)
            {
                rng.GetBytes(buffer);
                var toWrite = (int)Math.Min(buffer.Length, remaining);
                fs.Write(buffer, 0, toWrite);
                remaining -= toWrite;
            }
            fs.Flush(true);
            fs.Close();
            File.Delete(path);
        }

        private static void EnsureDirectoryAccess(string directory)
        {
            try
            {
                if (!Directory.Exists(directory))
                    return; // Directory will be created by Directory.CreateDirectory

                // Test if we can write to the directory
                var testFile = Path.Combine(directory, "access_test.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);
            }
            catch (UnauthorizedAccessException)
            {
                // If we can't access the directory, try to reset permissions
                try
                {
                    var dirInfo = new DirectoryInfo(directory);
                    var sec = dirInfo.GetAccessControl();
                    sec.SetAccessRuleProtection(isProtected: false, preserveInheritance: true);
                    dirInfo.SetAccessControl(sec);
                }
                catch
                {
                    // If we can't fix permissions, continue anyway
                }
            }
            catch
            {
                // Ignore other exceptions
            }
        }
    }
}
