<UserControl x:Class="PcFutureShield.UI.Views.SmartRepairView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource BackgroundBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <ScrollViewer.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF072A7F" Offset="1"/>
                </LinearGradientBrush>
            </ScrollViewer.Background>
            <StackPanel Margin="20">
                <TextBlock Text="Smart Repair Center" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- System Diagnostics -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="System Diagnostics" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="3" Rows="1">
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="System Health" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding SystemHealthStatus}" FontSize="18" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Issues Found" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding IssuesFound}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource ErrorBrush}"/>
                                </StackPanel>
                            </Border>
                            <Border Background="{DynamicResource CardBackgroundBrush}" Margin="5" Padding="15" CornerRadius="8">
                                <StackPanel>
                                    <TextBlock Text="Repairs Completed" FontWeight="SemiBold" Foreground="{DynamicResource SecondaryFontBrush}"/>
                                    <TextBlock Text="{Binding RepairsCompleted}" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Repair Options -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Automated Repairs" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <UniformGrid Columns="2" Rows="3">
                            <Button Content="Fix Registry Issues" Style="{DynamicResource GlassButtonStyle}" Command="{Binding FixRegistryCommand}" Margin="5"/>
                            <Button Content="Repair System Files" Style="{DynamicResource GlassButtonStyle}" Command="{Binding RepairSystemFilesCommand}" Margin="5"/>
                            <Button Content="Clean Startup Programs" Style="{DynamicResource GlassButtonStyle}" Command="{Binding CleanStartupCommand}" Margin="5"/>
                            <Button Content="Fix Network Issues" Style="{DynamicResource GlassButtonStyle}" Command="{Binding FixNetworkCommand}" Margin="5"/>
                            <Button Content="Repair Windows Update" Style="{DynamicResource GlassButtonStyle}" Command="{Binding RepairWindowsUpdateCommand}" Margin="5"/>
                            <Button Content="Optimize Performance" Style="{DynamicResource GlassButtonStyle}" Command="{Binding OptimizePerformanceCommand}" Margin="5"/>
                        </UniformGrid>
                    </StackPanel>
                </Border>

                <!-- Repair Progress -->
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="0,0,0,20" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Repair Progress" FontSize="24" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}" Margin="0,0,0,15"/>
                        <TextBlock Text="{Binding CurrentRepairOperation}" FontSize="16" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,10"/>
                        <ProgressBar Value="{Binding RepairProgress}" Height="20" Margin="0,0,0,10" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryColorBrush}"/>
                        <TextBlock Text="{Binding RepairStatus}" FontSize="14" Foreground="{DynamicResource PrimaryFontBrush}" TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- Quick Actions -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="Run Full Diagnostics" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding RunDiagnosticsCommand}" Margin="10"/>
                    <Button Content="Generate Repair Report" Style="{DynamicResource ChromeButtonStyle}" Command="{Binding GenerateReportCommand}" Margin="10"/>
                    <Button Content="Emergency Repair" Style="{DynamicResource MirrorButtonStyle}" Command="{Binding EmergencyRepairCommand}" Margin="10"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
