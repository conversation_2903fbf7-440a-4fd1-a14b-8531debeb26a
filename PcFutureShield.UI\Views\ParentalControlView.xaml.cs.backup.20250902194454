using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    /// <summary>
    /// Interaction logic for ParentalControlView.xaml
    /// </summary>
    public partial class ParentalControlView : UserControl
    {
        public ParentalControlView()
        {
            InitializeComponent();
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Auto(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
