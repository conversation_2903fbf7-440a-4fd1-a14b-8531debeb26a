﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class ParentalControlViewModel : BaseViewModel
    {
        private readonly ParentalControlService _parentalControlService;

        private bool _isEnabled = true;
        private string _currentUserProfile = "Default";
        private ObservableCollection<UserProfile> _userProfiles;
        private ObservableCollection<BlockedContent> _recentBlocks;
        private ObservableCollection<ContentFilter> _contentFilters;
        private TimeSpan _dailyScreenTimeLimit = TimeSpan.FromHours(2);
        private bool _blockSocialMedia = true;
        private bool _blockAdultContent = true;
        private bool _blockGambling = true;
        private bool _monitorChatActivity = true;
        private bool _enableSafeSearch = true;

        // New blocking level properties
        private BlockingLevel _currentBlockingLevel = BlockingLevel.Medium;
        private bool _emergencyDisableMode = false;
        private string _blockingLevelDescription = "Medium - Adult content, gambling, violence, and drugs";
        private bool _isEmergencyDisableVisible = false;

        private UserProfile? _selectedUserProfile;

        public ParentalControlViewModel(ParentalControlService parentalControlService)
        {
            _parentalControlService = parentalControlService;
            _userProfiles = new ObservableCollection<UserProfile>();
            _recentBlocks = new ObservableCollection<BlockedContent>();
            _contentFilters = new ObservableCollection<ContentFilter>();

            AddUserProfileCommand = new RelayCommand(AddUserProfile);
            RemoveUserProfileCommand = new RelayCommand<UserProfile>(RemoveUserProfile);
            UpdateFiltersCommand = new AsyncRelayCommand(async () => await UpdateFilters());
            ViewBlockedContentCommand = new RelayCommand(ViewBlockedContent);
            TestBlockingCommand = new AsyncRelayCommand(async () => await TestBlockingAsync());

            // New commands for blocking level control
            SetBlockingLevelCommand = new AsyncRelayCommand<BlockingLevel>(async (level) => await SetBlockingLevelAsync(level));
            EmergencyDisableCommand = new AsyncRelayCommand(async () => await EmergencyDisableAsync());
            ReEnableProtectionCommand = new AsyncRelayCommand(async () => await ReEnableProtectionAsync());

            LoadParentalControlData();
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set
            {
                if (SetProperty(ref _isEnabled, value))
                {
                    _ = Task.Run(async () => await ToggleNetworkBlockingAsync(value));
                }
            }
        }

        public string CurrentUserProfile
        {
            get => _currentUserProfile;
            set => SetProperty(ref _currentUserProfile, value);
        }

        public ObservableCollection<UserProfile> UserProfiles
        {
            get => _userProfiles;
            set => SetProperty(ref _userProfiles, value);
        }

        public ObservableCollection<BlockedContent> RecentBlocks
        {
            get => _recentBlocks;
            set => SetProperty(ref _recentBlocks, value);
        }

        public ObservableCollection<ContentFilter> ContentFilters
        {
            get => _contentFilters;
            set => SetProperty(ref _contentFilters, value);
        }

        public TimeSpan DailyScreenTimeLimit
        {
            get => _dailyScreenTimeLimit;
            set => SetProperty(ref _dailyScreenTimeLimit, value);
        }

        public bool BlockSocialMedia
        {
            get => _blockSocialMedia;
            set
            {
                if (SetProperty(ref _blockSocialMedia, value))
                {
                    UpdateContentFilter("Social Media", value);
                }
            }
        }

        public bool BlockAdultContent
        {
            get => _blockAdultContent;
            set
            {
                if (SetProperty(ref _blockAdultContent, value))
                {
                    UpdateContentFilter("Adult Content", value);
                    _ = Task.Run(async () => await UpdateDomainBlockingAsync("Adult Content", value));
                }
            }
        }

        public bool BlockGambling
        {
            get => _blockGambling;
            set
            {
                if (SetProperty(ref _blockGambling, value))
                {
                    UpdateContentFilter("Gambling", value);
                    _ = Task.Run(async () => await UpdateDomainBlockingAsync("Gambling", value));
                }
            }
        }

        public bool MonitorChatActivity
        {
            get => _monitorChatActivity;
            set => SetProperty(ref _monitorChatActivity, value);
        }

        public bool EnableSafeSearch
        {
            get => _enableSafeSearch;
            set => SetProperty(ref _enableSafeSearch, value);
        }

        // New property bound from XAML
        public UserProfile? SelectedUserProfile
        {
            get => _selectedUserProfile;
            set => SetProperty(ref _selectedUserProfile, value);
        }

        public ICommand AddUserProfileCommand { get; }
        public ICommand RemoveUserProfileCommand { get; }
        public ICommand UpdateFiltersCommand { get; }
        public ICommand ViewBlockedContentCommand { get; }
        public ICommand TestBlockingCommand { get; }

        // New commands for blocking level control
        public ICommand SetBlockingLevelCommand { get; }
        public ICommand EmergencyDisableCommand { get; }
        public ICommand ReEnableProtectionCommand { get; }

        // New properties for blocking level control
        public BlockingLevel CurrentBlockingLevel
        {
            get => _currentBlockingLevel;
            set
            {
                if (SetProperty(ref _currentBlockingLevel, value))
                {
                    UpdateBlockingLevelDescription();
                    _ = Task.Run(async () => await SetBlockingLevelAsync(value));
                }
            }
        }

        public bool EmergencyDisableMode
        {
            get => _emergencyDisableMode;
            set
            {
                if (SetProperty(ref _emergencyDisableMode, value))
                {
                    IsEmergencyDisableVisible = value;
                }
            }
        }

        public string BlockingLevelDescription
        {
            get => _blockingLevelDescription;
            set => SetProperty(ref _blockingLevelDescription, value);
        }

        public bool IsEmergencyDisableVisible
        {
            get => _isEmergencyDisableVisible;
            set => SetProperty(ref _isEmergencyDisableVisible, value);
        }

        public Array BlockingLevels => Enum.GetValues(typeof(BlockingLevel));

        private void LoadParentalControlData()
        {
            try
            {
                // Load user profiles - for now create a default one
                UserProfiles.Clear();
                var defaultProfile = _parentalControlService.GetUserProfile("default");
                UserProfiles.Add(new UserProfile
                {
                    Name = "Default User",
                    AgeGroup = $"{defaultProfile.Age}+",
                    IsActive = true,
                    ScreenTimeUsed = TimeSpan.Zero,
                    ScreenTimeLimit = defaultProfile.Restrictions.DailyScreenTimeLimit
                });

                // Initialize content filters based on service filters
                ContentFilters.Clear();
                // Since we don't have a direct method to get all filters, we'll create based on restrictions
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Predator Detection",
                    IsBlocked = defaultProfile.Restrictions.BlockPredators,
                    Keywords = new List<string> { "grooming", "secret friend", "meet offline" }
                });
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Inappropriate Content",
                    IsBlocked = defaultProfile.Restrictions.BlockInappropriate,
                    Keywords = new List<string> { "porn", "sex", "adult", "xxx" }
                });
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Violence",
                    IsBlocked = defaultProfile.Restrictions.BlockViolence,
                    Keywords = new List<string> { "kill", "murder", "blood", "gore" }
                });
                ContentFilters.Add(new ContentFilter
                {
                    Category = "Bullying",
                    IsBlocked = defaultProfile.Restrictions.BlockBullying,
                    Keywords = new List<string> { "stupid", "ugly", "worthless", "loser" }
                });

                // Load current settings
                DailyScreenTimeLimit = defaultProfile.Restrictions.DailyScreenTimeLimit;
                BlockSocialMedia = defaultProfile.Restrictions.BlockedCategories.Contains("Social Media");
                BlockAdultContent = defaultProfile.Restrictions.BlockInappropriate;
                BlockGambling = defaultProfile.Restrictions.BlockedCategories.Contains("Gambling");
                MonitorChatActivity = true; // Default to enabled
                EnableSafeSearch = true; // Default to enabled

                // Load blocking level settings
                CurrentBlockingLevel = defaultProfile.Restrictions.BlockingLevel;
                EmergencyDisableMode = defaultProfile.Restrictions.EmergencyDisableMode;
                IsEnabled = defaultProfile.Restrictions.IsEnabled && !EmergencyDisableMode;
                UpdateBlockingLevelDescription();

                // Recent blocks would need to be tracked separately - for now empty
                RecentBlocks.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading parental control data: {ex.Message}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddUserProfile()
        {
            // Simple implementation: add a new user profile
            var newProfile = new UserProfile
            {
                Name = "New User",
                AgeGroup = "13+",
                IsActive = false,
                ScreenTimeUsed = TimeSpan.Zero,
                ScreenTimeLimit = TimeSpan.FromHours(2)
            };
            UserProfiles.Add(newProfile);
            MessageBox.Show("New user profile added. Please configure the settings.", "Profile Added", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RemoveUserProfile(UserProfile profile)
        {
            if (profile != null)
            {
                var result = MessageBox.Show($"Remove user profile '{profile.Name}'?", "Confirm Removal", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    UserProfiles.Remove(profile);
                    // Remove from service if possible
                    // _parentalControlService.RemoveUserProfile(profile.Name);
                }
            }
        }

        private async Task UpdateFilters()
        {
            try
            {
                // Collect service filters to add and perform the blocking work in a background thread
                var filtersToAdd = ContentFilters.Select(filter => new PcFutureShield.Common.Services.ContentFilter
                {
                    Id = filter.Category.ToLower().Replace(" ", "_"),
                    Name = filter.Category,
                    Enabled = filter.IsBlocked,
                    Keywords = filter.BlockedSites
                }).ToList();

                // Persist filters using real async I/O
                foreach (var serviceFilter in filtersToAdd)
                {
                    await _parentalControlService.AddContentFilterAsync(serviceFilter).ConfigureAwait(false);
                }

                // Refresh UI-bound data on UI thread
                await System.Windows.Application.Current?.Dispatcher.InvokeAsync(() =>
                {
                    LoadParentalControlData(); // Refresh data
                    MessageBox.Show("Content filters updated successfully.", "Update Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to update filters: {ex.Message}", "Update Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewBlockedContent()
        {
            var blocks = RecentBlocks;
            if (blocks.Any())
            {
                var blockText = string.Join("\n", blocks.Select(b => $"{b.BlockedTime:yyyy-MM-dd HH:mm}: {b.Content} by {b.UserName}"));
                MessageBox.Show($"Blocked Content:\n{blockText}", "Blocked Content", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            else
            {
                MessageBox.Show("No recent blocked content.", "Blocked Content", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void UpdateContentFilter(string category, bool isBlocked)
        {
            var filter = ContentFilters.FirstOrDefault(f => f.Category == category);
            if (filter != null)
            {
                filter.IsBlocked = isBlocked;
            }
            else
            {
                ContentFilters.Add(new ContentFilter
                {
                    Category = category,
                    IsBlocked = isBlocked,
                    Keywords = GetDefaultKeywordsForCategory(category)
                });
            }
        }

        private List<string> GetDefaultKeywordsForCategory(string category)
        {
            return category switch
            {
                "Social Media" => new List<string> { "facebook", "twitter", "instagram", "tiktok", "snapchat" },
                "Adult Content" => new List<string> { "porn", "adult", "xxx", "sex" },
                "Gambling" => new List<string> { "casino", "poker", "betting", "lottery" },
                _ => new List<string>()
            };
        }
        public class UserProfile
        {
            public string Name { get; set; } = string.Empty;
            public string AgeGroup { get; set; } = string.Empty;
            public bool IsActive { get; set; }
            public TimeSpan ScreenTimeUsed { get; set; }
            public TimeSpan ScreenTimeLimit { get; set; }
            public class BlockedContent
            {
                public string Url { get; set; } = string.Empty;
                public string ContentType { get; set; } = string.Empty;
                public string BlockReason { get; set; } = string.Empty;
                public DateTime BlockedTime { get; set; }
                public string UserName { get; set; } = string.Empty;

                // Added for XAML bindings that expect a 'Content' field
                public string Content => !string.IsNullOrEmpty(BlockReason) ? $"{BlockReason} ({Url})" : Url;
                public class ContentFilter
                {
                    public string Category { get; set; } = string.Empty;
                    public bool IsBlocked { get; set; }
                    public string Keywords { get; set; } = string.Empty;
                    public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
                    protected void OnPropertyChanged(string name) =>
                        PropertyChanged?.Invoke(this, new System.ComponentModel.PropertyChangedEventArgs(name));

                    private string _content;
                    public string Content
                    {
                        get => _content;
                        set
                        {
                            if (_content != value)
                            {
                                _content = value;
                                OnPropertyChanged(nameof(Content));
                            }
                        }
                    }
                }
            }
        }

        private async Task ToggleNetworkBlockingAsync(bool enable)
        {
            try
            {
                if (enable)
                {
                    var success = await _parentalControlService.EnableNetworkBlockingAsync();
                    if (success)
                    {
                        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                        {
                            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Parental Control", "Network-level blocking enabled successfully."); } catch { }
                        });
                    }
                    else
                    {
                        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                        {
                            MessageBox.Show("Failed to enable network blocking. Please run as administrator.", "Parental Control Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                        });
                    }
                }
                else
                {
                    var success = await _parentalControlService.DisableNetworkBlockingAsync();
                    if (success)
                    {
                        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                        {
                            try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Parental Control", "Network-level blocking disabled."); } catch { }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Failed to toggle network blocking: {ex.Message}", "Parental Control Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        private async Task UpdateDomainBlockingAsync(string category, bool block)
        {
            try
            {
                // Use category-based blocking for better performance
                var categoryKey = GetCategoryKey(category);

                if (block)
                {
                    await _parentalControlService.BlockCategoryAsync(categoryKey);
                }
                else
                {
                    await _parentalControlService.UnblockCategoryAsync(categoryKey);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update domain blocking for {category}: {ex.Message}");
            }
        }

        private string GetCategoryKey(string category)
        {
            return category.ToLower() switch
            {
                "adult content" => "adult",
                "gambling" => "gambling",
                "social media" => "social",
                _ => category.ToLower()
            };
        }

        /// <summary>
        /// Set the blocking level for parental controls
        /// </summary>
        private async Task SetBlockingLevelAsync(BlockingLevel level)
        {
            try
            {
                var success = await _parentalControlService.SetBlockingLevelAsync(level);
                if (success)
                {
                    CurrentBlockingLevel = level;
                    UpdateBlockingLevelDescription();

                    System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                    {
                        try
                        {
                            Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo(
                                "Parental Control",
                                $"Blocking level set to: {level}");
                        }
                        catch { }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Failed to set blocking level: {ex.Message}", "Parental Control Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        /// <summary>
        /// Emergency disable all parental controls
        /// </summary>
        private async Task EmergencyDisableAsync()
        {
            try
            {
                var result = MessageBox.Show(
                    "This will immediately disable ALL parental controls and unblock all websites.\n\nAre you sure you want to continue?",
                    "Emergency Disable",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _parentalControlService.EmergencyDisableAsync();
                    if (success)
                    {
                        EmergencyDisableMode = true;
                        IsEnabled = false;

                        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                        {
                            try
                            {
                                Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowWarning(
                                    "Emergency Disable",
                                    "All parental controls have been disabled. Use 'Re-Enable Protection' to restore blocking.");
                            }
                            catch { }
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Failed to emergency disable: {ex.Message}", "Emergency Disable Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        /// <summary>
        /// Re-enable parental controls after emergency disable
        /// </summary>
        private async Task ReEnableProtectionAsync()
        {
            try
            {
                var success = await _parentalControlService.ReEnableAfterEmergencyAsync(CurrentBlockingLevel);
                if (success)
                {
                    EmergencyDisableMode = false;
                    IsEnabled = true;

                    System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                    {
                        try
                        {
                            Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo(
                                "Protection Re-Enabled",
                                $"Parental controls restored at {CurrentBlockingLevel} level.");
                        }
                        catch { }
                    });
                }
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Failed to re-enable protection: {ex.Message}", "Re-Enable Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        /// <summary>
        /// Update the description text for the current blocking level
        /// </summary>
        private void UpdateBlockingLevelDescription()
        {
            BlockingLevelDescription = CurrentBlockingLevel switch
            {
                BlockingLevel.Off => "Off - No content blocking",
                BlockingLevel.Low => "Low - Basic adult content blocking only",
                BlockingLevel.Medium => "Medium - Adult content, gambling, violence, and drugs",
                BlockingLevel.High => "High - Medium + social media restrictions and enhanced filtering",
                BlockingLevel.Maximum => "Maximum - All categories with aggressive keyword filtering",
                _ => "Unknown blocking level"
            };
        }

        private List<string> GetDomainsForCategory(string category)
        {
            return category.ToLower() switch
            {
                "adult content" => new List<string>
                {
                    "pornhub.com", "xvideos.com", "xnxx.com", "redtube.com", "youporn.com",
                    "tube8.com", "spankbang.com", "xhamster.com", "beeg.com", "sex.com"
                },
                "gambling" => new List<string>
                {
                    "bet365.com", "888casino.com", "pokerstars.com", "williamhill.com",
                    "ladbrokes.com", "betfair.com", "bwin.com", "unibet.com", "betway.com"
                },
                "social media" => new List<string>
                {
                    "onlyfans.com", "chaturbate.com", "cam4.com", "myfreecams.com"
                },
                _ => new List<string>()
            };
        }

        private async Task TestBlockingAsync()
        {
            try
            {
                var testService = new PcFutureShield.Common.Services.ParentalControlTestService();

                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    try { Services.ServiceLocator.GetOrNull<PcFutureShield.UI.Services.INotificationService>()?.ShowInfo("Testing", "Running parental control blocking tests..."); } catch { }
                });

                var testResult = await testService.TestBlockingAsync();
                var report = testService.GenerateTestReport(testResult);

                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(report, "Parental Control Test Results", MessageBoxButton.OK,
                        testResult.OverallSuccess ? MessageBoxImage.Information : MessageBoxImage.Warning);
                });
            }
            catch (Exception ex)
            {
                System.Windows.Application.Current?.Dispatcher.Invoke(() =>
                {
                    MessageBox.Show($"Test failed: {ex.Message}", "Test Error", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }
    }
}



