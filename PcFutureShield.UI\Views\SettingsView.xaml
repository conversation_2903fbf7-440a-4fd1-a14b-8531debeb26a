<UserControl x:Class="PcFutureShield.UI.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">
    <Grid Background="{DynamicResource PrimaryColorBrush}">
        <ScrollViewer VerticalScrollBarVisibility="Auto">
            <ScrollViewer.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF074EC1" Offset="1"/>
                </LinearGradientBrush>
            </ScrollViewer.Background>
            <StackPanel Margin="20">
                <TextBlock Text="Settings &amp; Configuration" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

                <!-- Settings Sections -->
                <UniformGrid Columns="2" Rows="3" Margin="0,0,0,24">
                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Appearance" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <TextBlock Text="Theme:" FontSize="16" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <ComboBox ItemsSource="{Binding AvailableThemes}" SelectedItem="{Binding SelectedTheme, Mode=TwoWay}" Margin="0,0,0,16" Height="32" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}"/>
                            <CheckBox Content="Enable Animations" IsChecked="{Binding EnableAnimations}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Show Notifications" IsChecked="{Binding ShowNotifications}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Auto-start with Windows" IsChecked="{Binding AutoStartWithWindows}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Security Settings" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <CheckBox Content="Real-time Protection" IsChecked="{Binding RealTimeProtectionEnabled}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Auto-update Definitions" IsChecked="{Binding AutoUpdateDefinitions}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Cloud Protection" IsChecked="{Binding CloudProtectionEnabled}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Scan Removable Drives" IsChecked="{Binding ScanRemovableDrives}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="Scan Schedule:" FontSize="16" Margin="0,8,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <ComboBox ItemsSource="{Binding ScanSchedules}" SelectedItem="{Binding SelectedScanSchedule, Mode=TwoWay}" Height="32" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Performance" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <CheckBox Content="Low Resource Mode" IsChecked="{Binding LowResourceMode}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Background Scanning" IsChecked="{Binding BackgroundScanningEnabled}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="CPU Usage Limit:" FontSize="16" Margin="0,8,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <Slider Value="{Binding CpuUsageLimit, Mode=TwoWay}" Minimum="10" Maximum="90" Margin="0,0,0,8"/>
                            <TextBlock Text="{Binding CpuUsageLimit, StringFormat={}{0}%}" FontSize="14" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18">
                        <StackPanel>
                            <TextBlock Text="Privacy &amp; Data" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <CheckBox Content="Send Anonymous Statistics" IsChecked="{Binding SendAnonymousStats}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Enable Crash Reporting" IsChecked="{Binding EnableCrashReporting}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <CheckBox Content="Store Scan History" IsChecked="{Binding StoreScanHistory}" Margin="0,0,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBlock Text="Data Retention (days):" FontSize="16" Margin="0,8,0,8" Foreground="{DynamicResource PrimaryFontBrush}"/>
                            <TextBox Text="{Binding DataRetentionDays, Mode=TwoWay}" Margin="0,0,0,8" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" BorderBrush="{DynamicResource BorderBrush}"/>
                            <Button Content="Clear All Data" Command="{Binding ClearAllDataCommand}" Style="{DynamicResource ChromeButtonStyle}" Width="120" Height="32"/>
                        </StackPanel>
                    </Border>

                    <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" Grid.ColumnSpan="2">
                        <StackPanel>
                            <TextBlock Text="About PcFutureShield" FontWeight="SemiBold" FontSize="20" Foreground="{DynamicResource SecondaryFontBrush}" Margin="0,0,0,12"/>
                            <UniformGrid Columns="3" Rows="1">
                                <StackPanel>
                                    <TextBlock Text="Version:" FontSize="14" Margin="0,0,0,4" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                    <TextBlock Text="{Binding ApplicationVersion}" FontSize="16" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Text="License:" FontSize="14" Margin="0,0,0,4" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                    <TextBlock Text="{Binding LicenseStatus}" FontSize="16" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                                </StackPanel>
                                <StackPanel>
                                    <TextBlock Text="Last Update:" FontSize="14" Margin="0,0,0,4" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                    <TextBlock Text="{Binding LastUpdateCheck}" FontSize="16" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                                </StackPanel>
                            </UniformGrid>
                        </StackPanel>
                    </Border>
                </UniformGrid>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,24,0,0">
                    <Button Content="Save Settings" Command="{Binding SaveSettingsCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="160" Height="48" Margin="0,0,20,0"/>
                    <Button Content="Reset to Defaults" Command="{Binding ResetToDefaultsCommand}" Style="{DynamicResource ChromeButtonStyle}" Width="160" Height="48" Margin="0,0,20,0"/>
                    <Button Content="Check for Updates" Command="{Binding CheckForUpdatesCommand}" Style="{DynamicResource GlassButtonStyle}" Width="180" Height="48"/>
                </StackPanel>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
