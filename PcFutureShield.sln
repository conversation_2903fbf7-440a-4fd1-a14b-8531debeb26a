﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36401.2
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PcFutureShield.UI", "PcFutureShield.UI\PcFutureShield.UI.csproj", "{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PcFutureShield.Engine", "PcFutureShield.Engine\PcFutureShield.Engine.csproj", "{34890464-29D5-41C8-940D-F0481B2F3D54}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PcFutureShield.Common", "PcFutureShield.Common\PcFutureShield.Common.csproj", "{9A3A12F8-B17E-4303-9732-AB7C53822434}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PcFutureShield.Tools.AutoValidator", "PcFutureShield.Tools.AutoValidator\PcFutureShield.Tools.AutoValidator.csproj", "{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Debug|x64.Build.0 = Debug|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Debug|x86.Build.0 = Debug|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Release|Any CPU.Build.0 = Release|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Release|x64.ActiveCfg = Release|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Release|x64.Build.0 = Release|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Release|x86.ActiveCfg = Release|Any CPU
		{8EBFB0B0-79BC-4C70-B3D8-3CF6B703EC9F}.Release|x86.Build.0 = Release|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Debug|x64.ActiveCfg = Debug|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Debug|x64.Build.0 = Debug|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Debug|x86.ActiveCfg = Debug|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Debug|x86.Build.0 = Debug|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Release|Any CPU.Build.0 = Release|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Release|x64.ActiveCfg = Release|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Release|x64.Build.0 = Release|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Release|x86.ActiveCfg = Release|Any CPU
		{34890464-29D5-41C8-940D-F0481B2F3D54}.Release|x86.Build.0 = Release|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Debug|x64.ActiveCfg = Debug|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Debug|x64.Build.0 = Debug|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Debug|x86.ActiveCfg = Debug|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Debug|x86.Build.0 = Debug|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Release|Any CPU.Build.0 = Release|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Release|x64.ActiveCfg = Release|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Release|x64.Build.0 = Release|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Release|x86.ActiveCfg = Release|Any CPU
		{9A3A12F8-B17E-4303-9732-AB7C53822434}.Release|x86.Build.0 = Release|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Debug|x64.ActiveCfg = Debug|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Debug|x64.Build.0 = Debug|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Debug|x86.Build.0 = Debug|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Release|Any CPU.Build.0 = Release|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Release|x64.ActiveCfg = Release|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Release|x64.Build.0 = Release|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Release|x86.ActiveCfg = Release|Any CPU
		{3ADA9D5D-37F2-4CF0-A7FA-5D2E20CD7ABF}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {E4967C65-D981-42E9-AC5B-1E4528434AFE}
	EndGlobalSection
EndGlobal
