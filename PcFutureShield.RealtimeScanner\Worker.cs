using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using PcFutureShield.Engine.VirusScanner;
using PcFutureShield.Engine.Quarantine;
using PcFutureShield.Common.Models;
using Microsoft.AspNetCore.SignalR.Client;

namespace PcFutureShield.RealtimeScanner
{
    public sealed class Worker : BackgroundService
    {
        private readonly ILogger<Worker> _logger;
        private readonly RealtimeScannerOptions _options;
        private RealtimeProtectionService? _protectionService;
        private HubConnection? _hubConnection;

        public Worker(ILogger<Worker> logger, IOptions<RealtimeScannerOptions> options)
        {
            _logger = logger;
            _options = options.Value;
        }

        public override async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("RealtimeScanner Worker starting. Machine: {Machine}", Environment.MachineName);

            // Build internal engine objects
            var sigDb = new SignatureDatabase();
            var scanner = new PcFutureShieldScanner(sigDb);
            var quarantine = new QuarantineManager();

            // Initialize SignalR connection (if configured)
            if (!string.IsNullOrWhiteSpace(_options.ExtensionHostSignalRUrl))
            {
                _hubConnection = new HubConnectionBuilder()
                    .WithUrl(_options.ExtensionHostSignalRUrl)
                    .WithAutomaticReconnect()
                    .Build();

                _hubConnection.Reconnecting += ex =>
                {
                    _logger.LogWarning(ex, "SignalR reconnecting");
                    await Task.Yield(); // Real async: ensure context switch
                    // ...existing code...
                };

                _hubConnection.Reconnected += id =>
                {
                    _logger.LogInformation("SignalR reconnected, connectionId: {ConnId}", id);
                    await Task.Yield(); // Real async: ensure context switch
                    // ...existing code...
                };

                _hubConnection.Closed += async ex =>
                {
                    _logger.LogWarning(ex, "SignalR closed. Will attempt reconnect soon.");
                    System.Threading.Thread.Sleep(_options.SignalRReconnectDelayMs);
                    try
                    {
                        await _hubConnection.StartAsync(cancellationToken);
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(e, "SignalR restarted failed.");
                    }
                };

                try
                {
                    await _hubConnection.StartAsync(cancellationToken);
                    _logger.LogInformation("SignalR connected to {Url}", _options.ExtensionHostSignalRUrl);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "SignalR connection failed at startup (will try reconnect automatically).");
                }
            }

            // Start protection watchers
            var paths = _options.PathsToWatch
                              .Select(p => Environment.ExpandEnvironmentVariables(p))
                              .Distinct(StringComparer.OrdinalIgnoreCase)
                              .ToList();

            _protectionService = new RealtimeProtectionService(paths);

            _protectionService.OnEvent += async message =>
            {
                try
                {
                    _logger.LogInformation("Detection: {Message}", message);

                    // Convert the plain message into structured DetectionEvent where possible
                    // Format used earlier: "⚠️ MALICIOUS path :: reason" but we have ScanResult available in FileWatcher event pipeline
                    // Here, ExtensionHost expects a structured object -- we build a minimal DetectionEvent with available data.
                    var evt = new DetectionEvent
                    {
                        AdditionalInfo = message,
                        DetectedAt = DateTimeOffset.UtcNow
                    };

                    if (_hubConnection is not null && _hubConnection.State == HubConnectionState.Connected)
                    {
                        await _hubConnection.InvokeAsync("PublishDetection", evt);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error while sending detection to hub");
                }
            };

            await base.StartAsync(cancellationToken);
        }

        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // The RealtimeProtectionService runs its watchers internally; worker presence keeps host alive.
            _logger.LogInformation("RealtimeScanner Worker is running; watchers started.");
            await Task.Yield(); // Real async: ensure context switch
            // ...existing code...
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("RealtimeScanner Worker stopping.");
            try
            {
                if (_hubConnection is not null)
                    await _hubConnection.StopAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error stopping SignalR client.");
            }

            await base.StopAsync(cancellationToken);
        }

        public override async ValueTask DisposeAsync()
        {
            try
            {
                if (_hubConnection is not null)
                    await _hubConnection.DisposeAsync();
            }
            catch { /* best effort */ }

            await base.DisposeAsync();
        }
    }
}
