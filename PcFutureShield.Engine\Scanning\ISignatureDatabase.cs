using System;
using System.Threading;
using System.Threading.Tasks;

namespace PcFutureShield.Engine.Scanning
{
    /// <summary>
    /// Provides access to virus signature database
    /// </summary>
    public interface ISignatureDatabase : IDisposable
    {
        /// <summary>
        /// Initializes the signature database
        /// </summary>
        Task<bool> InitializeAsync(CancellationToken ct = default);

        /// <summary>
        /// Updates the signature database
        /// </summary>
        Task<bool> UpdateAsync(CancellationToken ct = default);

        /// <summary>
        /// Looks up a file signature in the database
        /// </summary>
        Task<ThreatSignature?> LookupSignatureAsync(string signature, CancellationToken ct = default);
    }

    /// <summary>
    /// Represents a known threat signature
    /// </summary>
    public class ThreatSignature
    {
        /// <summary>
        /// Name of the threat
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Type of threat (e.g., Virus, Trojan, Ransomware)
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// Severity of the threat
        /// </summary>
        public ThreatSeverity Severity { get; set; }
    }
}
