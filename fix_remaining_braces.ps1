# PowerShell script to fix remaining closing brace issues

# Function to fix missing closing braces in ViewModels
function Fix-ViewModelBraces {
    param($FilePath)
    
    if (Test-Path $FilePath) {
        Write-Host "Fixing braces in $FilePath..."
        
        $content = Get-Content $FilePath -Raw
        
        # Common patterns to fix
        $patterns = @(
            # Property without closing brace followed by public
            '(\s+set\s*=>\s*SetProperty\([^;]+;\s*)(\r?\n\s*public\s+)',
            # Property without closing brace at end of class
            '(\s+set\s*=>\s*SetProperty\([^;]+;\s*)(\r?\n\s*}\s*$)',
            # Property without closing brace followed by private method
            '(\s+set\s*=>\s*SetProperty\([^;]+;\s*)(\r?\n\s*private\s+)',
            # Property without closing brace followed by // comment
            '(\s+set\s*=>\s*SetProperty\([^;]+;\s*)(\r?\n\s*//)',
            # Property without closing brace followed by ICommand
            '(\s+set\s*=>\s*SetProperty\([^;]+;\s*)(\r?\n\s*public\s+ICommand\s+)'
        )
        
        foreach ($pattern in $patterns) {
            $content = $content -replace $pattern, '$1' + "`r`n        }`r`n`r`n" + '$2'
        }
        
        # Fix specific method call patterns
        $content = $content -replace '(\s+UpdateContentFilter\([^)]+\);\s*)(\r?\n\s*}\s*)(\r?\n\s*public\s+)', '$1$2' + "`r`n        }`r`n`r`n" + '$3'
        
        Set-Content $FilePath $content -Encoding UTF8
        Write-Host "Fixed braces in $FilePath"
    }
}

# Fix all ViewModels with brace issues
$viewModels = @(
    "PcFutureShield.UI\ViewModels\AdminOverrideViewModel.cs",
    "PcFutureShield.UI\ViewModels\GamingProtectionViewModel.cs", 
    "PcFutureShield.UI\ViewModels\BrowserExtensionViewModel.cs",
    "PcFutureShield.UI\ViewModels\PcOptimizationViewModel.cs",
    "PcFutureShield.UI\ViewModels\ParentalControlViewModel.cs",
    "PcFutureShield.UI\ViewModels\DashboardViewModel.cs",
    "PcFutureShield.UI\ViewModels\QuarantineViewModel.cs",
    "PcFutureShield.UI\ViewModels\ThemeSelectorViewModel.cs",
    "PcFutureShield.UI\ViewModels\EnhancedScanViewModel.cs",
    "PcFutureShield.UI\ViewModels\EventLogViewModel.cs",
    "PcFutureShield.UI\ViewModels\UpdatesViewModel.cs"
)

foreach ($vm in $viewModels) {
    Fix-ViewModelBraces $vm
}

Write-Host "All ViewModels fixed!"
