# FORCE CLEAN HOSTS FILE - Run as Administrator
# This will completely overwrite the hosts file with the original Windows content

Write-Host "FORCE CLEANING HOSTS FILE" -ForegroundColor Red
Write-Host "=========================" -ForegroundColor Red

$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"

# Create backup
Write-Host "Creating backup..." -ForegroundColor Yellow
$backupPath = "$hostsPath.force.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Copy-Item $hostsPath $backupPath -Force
Write-Host "Backup created: $backupPath" -ForegroundColor Green

# Create the original Windows hosts file content (keeping Docker entries)
Write-Host "Writing clean hosts file..." -ForegroundColor Yellow

$cleanHostsContent = @"
# Copyright (c) 1993-2009 Microsoft Corp.
#
# This is a sample HOSTS file used by Microsoft TCP/IP for Windows.
#
# This file contains the mappings of IP addresses to host names. Each
# entry should be kept on an individual line. The IP address should
# be placed in the first column followed by the corresponding host name.
# The IP address and the host name should be separated by at least one
# space.
#
# Additionally, comments (such as these) may be inserted on individual
# lines or following the machine name denoted by a '#' symbol.
#
# For example:
#
#      ************     rhino.acme.com          # source server
#       ***********     x.acme.com              # x client host

# localhost name resolution is handled within DNS itself.
#	127.0.0.1       localhost
#	::1             localhost

# Added by Docker Desktop
************** host.docker.internal
************** gateway.docker.internal
# To allow the same kube context to work on the host and the container:
127.0.0.1 kubernetes.docker.internal
# End of section
"@

# Force write the clean content
try {
    $cleanHostsContent | Out-File -FilePath $hostsPath -Encoding ASCII -Force
    Write-Host "Clean hosts file written successfully!" -ForegroundColor Green
} catch {
    Write-Host "Error writing hosts file: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure you're running as Administrator" -ForegroundColor Yellow
    exit 1
}

# Verify the cleanup
Write-Host "Verifying cleanup..." -ForegroundColor Yellow
$currentContent = Get-Content $hostsPath
$pcFutureShieldEntries = $currentContent | Where-Object { $_ -match "PcFutureShield" }

if ($pcFutureShieldEntries.Count -eq 0) {
    Write-Host "✓ SUCCESS: Hosts file is now clean!" -ForegroundColor Green
} else {
    Write-Host "✗ ERROR: Still found $($pcFutureShieldEntries.Count) PcFutureShield entries" -ForegroundColor Red
}

# Show current hosts file content
Write-Host ""
Write-Host "Current hosts file content:" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan
Get-Content $hostsPath | ForEach-Object { Write-Host $_ -ForegroundColor White }

# Flush DNS cache multiple times
Write-Host ""
Write-Host "Flushing DNS cache..." -ForegroundColor Yellow
for ($i = 1; $i -le 3; $i++) {
    ipconfig /flushdns | Out-Null
    Start-Sleep -Seconds 1
}
Write-Host "DNS cache flushed!" -ForegroundColor Green

# Test connectivity
Write-Host ""
Write-Host "Testing internet connectivity..." -ForegroundColor Yellow
$testSites = @("google.com", "microsoft.com", "github.com", "facebook.com", "bet365.com")

foreach ($site in $testSites) {
    try {
        $result = Test-NetConnection -ComputerName $site -Port 80 -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($result) {
            Write-Host "✓ $site - ACCESSIBLE" -ForegroundColor Green
        } else {
            Write-Host "✗ $site - NOT ACCESSIBLE" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ $site - ERROR TESTING" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "HOSTS FILE FORCE CLEANUP COMPLETE!" -ForegroundColor Green
Write-Host ""
Write-Host "What was done:" -ForegroundColor Cyan
Write-Host "• Created backup: $backupPath" -ForegroundColor White
Write-Host "• Completely overwrote hosts file with clean content" -ForegroundColor White
Write-Host "• Preserved Docker Desktop entries" -ForegroundColor White
Write-Host "• Removed ALL PcFutureShield entries" -ForegroundColor White
Write-Host "• Flushed DNS cache multiple times" -ForegroundColor White
Write-Host ""
Write-Host "Your internet access should now be completely restored!" -ForegroundColor Yellow

Read-Host "Press Enter to exit"
