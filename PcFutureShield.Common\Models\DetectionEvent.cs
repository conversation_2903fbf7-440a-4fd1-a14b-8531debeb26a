using System;

namespace PcFutureShield.Common.Models
{
    public sealed class DetectionEvent
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string SourceHost { get; set; } = Environment.MachineName;
        public string FilePath { get; set; } = string.Empty;
        public string Sha256 { get; set; } = string.Empty;
        public bool IsMalicious { get; set; }
        public string Reason { get; set; } = string.Empty;
        public double Entropy { get; set; }
        public DateTimeOffset DetectedAt { get; set; } = DateTimeOffset.UtcNow;
        public string AdditionalInfo { get; set; }
    }
}
