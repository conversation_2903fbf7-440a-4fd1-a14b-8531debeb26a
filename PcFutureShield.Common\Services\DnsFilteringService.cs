#nullable enable

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using Microsoft.Win32;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced DNS filtering service for parental controls and content blocking
    /// </summary>
    public class DnsFilteringService
    {
        private readonly string _hostsFilePath;
        private readonly List<string> _blockedDomains;
        private readonly Dictionary<string, List<string>> _categoryDomains;

        public DnsFilteringService()
        {
            _hostsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers", "etc", "hosts");
            _blockedDomains = new List<string>();
            _categoryDomains = InitializeCategoryDomains();
        }

        /// <summary>
        /// Enable comprehensive DNS filtering with family-safe DNS servers
        /// </summary>
        public async Task<bool> EnableDnsFilteringAsync()
        {
            try
            {
                // Set family-safe DNS servers
                await SetFamilySafeDnsServersAsync();

                // Update hosts file with blocked domains
                await UpdateHostsFileAsync();

                // Flush DNS cache
                await FlushDnsCacheAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to enable DNS filtering: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable DNS filtering and restore default settings
        /// </summary>
        public async Task<bool> DisableDnsFilteringAsync()
        {
            try
            {
                // Restore automatic DNS
                await RestoreAutomaticDnsAsync();

                // Clean hosts file
                await CleanHostsFileAsync();

                // Flush DNS cache
                await FlushDnsCacheAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to disable DNS filtering: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Block specific domains by category
        /// </summary>
        public async Task<bool> BlockCategoryAsync(string category)
        {
            try
            {
                if (_categoryDomains.TryGetValue(category.ToLower(), out var domains))
                {
                    foreach (var domain in domains)
                    {
                        if (!_blockedDomains.Contains(domain, StringComparer.OrdinalIgnoreCase))
                        {
                            _blockedDomains.Add(domain);
                        }
                    }

                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to block category {category}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unblock specific domains by category
        /// </summary>
        public async Task<bool> UnblockCategoryAsync(string category)
        {
            try
            {
                if (_categoryDomains.TryGetValue(category.ToLower(), out var domains))
                {
                    foreach (var domain in domains)
                    {
                        _blockedDomains.Remove(domain);
                    }

                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to unblock category {category}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Add custom domain to block list
        /// </summary>
        public async Task<bool> BlockDomainAsync(string domain)
        {
            try
            {
                if (!_blockedDomains.Contains(domain, StringComparer.OrdinalIgnoreCase))
                {
                    _blockedDomains.Add(domain);
                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to block domain {domain}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove domain from block list
        /// </summary>
        public async Task<bool> UnblockDomainAsync(string domain)
        {
            try
            {
                if (_blockedDomains.Remove(domain))
                {
                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to unblock domain {domain}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get list of currently blocked domains
        /// </summary>
        public List<string> GetBlockedDomains()
        {
            return new List<string>(_blockedDomains);
        }

        /// <summary>
        /// Check if DNS filtering is currently active
        /// </summary>
        public async Task<bool> IsFilteringActiveAsync()
        {
            try
            {
                // Check if hosts file contains our entries
                if (File.Exists(_hostsFilePath))
                {
                    var content = await File.ReadAllTextAsync(_hostsFilePath);
                    return content.Contains("# PcFutureShield DNS Filter");
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private Dictionary<string, List<string>> InitializeCategoryDomains()
        {
            return new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
            {
                ["adult"] = new List<string>
                {
                    "pornhub.com", "xvideos.com", "xnxx.com", "redtube.com", "youporn.com",
                    "tube8.com", "spankbang.com", "xhamster.com", "beeg.com", "sex.com",
                    "porn.com", "xxx.com", "adult.com", "playboy.com", "penthouse.com",
                    "onlyfans.com", "chaturbate.com", "cam4.com", "myfreecams.com"
                },
                ["gambling"] = new List<string>
                {
                    "bet365.com", "888casino.com", "pokerstars.com", "williamhill.com",
                    "ladbrokes.com", "betfair.com", "bwin.com", "unibet.com", "betway.com",
                    "casino.com", "slots.com", "poker.com", "blackjack.com", "roulette.com",
                    "draftkings.com", "fanduel.com", "bovada.com", "ignition.com"
                },
                ["social"] = new List<string>
                {
                    "tiktok.com", "snapchat.com", "discord.com", "reddit.com"
                },
                ["violence"] = new List<string>
                {
                    "liveleak.com", "bestgore.com", "theync.com", "documenting-reality.com"
                }
            };
        }

        private async Task SetFamilySafeDnsServersAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                        .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

                    foreach (var ni in networkInterfaces)
                    {
                        var adapterName = ni.Name;

                        // Set OpenDNS FamilyShield (blocks adult content)
                        ExecuteNetshCommand($"interface ip set dns \"{adapterName}\" static **************");
                        ExecuteNetshCommand($"interface ip add dns \"{adapterName}\" ************** index=2");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to set family-safe DNS: {ex.Message}");
                }
            });
        }

        private async Task RestoreAutomaticDnsAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                        .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

                    foreach (var ni in networkInterfaces)
                    {
                        var adapterName = ni.Name;
                        ExecuteNetshCommand($"interface ip set dns \"{adapterName}\" dhcp");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to restore automatic DNS: {ex.Message}");
                }
            });
        }

        private void ExecuteNetshCommand(string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "netsh",
                        Arguments = arguments,
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        Verb = "runas" // Run as administrator
                    }
                };
                process.Start();
                process.WaitForExit();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to execute netsh command: {ex.Message}");
            }
        }

        private async Task UpdateHostsFileAsync()
        {
            try
            {
                var hostsContent = new List<string>();

                // Read existing hosts file
                if (File.Exists(_hostsFilePath))
                {
                    var existingLines = await File.ReadAllLinesAsync(_hostsFilePath);

                    // Keep non-PcFutureShield entries
                    foreach (var line in existingLines)
                    {
                        if (!line.Contains("# PcFutureShield DNS Filter"))
                        {
                            hostsContent.Add(line);
                        }
                    }
                }

                // Add PcFutureShield blocked domains
                if (_blockedDomains.Any())
                {
                    hostsContent.Add("");
                    hostsContent.Add("# PcFutureShield DNS Filter - Blocked Domains");

                    foreach (var domain in _blockedDomains)
                    {
                        hostsContent.Add($"127.0.0.1 {domain} # PcFutureShield DNS Filter");
                        hostsContent.Add($"127.0.0.1 www.{domain} # PcFutureShield DNS Filter");

                        // Block common subdomains
                        hostsContent.Add($"127.0.0.1 m.{domain} # PcFutureShield DNS Filter");
                        hostsContent.Add($"127.0.0.1 mobile.{domain} # PcFutureShield DNS Filter");
                    }

                    hostsContent.Add("# End PcFutureShield DNS Filter");
                }

                // Write back to hosts file
                await File.WriteAllLinesAsync(_hostsFilePath, hostsContent);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update hosts file: {ex.Message}");
            }
        }

        private async Task CleanHostsFileAsync()
        {
            try
            {
                if (!File.Exists(_hostsFilePath)) return;

                var existingLines = await File.ReadAllLinesAsync(_hostsFilePath);
                var cleanedLines = existingLines.Where(line => !line.Contains("# PcFutureShield DNS Filter")).ToList();

                await File.WriteAllLinesAsync(_hostsFilePath, cleanedLines);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to clean hosts file: {ex.Message}");
            }
        }

        private async Task FlushDnsCacheAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "ipconfig",
                        Arguments = "/flushdns",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to flush DNS cache: {ex.Message}");
            }
        }
    }
}
