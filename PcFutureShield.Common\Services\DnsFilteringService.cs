#nullable enable

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.NetworkInformation;
using System.Threading.Tasks;
using Microsoft.Win32;

namespace PcFutureShield.Common.Services
{
    /// <summary>
    /// Advanced DNS filtering service for parental controls and content blocking
    /// </summary>
    public class DnsFilteringService
    {
        private readonly string _hostsFilePath;
        private readonly List<string> _blockedDomains;
        private readonly Dictionary<string, List<string>> _categoryDomains;

        public DnsFilteringService()
        {
            _hostsFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers", "etc", "hosts");
            _blockedDomains = new List<string>();
            _categoryDomains = InitializeCategoryDomains();
        }

        /// <summary>
        /// Enable comprehensive DNS filtering with family-safe DNS servers
        /// </summary>
        public async Task<bool> EnableDnsFilteringAsync()
        {
            try
            {
                // Set family-safe DNS servers
                await SetFamilySafeDnsServersAsync();

                // Update hosts file with blocked domains
                await UpdateHostsFileAsync();

                // Flush DNS cache
                await FlushDnsCacheAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to enable DNS filtering: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Disable DNS filtering and restore default settings
        /// </summary>
        public async Task<bool> DisableDnsFilteringAsync()
        {
            try
            {
                // Restore automatic DNS
                await RestoreAutomaticDnsAsync();

                // Clean hosts file
                await CleanHostsFileAsync();

                // Flush DNS cache
                await FlushDnsCacheAsync();

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to disable DNS filtering: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Block specific domains by category
        /// </summary>
        public async Task<bool> BlockCategoryAsync(string category)
        {
            try
            {
                if (_categoryDomains.TryGetValue(category.ToLower(), out var domains))
                {
                    foreach (var domain in domains)
                    {
                        if (!_blockedDomains.Contains(domain, StringComparer.OrdinalIgnoreCase))
                        {
                            _blockedDomains.Add(domain);
                        }
                    }

                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to block category {category}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Unblock specific domains by category
        /// </summary>
        public async Task<bool> UnblockCategoryAsync(string category)
        {
            try
            {
                if (_categoryDomains.TryGetValue(category.ToLower(), out var domains))
                {
                    foreach (var domain in domains)
                    {
                        _blockedDomains.Remove(domain);
                    }

                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to unblock category {category}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Clear all blocked categories and domains
        /// </summary>
        public async Task<bool> ClearAllBlockedCategoriesAsync()
        {
            try
            {
                _blockedDomains.Clear();
                await UpdateHostsFileAsync();
                await FlushDnsCacheAsync();
                System.Diagnostics.Debug.WriteLine("All blocked categories cleared");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to clear blocked categories: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Add custom domain to block list
        /// </summary>
        public async Task<bool> BlockDomainAsync(string domain)
        {
            try
            {
                if (!_blockedDomains.Contains(domain, StringComparer.OrdinalIgnoreCase))
                {
                    _blockedDomains.Add(domain);
                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to block domain {domain}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove domain from block list
        /// </summary>
        public async Task<bool> UnblockDomainAsync(string domain)
        {
            try
            {
                if (_blockedDomains.Remove(domain))
                {
                    await UpdateHostsFileAsync();
                    await FlushDnsCacheAsync();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to unblock domain {domain}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get list of currently blocked domains
        /// </summary>
        public List<string> GetBlockedDomains()
        {
            return new List<string>(_blockedDomains);
        }

        /// <summary>
        /// Check if DNS filtering is currently active
        /// </summary>
        public async Task<bool> IsFilteringActiveAsync()
        {
            try
            {
                // Check if hosts file contains our entries
                if (File.Exists(_hostsFilePath))
                {
                    var content = await File.ReadAllTextAsync(_hostsFilePath);
                    return content.Contains("# PcFutureShield DNS Filter");
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private Dictionary<string, List<string>> InitializeCategoryDomains()
        {
            return new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
            {
                ["adult"] = new List<string>
                {
                    // Major adult sites
                    "pornhub.com", "xvideos.com", "xnxx.com", "redtube.com", "youporn.com",
                    "tube8.com", "spankbang.com", "xhamster.com", "beeg.com", "sex.com",
                    "porn.com", "xxx.com", "adult.com", "playboy.com", "penthouse.com",
                    "onlyfans.com", "chaturbate.com", "cam4.com", "myfreecams.com",

                    // Additional comprehensive adult content sites
                    "brazzers.com", "realitykings.com", "bangbros.com", "naughtyamerica.com",
                    "digitalplayground.com", "wickedpictures.com", "vivid.com", "hustler.com",
                    "adultfriendfinder.com", "ashley-madison.com", "seeking.com", "alt.com",
                    "fetlife.com", "kink.com", "pornhd.com", "tnaflix.com", "empflix.com",
                    "drtuber.com", "slutload.com", "xtube.com", "4tube.com", "fuq.com",
                    "txxx.com", "hdzog.com", "vjav.com", "javhd.com", "javmost.com",
                    "eporner.com", "sunporno.com", "analdin.com", "upornia.com", "nuvid.com",
                    "hotmovs.com", "ok.xxx", "porntrex.com", "sexvid.xxx", "fapdu.com"
                },
                ["gambling"] = new List<string>
                {
                    // Major gambling sites
                    "bet365.com", "888casino.com", "pokerstars.com", "williamhill.com",
                    "ladbrokes.com", "betfair.com", "bwin.com", "unibet.com", "betway.com",
                    "casino.com", "slots.com", "poker.com", "blackjack.com", "roulette.com",
                    "draftkings.com", "fanduel.com", "bovada.com", "ignition.com",

                    // Additional gambling and betting sites
                    "caesars.com", "mgmresorts.com", "harrahscasino.com", "borgataonline.com",
                    "partypoker.com", "fulltilt.com", "wsop.com", "ggpoker.com", "acr.eu",
                    "betonline.ag", "sportsbetting.ag", "mybookie.ag", "xbet.ag",
                    "stake.com", "roobet.com", "duelbits.com", "rollbit.com", "bc.game",
                    "cloudbet.com", "nitrogen.eu", "fortunejack.com", "bitcasino.io",
                    "1xbet.com", "22bet.com", "melbet.com", "parimatch.com", "mostbet.com"
                },
                ["social"] = new List<string>
                {
                    "tiktok.com", "snapchat.com", "discord.com", "reddit.com",
                    "instagram.com", "twitter.com", "x.com", "facebook.com", "messenger.com",
                    "whatsapp.com", "telegram.org", "signal.org", "kik.com", "viber.com"
                },
                ["violence"] = new List<string>
                {
                    "liveleak.com", "bestgore.com", "theync.com", "documenting-reality.com",
                    "goregrish.com", "crazyshit.com", "kaotic.com", "seegore.com",
                    "hoodsite.com", "watchpeopledie.tv", "makemycoffin.com"
                },
                ["drugs"] = new List<string>
                {
                    "silk-road.com", "alphabay.com", "dream-market.com", "wallstreet-market.com",
                    "empire-market.com", "white-house-market.com", "darkmarket.com"
                }
            };
        }

        private async Task SetFamilySafeDnsServersAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                        .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Loopback &&
                                   (ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet ||
                                    ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211));

                    var successCount = 0;
                    var totalCount = 0;

                    foreach (var ni in networkInterfaces)
                    {
                        totalCount++;
                        var adapterName = ni.Name;

                        try
                        {
                            // Set multiple family-safe DNS servers for redundancy
                            // Primary: OpenDNS FamilyShield (blocks adult content)
                            ExecuteNetshCommand($"interface ip set dns \"{adapterName}\" static **************");

                            // Secondary: CleanBrowsing Family Filter
                            ExecuteNetshCommand($"interface ip add dns \"{adapterName}\" *************** index=2");

                            // Tertiary: OpenDNS FamilyShield secondary
                            ExecuteNetshCommand($"interface ip add dns \"{adapterName}\" ************** index=3");

                            // Quaternary: Quad9 Filtered
                            ExecuteNetshCommand($"interface ip add dns \"{adapterName}\" ******** index=4");

                            successCount++;
                            System.Diagnostics.Debug.WriteLine($"Successfully set family-safe DNS for adapter: {adapterName}");
                        }
                        catch (Exception adapterEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"Failed to set DNS for adapter {adapterName}: {adapterEx.Message}");
                        }
                    }

                    if (successCount == 0 && totalCount > 0)
                    {
                        throw new InvalidOperationException($"Failed to set DNS on any of {totalCount} network adapters. Administrator privileges may be required.");
                    }

                    System.Diagnostics.Debug.WriteLine($"Successfully configured family-safe DNS on {successCount}/{totalCount} network adapters");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to set family-safe DNS: {ex.Message}");
                    throw; // Re-throw to let caller handle
                }
            });
        }

        private async Task RestoreAutomaticDnsAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                        .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

                    foreach (var ni in networkInterfaces)
                    {
                        var adapterName = ni.Name;
                        ExecuteNetshCommand($"interface ip set dns \"{adapterName}\" dhcp");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to restore automatic DNS: {ex.Message}");
                }
            });
        }

        private void ExecuteNetshCommand(string arguments)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "netsh",
                        Arguments = arguments,
                        UseShellExecute = true, // Required for runas
                        CreateNoWindow = true,
                        Verb = "runas", // Run as administrator
                        WindowStyle = ProcessWindowStyle.Hidden
                    }
                };

                process.Start();
                if (!process.WaitForExit(30000)) // 30 second timeout
                {
                    process.Kill();
                    throw new TimeoutException("Netsh command timed out");
                }

                if (process.ExitCode != 0)
                {
                    throw new InvalidOperationException($"Netsh command failed with exit code {process.ExitCode}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to execute netsh command '{arguments}': {ex.Message}");
                throw; // Re-throw to let caller handle
            }
        }

        private async Task UpdateHostsFileAsync()
        {
            try
            {
                // Check if we have write access to hosts file
                if (!HasHostsFileWriteAccess())
                {
                    throw new UnauthorizedAccessException("Administrator privileges required to modify hosts file");
                }

                var hostsContent = new List<string>();

                // Read existing hosts file
                if (File.Exists(_hostsFilePath))
                {
                    var existingLines = await File.ReadAllLinesAsync(_hostsFilePath);

                    // Keep non-PcFutureShield entries
                    foreach (var line in existingLines)
                    {
                        if (!line.Contains("# PcFutureShield DNS Filter"))
                        {
                            hostsContent.Add(line);
                        }
                    }
                }

                // Add PcFutureShield blocked domains
                if (_blockedDomains.Any())
                {
                    hostsContent.Add("");
                    hostsContent.Add("# PcFutureShield DNS Filter - Blocked Domains");
                    hostsContent.Add($"# Generated on {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                    foreach (var domain in _blockedDomains.OrderBy(d => d))
                    {
                        // Block main domain
                        hostsContent.Add($"127.0.0.1 {domain} # PcFutureShield DNS Filter");

                        // Block www subdomain
                        hostsContent.Add($"127.0.0.1 www.{domain} # PcFutureShield DNS Filter");

                        // Block common subdomains for comprehensive blocking
                        var commonSubdomains = new[] { "m", "mobile", "app", "api", "cdn", "static", "media", "images", "video", "live", "chat", "secure", "ssl", "www2", "www3" };
                        foreach (var subdomain in commonSubdomains)
                        {
                            hostsContent.Add($"127.0.0.1 {subdomain}.{domain} # PcFutureShield DNS Filter");
                        }

                        // Block IPv6 as well
                        hostsContent.Add($"::1 {domain} # PcFutureShield DNS Filter");
                        hostsContent.Add($"::1 www.{domain} # PcFutureShield DNS Filter");
                    }

                    hostsContent.Add("# End PcFutureShield DNS Filter");
                }

                // Create backup before modifying
                var backupPath = _hostsFilePath + ".pcfutureshield.backup";
                if (File.Exists(_hostsFilePath))
                {
                    File.Copy(_hostsFilePath, backupPath, true);
                }

                // Write back to hosts file
                await File.WriteAllLinesAsync(_hostsFilePath, hostsContent);

                System.Diagnostics.Debug.WriteLine($"Successfully updated hosts file with {_blockedDomains.Count} blocked domains");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update hosts file: {ex.Message}");
                throw; // Re-throw to let caller handle
            }
        }

        private bool HasHostsFileWriteAccess()
        {
            try
            {
                // Try to open the hosts file for writing
                using var fs = File.OpenWrite(_hostsFilePath);
                return true;
            }
            catch (UnauthorizedAccessException)
            {
                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private async Task CleanHostsFileAsync()
        {
            try
            {
                if (!File.Exists(_hostsFilePath)) return;

                var existingLines = await File.ReadAllLinesAsync(_hostsFilePath);
                var cleanedLines = existingLines.Where(line => !line.Contains("# PcFutureShield DNS Filter")).ToList();

                await File.WriteAllLinesAsync(_hostsFilePath, cleanedLines);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to clean hosts file: {ex.Message}");
            }
        }

        private async Task FlushDnsCacheAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "ipconfig",
                        Arguments = "/flushdns",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to flush DNS cache: {ex.Message}");
            }
        }
    }
}
