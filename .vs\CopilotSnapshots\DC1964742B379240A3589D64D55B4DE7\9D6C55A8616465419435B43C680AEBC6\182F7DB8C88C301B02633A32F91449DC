﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using PcFutureShield.Common.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class PcOptimizationViewModel : BaseViewModel
    {
        private readonly PcOptimizationService _optimizationService;

        // System Health Properties
        private SystemHealthReport _currentHealthReport = new SystemHealthReport();
        public SystemHealthReport CurrentHealthReport
        {
            get => _currentHealthReport;
            set => SetProperty(ref _currentHealthReport, value);
        }

        private bool _isAnalyzing;
        public bool IsAnalyzing
        {
            get => _isAnalyzing;
            set => SetProperty(ref _isAnalyzing, value);
        }

        // Performance metric properties used by view
        private double _cpuUsage;
        public double CpuUsage
        {
            get => _cpuUsage;
            set => SetProperty(ref _cpuUsage, value);
        }

        private double _memoryUsage;
        public double MemoryUsage
        {
            get => _memoryUsage;
            set => SetProperty(ref _memoryUsage, value);
        }

        private double _diskUsage;
        public double DiskUsage
        {
            get => _diskUsage;
            set => SetProperty(ref _diskUsage, value);
        }

        private int _performanceScore;
        public int PerformanceScore
        {
            get => _performanceScore;
            set => SetProperty(ref _performanceScore, value);
        }

        // Optimization status/progress
        private string _optimizationStatus = string.Empty;
        public string OptimizationStatus
        {
            get => _optimizationStatus;
            set => SetProperty(ref _optimizationStatus, value);
        }

        private double _optimizationProgress;
        public double OptimizationProgress
        {
            get => _optimizationProgress;
            set => SetProperty(ref _optimizationProgress, value);
        }

        // Optimization Properties
        private OptimizationResult _lastOptimizationResult;
        public OptimizationResult LastOptimizationResult
        {
            get => _lastOptimizationResult;
            set => SetProperty(ref _lastOptimizationResult, value);
        }

        private bool _isOptimizing;
        public bool IsOptimizing
        {
            get => _isOptimizing;
            set => SetProperty(ref _isOptimizing, value);
        }

        // Repair Properties
        private RepairResult _lastRepairResult;
        public RepairResult LastRepairResult
        {
            get => _lastRepairResult;
            set => SetProperty(ref _lastRepairResult, value);
        }

        private bool _isRepairing;
        public bool IsRepairing
        {
            get => _isRepairing;
            set => SetProperty(ref _isRepairing, value);
        }

        // Performance Boost Properties
        private PerformanceBoostResult _lastBoostResult;
        public PerformanceBoostResult LastBoostResult
        {
            get => _lastBoostResult;
            set => SetProperty(ref _lastBoostResult, value);
        }

        private bool _isBoosting;
        public bool IsBoosting
        {
            get => _isBoosting;
            set => SetProperty(ref _isBoosting, value);
        }

        // Uninstall Properties
        private ObservableCollection<string> _programsToUninstall;
        public ObservableCollection<string> ProgramsToUninstall
        {
            get => _programsToUninstall;
            set => SetProperty(ref _programsToUninstall, value);
        }

        private string _newProgramToAdd;
        public string NewProgramToAdd
        {
            get => _newProgramToAdd;
            set => SetProperty(ref _newProgramToAdd, value);
        }

        private UninstallResult _lastUninstallResult;
        public UninstallResult LastUninstallResult
        {
            get => _lastUninstallResult;
            set => SetProperty(ref _lastUninstallResult, value);
        }

        private bool _isUninstalling;
        public bool IsUninstalling
        {
            get => _isUninstalling;
            set => SetProperty(ref _isUninstalling, value);
        }

        // Commands
        public ICommand AnalyzeSystemCommand { get; }
        public ICommand PerformOptimizationCommand { get; }
        public ICommand PerformRepairCommand { get; }
        public ICommand BoostPerformanceCommand { get; }
        public ICommand AddProgramCommand { get; }
        public ICommand RemoveProgramCommand { get; }
        public ICommand PerformUninstallCommand { get; }

        // Added commands used by the view
        public ICommand CleanTempFilesCommand { get; }
        public ICommand DefragmentCommand { get; }
        public ICommand ClearCacheCommand { get; }
        public ICommand OptimizeStartupCommand { get; }
        public ICommand UpdateDriversCommand { get; }
        public ICommand FullOptimizationCommand { get; }
        public ICommand GenerateReportCommand { get; }

        public PcOptimizationViewModel(PcOptimizationService optimizationService)
        {
            _optimizationService = optimizationService ?? throw new ArgumentNullException(nameof(optimizationService));

            ProgramsToUninstall = new ObservableCollection<string>();

            // Initialize commands
            AnalyzeSystemCommand = new RelayCommand(async () => await AnalyzeSystemAsync());
            PerformOptimizationCommand = new RelayCommand(async () => await PerformOptimizationAsync());
            PerformRepairCommand = new RelayCommand(async () => await PerformRepairAsync());
            BoostPerformanceCommand = new RelayCommand(async () => await BoostPerformanceAsync());
            AddProgramCommand = new RelayCommand(AddProgram);
            RemoveProgramCommand = new RelayCommand<string>(RemoveProgram);
            PerformUninstallCommand = new RelayCommand(async () => await PerformUninstallAsync());

            // Initialize commands referenced by XAML
            CleanTempFilesCommand = new RelayCommand(async () => await PerformCleanTempFilesAsync(CancellationToken.None));
            DefragmentCommand = new RelayCommand(async () => await PerformDefragmentAsync(CancellationToken.None));
            ClearCacheCommand = new RelayCommand(async () => await PerformClearCacheAsync(CancellationToken.None));
            OptimizeStartupCommand = new RelayCommand(async () => await PerformOptimizeStartupAsync(CancellationToken.None));
            UpdateDriversCommand = new RelayCommand(async () => await PerformUpdateDriversAsync(CancellationToken.None));
            FullOptimizationCommand = new RelayCommand(async () => await PerformFullOptimizationAsync(CancellationToken.None));
            GenerateReportCommand = new RelayCommand(async () => await GenerateReportAsync());
        }

        private async Task AnalyzeSystemAsync()
        {
            if (IsAnalyzing) return;

            IsAnalyzing = true;
            try
            {
                CurrentHealthReport = await _optimizationService.GenerateSystemHealthReportAsync();

                // Update simple metrics if available
                if (CurrentHealthReport?.PerformanceMetrics != null)
                {
                    CpuUsage = CurrentHealthReport.PerformanceMetrics.CpuUsage;
                    MemoryUsage = CurrentHealthReport.PerformanceMetrics.MemoryUsagePercent;
                    DiskUsage = CurrentHealthReport.PerformanceMetrics.DiskUsagePercent;
                }

                PerformanceScore = (int)Math.Round(CurrentHealthReport.OverallHealthScore * 100);
            }
            catch (Exception ex)
            {
                // Log error to console as fallback; UI should surface via logger/notification
                Console.WriteLine($"Error analyzing system: {ex.Message}");
            }
            finally
            {
                IsAnalyzing = false;
            }
        }

        private async Task PerformOptimizationAsync()
        {
            if (IsOptimizing) return;

            IsOptimizing = true;
            OptimizationStatus = "Starting optimization...";
            OptimizationProgress = 0;

            try
            {
                var options = new OptimizationOptions
                {
                    CleanTempFiles = true,
                    RemoveBloatware = true,
                    OptimizeStartup = true,
                    DefragmentDrives = true,
                    OptimizeMemory = true,
                    CleanRegistry = true
                };

                LastOptimizationResult = await _optimization_service_PerformDeepOptimization(options);

                OptimizationStatus = "Optimization completed.";
                OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing optimization: {ex.Message}");
                OptimizationStatus = "Optimization failed.";
            }
            finally
            {
                IsOptimizing = false;
            }
        }

        // Adapter to call specific operations via PerformDeepOptimizationAsync with targeted options
        private async Task<OptimizationResult> _optimization_service_PerformDeepOptimization(OptimizationOptions options)
        {
            // Use the service API which performs requested operations
            return await _optimizationService.PerformDeepOptimizationAsync(options);
        }

        private async Task PerformRepairAsync()
        {
            if (IsRepairing) return;

            IsRepairing = true;
            OptimizationStatus = "Starting repair...";
            try
            {
                var options = new RepairOptions
                {
                    RepairSystemFiles = true,
                    FixWindowsCorruption = true,
                    RepairBootIssues = true,
                    FixDriverIssues = true,
                    RepairNetworkIssues = true
                };

                LastRepairResult = await _optimizationService.PerformSystemRepairAsync(options);
                OptimizationStatus = LastRepairResult.SuccessRate >= 0.0 ? "Repair completed." : "Repair completed with issues.";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing repair: {ex.Message}");
                OptimizationStatus = "Repair failed.";
            }
            finally
            {
                IsRepairing = false;
            }
        }

        private async Task BoostPerformanceAsync()
        {
            if (IsBoosting) return;

            IsBoosting = true;
            OptimizationStatus = "Boosting performance...";
            try
            {
                LastBoostResult = await _optimizationService.BoostPerformanceAsync();
                OptimizationStatus = "Boost complete.";
                OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error boosting performance: {ex.Message}");
                OptimizationStatus = "Boost failed.";
            }
            finally
            {
                IsBoosting = false;
            }
        }

        private void AddProgram()
        {
            if (!string.IsNullOrWhiteSpace(NewProgramToAdd) && !ProgramsToUninstall.Contains(NewProgramToAdd))
            {
                ProgramsToUninstall.Add(NewProgramToAdd);
                NewProgramToAdd = string.Empty;
            }
        }

        private void RemoveProgram(string program)
        {
            if (!string.IsNullOrEmpty(program))
            {
                ProgramsToUninstall.Remove(program);
            }
        }

        private async Task PerformUninstallAsync()
        {
            if (IsUninstalling || !ProgramsToUninstall.Any()) return;

            IsUninstalling = true;
            OptimizationStatus = "Uninstalling selected programs...";
            try
            {
                LastUninstallResult = await _optimizationService.PerformSmartUninstallAsync(ProgramsToUninstall.ToList());
                ProgramsToUninstall.Clear();
                OptimizationStatus = "Uninstall completed.";
                OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error performing uninstall: {ex.Message}");
                OptimizationStatus = "Uninstall failed.";
            }
            finally
            {
                IsUninstalling = false;
            }
        }

        // Implementations for XAML referenced commands
        private async Task PerformCleanTempFilesAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "Cleaning temporary files...";
            OptimizationProgress = 0;

            var options = new OptimizationOptions { CleanTempFiles = true };
            var result = await _optimization_service_PerformDeepOptimization(options);

            if (result.TempFileCleanup != null)
            {
                OptimizationStatus = $"Cleaned {result.TempFileCleanup.FilesRemoved} files ({result.TempFileCleanup.BytesCleaned} bytes).";
            }
            else
            {
                OptimizationStatus = "Temporary file cleanup completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformDefragmentAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "Defragmenting drives...";
            OptimizationProgress = 0;

            var options = new OptimizationOptions { DefragmentDrives = true };
            var result = await _optimization_service_PerformDeepOptimization(options);

            if (result.DiskDefragmentation != null)
            {
                OptimizationStatus = $"Defragmented drives: {string.Join(",", result.DiskDefragmentation.DefragmentedDrives)}.";
            }
            else
            {
                OptimizationStatus = "Defragmentation completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformClearCacheAsync(CancellationToken cancellationToken)
        {
            // Clear cache is part of temp file cleanup in service
            await PerformCleanTempFilesAsync(cancellationToken);
        }

        private async Task PerformOptimizeStartupAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "Optimizing startup programs (analysis)...";
            OptimizationProgress = 0;

            var options = new OptimizationOptions { OptimizeStartup = true };
            var result = await _optimization_service_PerformDeepOptimization(options);

            if (result.StartupOptimization != null)
            {
                OptimizationStatus = $"Startup entries analyzed: {result.StartupOptimization.ProgramsEnabled}.";
            }
            else
            {
                OptimizationStatus = "Startup optimization completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformUpdateDriversAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "Checking and updating drivers...";
            OptimizationProgress = 0;

            var repairOptions = new RepairOptions { FixDriverIssues = true };
            var repairResult = await _optimizationService.PerformSystemRepairAsync(repairOptions);

            if (repairResult.DriverRepair != null)
            {
                OptimizationStatus = "Driver scan completed.";
            }
            else
            {
                OptimizationStatus = "Driver update check completed.";
            }

            OptimizationProgress = 100;
        }

        private async Task PerformFullOptimizationAsync(CancellationToken cancellationToken)
        {
            OptimizationStatus = "Running full system optimization...";
            OptimizationProgress = 0;

            var options = new OptimizationOptions
            {
                CleanTempFiles = true,
                RemoveBloatware = true,
                OptimizeStartup = true,
                DefragmentDrives = true,
                OptimizeMemory = true,
                CleanRegistry = true
            };

            LastOptimizationResult = await _optimization_service_PerformDeepOptimization(options);

            OptimizationStatus = "Full optimization complete.";
            OptimizationProgress = 100;
        }

        private async Task GenerateReportAsync()
        {
            try
            {
                var report = new
                {
                    GeneratedAt = DateTime.UtcNow,
                    HealthReport = CurrentHealthReport,
                    LastOptimization = LastOptimizationResult,
                    LastRepair = LastRepairResult,
                    LastBoost = LastBoostResult,
                    LastUninstall = LastUninstallResult
                };

                var appData = Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData);
                var outDir = Path.Combine(appData, "PcFutureShield", "Reports");
                Directory.CreateDirectory(outDir);
                var fileName = Path.Combine(outDir, $"optimization_report_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json");
                var json = JsonSerializer.Serialize(report, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(fileName, json);

                OptimizationStatus = $"Report saved: {fileName}";
                OptimizationProgress = 100;
            }
            catch (Exception ex)
            {
                OptimizationStatus = $"Failed to generate report: {ex.Message}";
            }
        }
    }
}
