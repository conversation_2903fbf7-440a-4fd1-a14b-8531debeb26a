<UserControl x:Class="PcFutureShield.UI.Views.AIDashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid Background="{DynamicResource PrimaryColorBrush}">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center" Width="600">
            <TextBlock Text="AI Security Dashboard" FontSize="32" FontWeight="Bold" Margin="0,0,0,24" HorizontalAlignment="Center" Foreground="{DynamicResource PrimaryFontBrush}"/>

            <UniformGrid Columns="2" Rows="3" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,0,24">
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="AI Status" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource SecondaryFontBrush}"/>
                        <TextBlock Text="{Binding AIStatus}" FontSize="20" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="AI Model" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource SecondaryFontBrush}"/>
                        <TextBlock Text="{Binding AIModelStatus}" FontSize="20" FontWeight="Bold" Foreground="{DynamicResource SuccessBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="Threats Detected" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource SecondaryFontBrush}"/>
                        <TextBlock Text="{Binding ThreatsDetected}" FontSize="28" FontWeight="Bold" Foreground="{DynamicResource ErrorBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}">
                    <StackPanel>
                        <TextBlock Text="Risk Score" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource SecondaryFontBrush}"/>
                        <TextBlock Text="{Binding SystemRiskScore, StringFormat={}{0:P1}}" FontSize="28" FontWeight="Bold" Foreground="{DynamicResource WarningBrush}"/>
                    </StackPanel>
                </Border>
                <Border Style="{DynamicResource ChromeBackgroundStyle}" Margin="12" Padding="18" BorderBrush="{DynamicResource BorderBrush}" Grid.ColumnSpan="2">
                    <StackPanel>
                        <TextBlock Text="Last Analysis" FontWeight="SemiBold" FontSize="18" Foreground="{DynamicResource SecondaryFontBrush}"/>
                        <TextBlock Text="{Binding LastAnalysis}" FontSize="16" FontWeight="Bold" Foreground="{DynamicResource PrimaryFontBrush}"/>
                    </StackPanel>
                </Border>
            </UniformGrid>

            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,24,0,0">
                <Button Content="Run AI Analysis" Command="{Binding AnalyzeCommand}" Style="{DynamicResource GlassButtonStyle}" Width="180" Height="48" Margin="0,0,20,0"/>
                <Button Content="Quick AI Scan" Command="{Binding QuickScanCommand}" Style="{DynamicResource MirrorButtonStyle}" Width="180" Height="48"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>
