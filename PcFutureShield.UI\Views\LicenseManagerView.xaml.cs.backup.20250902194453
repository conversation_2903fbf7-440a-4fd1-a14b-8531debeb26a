using System.Windows.Controls;

namespace PcFutureShield.UI.Views
{
    public partial class LicenseManagerView : UserControl
    {
        public LicenseManagerView() { }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Auto(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}

		// Auto-generated event handler stub by AutoValidationTool
		private void Horizontal(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
