using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace PcFutureShield.UI.Services
{
    /// <summary>
    /// Production-grade service locator with thread-safe operations
    /// </summary>
    public static class ServiceLocator
    {
        private static IServiceProvider? _serviceProvider;
        private static readonly ConcurrentDictionary<Type, object> _singletonServices = new();
        private static readonly object _lock = new();
        private static bool _isInitialized = false;

        /// <summary>
        /// Initialize the service locator with a service provider
        /// </summary>
        /// <param name="serviceProvider">The service provider to use</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            lock (_lock)
            {
                _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
                _isInitialized = true;
            }
        }

        /// <summary>
        /// Register a singleton service instance
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <param name="service">Service instance</param>
        public static void Register<T>(T service) where T : class
        {
            if (service == null) throw new ArgumentNullException(nameof(service));
            _singletonServices[typeof(T)] = service;
        }

        /// <summary>
        /// Register a singleton service instance by type
        /// </summary>
        /// <param name="serviceType">Service type</param>
        /// <param name="service">Service instance</param>
        public static void Register(Type serviceType, object service)
        {
            if (serviceType == null) throw new ArgumentNullException(nameof(serviceType));
            if (service == null) throw new ArgumentNullException(nameof(service));
            _singletonServices[serviceType] = service;
        }

        /// <summary>
        /// Get a required service instance
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        /// <exception cref="InvalidOperationException">Thrown when service is not registered</exception>
        public static T Get<T>() where T : class
        {
            // First check singleton services
            if (_singletonServices.TryGetValue(typeof(T), out var singletonService))
            {
                return (T)singletonService;
            }

            // Then check service provider
            if (_serviceProvider != null)
            {
                var service = _serviceProvider.GetService(typeof(T));
                if (service is T typedService)
                {
                    return typedService;
                }
            }

            throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered. " +
                                              $"Make sure to register the service or initialize the ServiceLocator properly.");
        }

        /// <summary>
        /// Get a service instance or null if not registered
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance or null</returns>
        public static T? GetOrNull<T>() where T : class
        {
            try
            {
                return Get<T>();
            }
            catch (InvalidOperationException)
            {
                return null;
            }
        }

        /// <summary>
        /// Get a required service instance by type
        /// </summary>
        /// <param name="serviceType">Service type</param>
        /// <returns>Service instance</returns>
        public static object Get(Type serviceType)
        {
            if (serviceType == null) throw new ArgumentNullException(nameof(serviceType));

            // First check singleton services
            if (_singletonServices.TryGetValue(serviceType, out var singletonService))
            {
                return singletonService;
            }

            // Then check service provider
            if (_serviceProvider != null)
            {
                var service = _serviceProvider.GetService(serviceType);
                if (service != null)
                {
                    return service;
                }
            }

            throw new InvalidOperationException($"Service of type {serviceType.Name} is not registered.");
        }

        /// <summary>
        /// Check if a service is registered
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>True if registered, false otherwise</returns>
        public static bool IsRegistered<T>() where T : class
        {
            return _singletonServices.ContainsKey(typeof(T)) ||
                   (_serviceProvider?.GetService(typeof(T)) != null);
        }

        /// <summary>
        /// Check if a service is registered by type
        /// </summary>
        /// <param name="serviceType">Service type</param>
        /// <returns>True if registered, false otherwise</returns>
        public static bool IsRegistered(Type serviceType)
        {
            return _singletonServices.ContainsKey(serviceType) ||
                   (_serviceProvider?.GetService(serviceType) != null);
        }

        /// <summary>
        /// Get all registered service types
        /// </summary>
        /// <returns>Collection of registered service types</returns>
        public static IEnumerable<Type> GetRegisteredTypes()
        {
            return _singletonServices.Keys;
        }

        /// <summary>
        /// Clear all registered services
        /// </summary>
        public static void Clear()
        {
            lock (_lock)
            {
                _singletonServices.Clear();
                if (_serviceProvider is IDisposable disposableProvider)
                {
                    disposableProvider.Dispose();
                }
                _serviceProvider = null;
                _isInitialized = false;
            }
        }

        /// <summary>
        /// Check if the service locator is initialized
        /// </summary>
        public static bool IsInitialized => _isInitialized;
    }
}
