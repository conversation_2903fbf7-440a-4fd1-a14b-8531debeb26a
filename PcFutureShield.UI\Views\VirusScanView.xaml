<UserControl x:Class="PcFutureShield.UI.Views.VirusScanView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             d:DesignHeight="400" d:DesignWidth="600">
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    <Grid Background="{DynamicResource BackgroundBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <TextBlock Text="Virus Scan" FontSize="24" FontWeight="Bold" Margin="20,20,20,10" Foreground="{DynamicResource PrimaryFontBrush}" Grid.Row="0"/>
        <TextBlock Text="{Binding Status}" FontSize="14" Margin="20,0,20,10" Foreground="{DynamicResource SecondaryFontBrush}" Grid.Row="1"/>
        <StackPanel Orientation="Horizontal" Margin="20,0,20,10" Grid.Row="2">
            <Button Content="Start Scan" Command="{Binding StartScanCommand}" Margin="0,0,10,0" Style="{DynamicResource ChromeButtonStyle}" BorderBrush="#FFDEC207">
                <Button.Background>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="Black"/>
                        <GradientStop Color="#FF08416D" Offset="1"/>
                    </LinearGradientBrush>
                </Button.Background>
            </Button>
            <Button Content="Cancel" Command="{Binding CancelScanCommand}" Style="{DynamicResource ChromeButtonStyle}" BorderBrush="#FFDEC207">
                <Button.Background>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="Black"/>
                        <GradientStop Color="#FF11539C" Offset="1"/>
                    </LinearGradientBrush>
                </Button.Background>
            </Button>
        </StackPanel>
        <ProgressBar IsIndeterminate="True" Height="20" Margin="20,0,20,10" Visibility="{Binding IsScanning, Converter={StaticResource BooleanToVisibilityConverter}}" Grid.Row="3" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryColorBrush}"/>
        <StackPanel Margin="20,0,20,20" Grid.Row="4">
            <TextBlock Text="Files Scanned: {Binding FilesScanned}" Margin="0,10,0,0" Foreground="{DynamicResource PrimaryFontBrush}"/>
            <TextBlock Text="Duration: {Binding Duration}" Margin="0,0,0,10" Foreground="{DynamicResource SecondaryFontBrush}"/>
            <DataGrid ItemsSource="{Binding Threats}" AutoGenerateColumns="False" Height="200" Background="{DynamicResource CardBackgroundBrush}" Foreground="{DynamicResource PrimaryFontBrush}" RowBackground="{DynamicResource SidebarBackgroundBrush}" AlternatingRowBackground="{DynamicResource PrimaryColorBrush}" BorderBrush="{DynamicResource BorderBrush}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="File Path" Binding="{Binding FilePath}" Width="*"/>
                    <DataGridTextColumn Header="Threat Name" Binding="{Binding ThreatName}" Width="200"/>
                    <DataGridTextColumn Header="Detected At" Binding="{Binding DetectedAt}" Width="150"/>
                    <DataGridCheckBoxColumn Header="Quarantined" Binding="{Binding IsQuarantined}" Width="100"/>
                </DataGrid.Columns>
            </DataGrid>
        </StackPanel>
    </Grid>
</UserControl>
