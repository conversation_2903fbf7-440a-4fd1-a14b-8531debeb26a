<UserControl x:Class="PcFutureShield.UI.Views.RealtimeProtectionView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d">
    <Grid Background="{DynamicResource PrimaryColorBrush}">
    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <StackPanel.Background>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="Black"/>
                    <GradientStop Color="#FF0860C1" Offset="1"/>
                </LinearGradientBrush>
            </StackPanel.Background>
            <TextBlock Text="Real-Time Protection" FontSize="32" FontWeight="Bold" Margin="0,0,0,24"/>
            <TextBlock Text="Live monitoring of file system, processes, and network activity." FontSize="18" Margin="0,0,0,12"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <ToggleButton Content="Enable Protection" IsChecked="{Binding IsProtectionEnabled, Mode=TwoWay}" Width="180" Height="48" Style="{DynamicResource GlassToggleButtonStyle}" Margin="0,0,18,0"/>
                <Button Content="View Events" Command="{Binding ViewEventsCommand}" Width="160" Height="48" Style="{DynamicResource GlassButtonStyle}"/>
            </StackPanel>
            <ListView ItemsSource="{Binding Events}" Margin="0,24,0,0" Height="220" Width="800" Foreground="#FFF6F8FD">
                <ListView.Background>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="Black"/>
                        <GradientStop Color="#FF0B567D" Offset="1"/>
                    </LinearGradientBrush>
                </ListView.Background>
                <ListView.View>
                    <GridView>
                        <GridViewColumn Header="Time" Width="140" DisplayMemberBinding="{Binding Time}"/>
                        <GridViewColumn Header="Event" Width="320" DisplayMemberBinding="{Binding Event}"/>
                        <GridViewColumn Header="File" Width="220" DisplayMemberBinding="{Binding File}"/>
                        <GridViewColumn Header="Severity" Width="90" DisplayMemberBinding="{Binding Severity}"/>
                    </GridView>
                </ListView.View>
            </ListView>
        </StackPanel>
    </Grid>
</UserControl>
