using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;

namespace PcFutureShield.Engine.Scanning
{
    public class ScannerService : IScannerService, IDisposable, IHostedService
    {
        private const int MaxConcurrentScans = 8;
        private const int MaxFileSizeBytes = 200 * 1024 * 1024;
        private readonly ILogger<ScannerService> _logger;
        private readonly ISignatureDatabase _signatureDb;
        private readonly IServiceProvider _serviceProvider;
        private bool _disposed;
        private readonly CancellationTokenSource _stoppingCts = new();

        public ScannerService(
            ILogger<ScannerService> logger,
            ISignatureDatabase signatureDb,
            IServiceProvider serviceProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _signatureDb = signatureDb ?? throw new ArgumentNullException(nameof(signatureDb));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        // IHostedService implementation is provided at the end of the file

        public async Task<ScanResult> ScanFileAsync(string filePath, CancellationToken ct = default)
        {
            var result = new ScanResult();
            result.StartTime = DateTime.UtcNow;
            FileStream? fileStream = null;
            try
            {
                // Basic file validation
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("File path cannot be empty", nameof(filePath));

                var fileInfo = new FileInfo(filePath);
                if (!fileInfo.Exists)
                {
                    _logger.LogWarning("File not found: {FilePath}", filePath);
                    result.IsClean = false;
                    result.FilesScanned = 1;
                    return result;
                }

                // Check file size limits
                if (fileInfo.Length > MaxFileSizeBytes)
                {
                    _logger.LogWarning("File too large for scanning: {FilePath} ({FileSize} bytes)",
                        filePath, fileInfo.Length);
                    result.IsClean = false;
                    result.FilesScanned = 1;
                    result.Threats = new List<ThreatDetection>
                    {
                        new ThreatDetection
                        {
                            FilePath = filePath,
                            ThreatName = "FileTooLarge",
                            ThreatType = "FileSizeLimitExceeded",
                            Severity = ThreatSeverity.Info
                        }
                    };
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                // Check file signature
                fileStream = File.OpenRead(filePath);
                using (fileStream)
                {
                    using var sha256 = SHA256.Create();
                    var hash = await sha256.ComputeHashAsync(fileStream, ct);
                    var hashString = BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();

                    // Check against signature database
                    var threat = await _signatureDb.LookupSignatureAsync(hashString, ct);
                    if (threat != null)
                    {
                        _logger.LogWarning("Threat detected: {ThreatName} in {FilePath}",
                            threat.Name, filePath);
                        result.IsClean = false;
                        result.FilesScanned = 1;
                        result.Threats = new List<ThreatDetection>
                        {
                            new()
                            {
                                FilePath = filePath,
                                ThreatName = threat.Name,
                                ThreatType = threat.Type,
                                Severity = threat.Severity,
                                Details = new Dictionary<string, string>
                                {
                                    { "Hash", hashString },
                                    { "Size", fileInfo.Length.ToString() },
                                    { "LastModified", fileInfo.LastWriteTimeUtc.ToString("O") }
                                }
                            }
                        };
                        return result;
                    }
                }

                // Perform heuristic analysis
                var heuristicResult = await PerformHeuristicAnalysisAsync(filePath, ct);
                if (heuristicResult.IsSuspicious)
                {
                    _logger.LogWarning("Suspicious activity detected in {FilePath}", filePath);

                    result.IsClean = false;
                    result.FilesScanned = 1;
                    result.Threats = new List<ThreatDetection>
                    {
                        new()
                        {
                            FilePath = filePath,
                            ThreatName = heuristicResult.ThreatName,
                            ThreatType = "Heuristic",
                            Severity = heuristicResult.Severity,
                            Details = heuristicResult.Details
                        }
                    };
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                _logger.LogDebug("File scanned clean: {FilePath}", filePath);
                result.IsClean = true;
                result.FilesScanned = 1;
                return result;
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogError(ex, "Access denied to file: {FilePath}", filePath);
                result.IsClean = false;
                result.FilesScanned = 1;
                result.Threats = new List<ThreatDetection>
                {
                    new()
                    {
                        FilePath = filePath,
                        ThreatName = "AccessDenied",
                        ThreatType = "PermissionError",
                        Severity = ThreatSeverity.Info,
                        Details = new() { { "Error", ex.Message } }
                    }
                };
                return result;
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "I/O error scanning file: {FilePath}", filePath);
                result.IsClean = false;
                result.FilesScanned = 1;
                result.Threats = new List<ThreatDetection>
                {
                    new()
                    {
                        FilePath = filePath,
                        ThreatName = "IOError",
                        ThreatType = "ScanError",
                        Severity = ThreatSeverity.Info,
                        Details = new() { { "Error", ex.Message } }
                    }
                };
                return result;
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Unexpected error scanning file: {FilePath}", filePath);
                result.IsClean = false;
                result.FilesScanned = 1;
                result.Threats = new List<ThreatDetection>
                {
                    new()
                    {
                        FilePath = filePath,
                        ThreatName = "ScanError",
                        ThreatType = "UnexpectedError",
                        Severity = ThreatSeverity.Info,
                        Details = new() { { "Error", ex.Message } }
                    }
                };
                return result;
            }
            finally
            {
                if (fileStream != null)
                {
                    await fileStream.DisposeAsync();
                }
                result.EndTime = DateTime.UtcNow;
            }
        }

        public async Task<ScanResult> ScanDirectoryAsync(string directoryPath, bool recursive = true, CancellationToken ct = default)
        {
            var result = new ScanResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("Directory path cannot be empty", nameof(directoryPath));

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"Directory not found: {directoryPath}");

                var files = Directory.EnumerateFiles(directoryPath, "*",
                    recursive ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly)
                    .ToList();

                var options = new ParallelOptions
                {
                    MaxDegreeOfParallelism = MaxConcurrentScans,
                    CancellationToken = ct
                };

                var allThreats = new ConcurrentBag<ThreatDetection>();
                int totalFiles = files.Count;
                int processedCount = 0;
                int errorCount = 0;
                var lastProgressUpdate = DateTime.UtcNow;

                try
                {
                    await Parallel.ForEachAsync(files, options, async (file, token) =>
                    {
                        try
                        {
                            var fileResult = await ScanFileAsync(file, token);
                            var currentProcessed = Interlocked.Increment(ref processedCount);

                            if (!fileResult.IsClean && fileResult.Threats != null)
                            {
                                foreach (var threat in fileResult.Threats)
                                {
                                    allThreats.Add(threat);
                                }
                            }

                            // Report progress periodically (at most once per second)
                            var now = DateTime.UtcNow;
                            if ((now - lastProgressUpdate).TotalSeconds >= 1.0)
                            {
                                var progress = (int)((double)currentProcessed / totalFiles * 100);
                                _logger.LogInformation("Scan progress: {Progress}% ({Processed}/{Total} files, {Threats} threats)",
                                    progress, currentProcessed, totalFiles, allThreats.Count);
                                lastProgressUpdate = now;
                            }
                        }
                        catch (Exception ex) when (ex is not OperationCanceledException)
                        {
                            _logger.LogError(ex, "Error scanning file: {FilePath}", file);
                            Interlocked.Increment(ref errorCount);
                        }
                    });
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Directory scan was canceled");
                    throw;
                }

                var scanDuration = DateTime.UtcNow - result.StartTime;
                _logger.LogInformation("Directory scan completed in {Duration}. Scanned {Scanned} files, found {Threats} threats, {Errors} errors",
                    scanDuration, processedCount, allThreats.Count, errorCount);

                result.IsClean = !allThreats.Any();
                result.FilesScanned = processedCount;
                result.Threats = allThreats.ToList();
                return result;
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error scanning directory: {DirectoryPath}", directoryPath);
                throw;
            }
        }

        public async Task<ScanResult> ScanProcessAsync(int processId, CancellationToken ct = default)
        {
            var result = new ScanResult
            {
                StartTime = DateTime.UtcNow,
                FilesScanned = 1 // Count as one process scanned
            };

            try
            {
                _logger.LogInformation("Starting process scan for PID: {ProcessId}", processId);

                // Get the process by ID
                Process process;
                try
                {
                    process = Process.GetProcessById(processId);
                }
                catch (ArgumentException ex)
                {
                    _logger.LogWarning("Process with ID {ProcessId} not found: {Message}", processId, ex.Message);
                    result.IsClean = false;
                    result.Threats = new List<ThreatDetection>
                    {
                        CreateThreat("ProcessNotFound", "ProcessError", ThreatSeverity.Medium,
                            $"Process with ID {processId} not found: {ex.Message}")
                    };
                    return result;
                }

                var threats = new List<ThreatDetection>();

                // Check process modules (DLLs)
                try
                {
                    var moduleThreats = await ScanProcessModulesAsync(process, ct);
                    threats.AddRange(moduleThreats);
                }
                catch (Exception ex) when (ex is not OperationCanceledException)
                {
                    _logger.LogError(ex, "Error scanning process modules for PID {ProcessId}", processId);
                    threats.Add(CreateThreat("ModuleScanError", "ProcessError", ThreatSeverity.Medium, ex.Message));
                }

                // Check process handles for suspicious activity
                if (!ct.IsCancellationRequested)
                {
                    try
                    {
                        var handleThreats = await ScanProcessHandlesAsync(process, ct);
                        threats.AddRange(handleThreats);
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException)
                    {
                        _logger.LogError(ex, "Error scanning process handles for PID {ProcessId}", processId);
                        threats.Add(CreateThreat("HandleScanError", "ProcessError", ThreatSeverity.Medium, ex.Message));
                    }
                }

                _logger.LogInformation("Completed process scan for PID {ProcessId}. Found {ThreatCount} threats.",
                    processId, threats.Count);

                result.IsClean = threats.Count == 0;
                result.Threats = threats;
                return result;
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Unexpected error scanning process {ProcessId}", processId);
                result.IsClean = false;
                result.Threats = new List<ThreatDetection>
                {
                    CreateThreat("ScanError", "UnexpectedError", ThreatSeverity.High,
                        $"Unexpected error scanning process {processId}: {ex.Message}")
                };
                return result;
            }
            finally
            {
                result.EndTime = DateTime.UtcNow;
            }
        }

        private async Task<List<ThreatDetection>> ScanProcessModulesAsync(Process process, CancellationToken ct)
        {
            var threats = new List<ThreatDetection>();

            try
            {
                foreach (ProcessModule? module in process.Modules)
                {
                    if (module == null) continue;
                    ct.ThrowIfCancellationRequested();

                    try
                    {
                        var modulePath = module.FileName;
                        if (string.IsNullOrEmpty(modulePath)) continue;

                        // Check if the module is signed and verify its signature
                        var signatureCheck = await VerifyModuleSignatureAsync(modulePath, ct);
                        if (!signatureCheck.IsValid)
                        {
                            threats.Add(CreateThreat(
                                "InvalidModuleSignature",
                                "CodeIntegrity",
                                ThreatSeverity.High,
                                $"Invalid signature for {module.ModuleName}",
                                modulePath,
                                new()
                                {
                                    { "ModuleName", module.ModuleName ?? "Unknown" },
                                    { "Reason", signatureCheck.FailureReason ?? "Unknown" }
                                }));
                            continue;
                        }

                        // Check module against known malicious hashes
                        var fileResult = await ScanFileAsync(modulePath, ct);
                        if (!fileResult.IsClean)
                        {
                            threats.AddRange(fileResult.Threats);
                        }
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException)
                    {
                        _logger.LogWarning(ex, "Error scanning module {ModuleName} in process {ProcessId}",
                            module.ModuleName, process.Id);
                    }
                }
            }
            catch (System.ComponentModel.Win32Exception ex)
            {
                _logger.LogWarning("Access denied when scanning process modules for PID {ProcessId}: {Message}",
                    process.Id, ex.Message);
                // Continue with other checks
            }

            return threats;
        }

        private async Task<(bool IsValid, string? FailureReason)> VerifyModuleSignatureAsync(string filePath, CancellationToken ct)
        {
            try
            {
                if (!File.Exists(filePath))
                    return (false, "File not found");

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length < 100) // Suspiciously small for a valid module
                    return (false, "Suspicious file size");

                // Perform certificate verification synchronously after yielding to keep async contract
                try
                {
                    await Task.Yield();
                    try
                    {
                        using var cert = new X509Certificate2(filePath);
                        var isValid = cert.Verify();
                        return (isValid, isValid ? null : "Certificate verification failed");
                    }
                    catch (Exception ex)
                    {
                        return (false, $"Signature verification error: {ex.Message}");
                    }
                }
                catch (OperationCanceledException)
                {
                    throw;
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogWarning(ex, "Error verifying module signature: {FilePath}", filePath);
                return (false, $"Verification error: {ex.Message}");
            }
        }

        private async Task<List<ThreatDetection>> ScanProcessMemoryAsync(Process process, CancellationToken ct)
        {
            var threats = new List<ThreatDetection>();

            try
            {
                _logger.LogDebug("Scanning memory of process {ProcessName} (PID: {ProcessId})",
                    process.ProcessName, process.Id);

                // Check for suspicious memory regions
                var suspiciousRegions = await FindSuspiciousMemoryRegionsAsync(process, ct);

                foreach (var region in suspiciousRegions)
                {
                    threats.Add(CreateThreat(
                        "SuspiciousMemoryRegion",
                        "MemoryThreat",
                        region.Severity,
                        $"Suspicious memory region at 0x{region.BaseAddress:X}",
                        process.ProcessName,
                        new()
                        {
                            { "BaseAddress", $"0x{region.BaseAddress:X}" },
                            { "Size", $"{region.Size} bytes" },
                            { "Protection", region.Protection },
                            { "Reason", region.Reason }
                        }));
                }

                // Check for known malicious patterns in memory
                var patternThreats = await ScanForMemoryPatternsAsync(process, ct);
                threats.AddRange(patternThreats);
            }
            catch (System.ComponentModel.Win32Exception ex)
            {
                _logger.LogWarning("Access denied when scanning memory of process {ProcessId}: {Message}",
                    process.Id, ex.Message);
                // Continue with other checks
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error scanning memory of process {ProcessId}", process.Id);
            }

            return threats;
        }

        private static class NativeMethods
        {
            [Flags]
            public enum ProcessAccessFlags : uint
            {
                All = 0x001F0FFF,
                Terminate = 0x00000001,
                CreateThread = 0x00000002,
                VMOperation = 0x00000008,
                VMRead = 0x00000010,
                VMWrite = 0x00000020,
                DupHandle = 0x00000040,
                SetInformation = 0x00000200,
                QueryInformation = 0x00000400,
                Synchronize = 0x00100000,
                PROCESS_DUP_HANDLE = 0x00000040,
                PROCESS_QUERY_INFORMATION = 0x00000400
            }

            [Flags]
            public enum PageProtection : uint
            {
                NOACCESS = 0x01,
                READONLY = 0x02,
                READWRITE = 0x04,
                WRITECOPY = 0x08,
                EXECUTE = 0x10,
                EXECUTE_READ = 0x20,
                EXECUTE_READWRITE = 0x40,
                EXECUTE_WRITECOPY = 0x80,
                GUARD = 0x100,
                NOCACHE = 0x200,
                WRITECOMBINE = 0x400,
                PAGE_GUARD = 0x100,
                PAGE_NOCACHE = 0x200,
                PAGE_WRITECOMBINE = 0x400,
                PAGE_NOACCESS = 0x01,
                PAGE_READONLY = 0x02,
                PAGE_READWRITE = 0x04,
                PAGE_WRITECOPY = 0x08,
                PAGE_EXECUTE = 0x10,
                PAGE_EXECUTE_READ = 0x20,
                PAGE_EXECUTE_READWRITE = 0x40,
                PAGE_EXECUTE_WRITECOPY = 0x80
            }

            public enum MemoryState : uint
            {
                MEM_COMMIT = 0x1000,
                MEM_FREE = 0x10000,
                MEM_RESERVE = 0x2000
            }

            [StructLayout(LayoutKind.Sequential)]
            public struct MEMORY_BASIC_INFORMATION64
            {
                public ulong BaseAddress;
                public ulong AllocationBase;
                public int AllocationProtect;
                public int __alignment1;
                public ulong RegionSize;
                public uint State;
                public uint Protect;
                public uint Type;
                public int __alignment2;
            }

            public const uint PAGE_EXECUTE_READWRITE = 0x40;
            public const uint MEM_COMMIT = 0x1000;

            [DllImport("kernel32.dll")]
            public static extern int VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress,
                out MEMORY_BASIC_INFORMATION64 lpBuffer, uint dwLength);

            [DllImport("kernel32.dll")]
            [return: MarshalAs(UnmanagedType.Bool)]
            public static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
                [Out] byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

            [DllImport("kernel32.dll", SetLastError = true)]
            public static extern IntPtr OpenProcess(ProcessAccessFlags processAccess,
                bool bInheritHandle, int processId);

            [DllImport("kernel32.dll", SetLastError = true)]
            [return: MarshalAs(UnmanagedType.Bool)]
            public static extern bool CloseHandle(IntPtr hObject);
        }

        private async Task<List<MemoryRegionInfo>> FindSuspiciousMemoryRegionsAsync(Process process, CancellationToken ct)
        {
            var suspiciousRegions = new List<MemoryRegionInfo>();
            IntPtr processHandle = IntPtr.Zero;

            try
            {
                processHandle = NativeMethods.OpenProcess(
                    NativeMethods.ProcessAccessFlags.VMRead |
                    NativeMethods.ProcessAccessFlags.QueryInformation,
                    false,
                    process.Id);

                if (processHandle == IntPtr.Zero)
                {
                    _logger.LogWarning("Failed to open process {ProcessId} (Error: {ErrorCode})",
                        process.Id, Marshal.GetLastWin32Error());
                    return suspiciousRegions;
                }

                long address = 0;
                NativeMethods.MEMORY_BASIC_INFORMATION64 mbi;
                int mbiSize = Marshal.SizeOf<NativeMethods.MEMORY_BASIC_INFORMATION64>();

                while (NativeMethods.VirtualQueryEx(processHandle, (IntPtr)address, out mbi, (uint)mbiSize) != 0)
                {
                    if (ct.IsCancellationRequested)
                        break;

                    // Check for committed, executable memory regions
                    if ((mbi.State == (uint)NativeMethods.MemoryState.MEM_COMMIT) &&
                        (((uint)mbi.Protect & (uint)NativeMethods.PageProtection.EXECUTE) != 0 ||
                         ((uint)mbi.Protect & (uint)NativeMethods.PageProtection.EXECUTE_READ) != 0 ||
                         ((uint)mbi.Protect & (uint)NativeMethods.PageProtection.EXECUTE_READWRITE) != 0 ||
                         ((uint)mbi.Protect & (uint)NativeMethods.PageProtection.EXECUTE_WRITECOPY) != 0) &&
                        ((uint)mbi.Protect & (uint)NativeMethods.PageProtection.PAGE_GUARD) == 0 &&
                        ((uint)mbi.Protect & (uint)NativeMethods.PageProtection.PAGE_NOACCESS) == 0)
                    {
                        // Read memory to analyze
                        var buffer = new byte[Math.Min(4096, (int)mbi.RegionSize)];
                        if (NativeMethods.ReadProcessMemory(processHandle, (IntPtr)mbi.BaseAddress,
                            buffer, buffer.Length, out var bytesRead) && bytesRead > 0)
                        {
                            if (ContainsShellcodePatterns(buffer))
                            {
                                var protection = GetProtectionString((NativeMethods.PageProtection)mbi.Protect);
                                suspiciousRegions.Add(new MemoryRegionInfo
                                {
                                    BaseAddress = (long)mbi.BaseAddress,
                                    Size = (long)mbi.RegionSize,
                                    Protection = protection,
                                    Severity = ThreatSeverity.High,
                                    Reason = $"Suspicious executable memory region with protection {protection}"
                                });
                            }
                        }
                    }

                    if (mbi.RegionSize == 0)
                        break;

                    address = (long)mbi.BaseAddress + (long)mbi.RegionSize;

                    // Allow other tasks to run periodically
                    if (suspiciousRegions.Count % 10 == 0)
                        await Task.Yield();
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error scanning memory regions in process {ProcessId}", process.Id);
            }
            finally
            {
                if (processHandle != IntPtr.Zero)
                    NativeMethods.CloseHandle(processHandle);
            }

            return suspiciousRegions;
        }

        private bool ContainsShellcodePatterns(byte[] buffer)
        {
            if (buffer == null || buffer.Length < 4)
                return false;

            // Common shellcode patterns (simplified for example)
            var patterns = new (byte[] Pattern, int WildcardByte)[]
            {
                (new byte[] { 0xE8, 0x00, 0x00, 0x00, 0x00 }, 1), // CALL $+5 (with wildcard for address)
                (new byte[] { 0x68, 0x74, 0x74, 0x70, 0x3A }, -1), // 'http:' (no wildcards)
                (new byte[] { 0xFC, 0xE8, 0x82, 0x00 }, -1),      // Common shellcode prologue
                (new byte[] { 0x89, 0xE5, 0x83, 0xEC }, -1)       // Function prologue
            };

            // Check for NOP sleds (0x90) or INT3 (0xCC)
            int consecutiveNops = 0;
            foreach (var b in buffer)
            {
                if (b == 0x90 || b == 0xCC) // NOP or INT3
                {
                    if (++consecutiveNops > 10) // More than 10 consecutive NOPs is suspicious
                        return true;
                }
                else
                {
                    consecutiveNops = 0;
                }
            }

            // Check for known patterns
            foreach (var (pattern, wildcardByte) in patterns)
            {
                for (int i = 0; i <= buffer.Length - pattern.Length; i++)
                {
                    bool match = true;
                    for (int j = 0; j < pattern.Length; j++)
                    {
                        // If this is a wildcard byte position, skip the check
                        if (j == wildcardByte && wildcardByte != -1)
                            continue;

                        if (i + j >= buffer.Length || buffer[i + j] != pattern[j])
                        {
                            match = false;
                            break;
                        }
                    }
                    if (match)
                        return true;
                }
            }

            return false;
        }

        private async Task<List<ThreatDetection>> ScanForMemoryPatternsAsync(Process process, CancellationToken ct)
        {
            var threats = new List<ThreatDetection>();
            IntPtr processHandle = IntPtr.Zero;

            try
            {
                processHandle = NativeMethods.OpenProcess(
                    NativeMethods.ProcessAccessFlags.VMRead |
                    NativeMethods.ProcessAccessFlags.QueryInformation,
                    false,
                    process.Id);

                if (processHandle == IntPtr.Zero)
                {
                    _logger.LogWarning("Failed to open process {ProcessId} for memory scanning (Error: {ErrorCode})",
                        process.Id, Marshal.GetLastWin32Error());
                    return threats;
                }

                // Define patterns to scan for
                var patterns = new[]
                {
                    new { Name = "Mimikatz_Pattern", Pattern = new byte[] { 0x48, 0x8B, 0x01, 0xFF, 0x50 }, Severity = ThreatSeverity.Critical },
                    new { Name = "CobaltStrike_Beacon", Pattern = new byte[] { 0xE8, 0x00, 0x00, 0x00, 0x00, 0x41, 0x59 }, Severity = ThreatSeverity.Critical },
                    new { Name = "Meterpreter_Stager", Pattern = new byte[] { 0xFC, 0xE8, 0x82, 0x00, 0x00, 0x00 }, Severity = ThreatSeverity.High },
                    new { Name = "Ransomware_Key", Pattern = new byte[] { 0x52, 0x53, 0x41, 0x31, 0x32 }, Severity = ThreatSeverity.Critical },
                    new { Name = "CredentialDumper", Pattern = new byte[] { 0x48, 0x8B, 0x05, 0x00, 0x00, 0x00, 0x00, 0x48, 0x85, 0xC0, 0x74, 0x0C }, Severity = ThreatSeverity.High }
                };

                long minAddress = 0;
                long maxAddress = Environment.Is64BitProcess ? (1L << 47) - 1 : (1L << 32) - 1;
                const int bufferSize = 4096;
                var buffer = new byte[bufferSize];
                var memInfo = new NativeMethods.MEMORY_BASIC_INFORMATION64();
                int scannedRegions = 0;

                while (minAddress < maxAddress)
                {
                    if (ct.IsCancellationRequested)
                        break;

                    // Query memory region information
                    int result = NativeMethods.VirtualQueryEx(processHandle, (IntPtr)minAddress, out memInfo,
                        (uint)Marshal.SizeOf<NativeMethods.MEMORY_BASIC_INFORMATION64>());

                    if (result == 0)
                    {
                        int error = Marshal.GetLastWin32Error();
                        if (error != 0x1F) // ERROR_PARTIAL_COPY
                        {
                            _logger.LogDebug("VirtualQueryEx failed at address {Address} with error {Error}",
                                (IntPtr)minAddress, error);
                        }
                        minAddress += bufferSize;
                        continue;
                    }

                    // Only scan executable and readable memory
                    if ((((uint)memInfo.Protect & ((uint)NativeMethods.PageProtection.EXECUTE |
                                         (uint)NativeMethods.PageProtection.EXECUTE_READ |
                                         (uint)NativeMethods.PageProtection.EXECUTE_READWRITE |
                                         (uint)NativeMethods.PageProtection.EXECUTE_WRITECOPY)) == 0) ||
                        ((uint)memInfo.State != (uint)NativeMethods.MemoryState.MEM_COMMIT) ||
                        (((uint)memInfo.Protect & (uint)NativeMethods.PageProtection.PAGE_GUARD) != 0) ||
                        (((uint)memInfo.Protect & (uint)NativeMethods.PageProtection.PAGE_NOACCESS) != 0))
                    {
                        minAddress = (long)memInfo.BaseAddress + (long)memInfo.RegionSize;
                        continue;
                    }

                    // Scan the memory region in chunks
                    long regionEnd = (long)memInfo.BaseAddress + (long)memInfo.RegionSize;
                    long currentAddress = (long)memInfo.BaseAddress;

                    while (currentAddress < regionEnd)
                    {
                        if (ct.IsCancellationRequested)
                            break;

                        int bytesToRead = (int)Math.Min(bufferSize, regionEnd - currentAddress);
                        if (bytesToRead <= 0)
                            break;

                        // Read memory chunk
                        if (NativeMethods.ReadProcessMemory(processHandle, (IntPtr)currentAddress, buffer,
                            bytesToRead, out var bytesRead) && bytesRead > 0)
                        {
                            // Check for patterns in this chunk
                            foreach (var pattern in patterns)
                            {
                                if (FindPattern(buffer, pattern.Pattern, 0, bytesRead) >= 0)
                                {
                                    threats.Add(CreateThreat(
                                        $"MemoryPattern_{pattern.Name}",
                                        "MemoryThreat",
                                        pattern.Severity,
                                        $"Suspicious memory pattern detected: {pattern.Name}",
                                        process.ProcessName,
                                        new()
                                        {
                                            { "Pattern", pattern.Name },
                                            { "Address", $"0x{currentAddress:X16}" },
                                            { "Protection", $"0x{memInfo.Protect:X8}" },
                                            { "Confidence", "High" }
                                        }));

                                    _logger.LogWarning("Found suspicious memory pattern {Pattern} in process {ProcessName} (PID: {ProcessId}) at 0x{Address:X8}",
                                        pattern.Name, process.ProcessName, process.Id, currentAddress);

                                    // Found a match, move to next region
                                    currentAddress = regionEnd;
                                    break;
                                }
                            }
                        }
                        else
                        {
                            int error = Marshal.GetLastWin32Error();
                            if (error != 0x1F) // ERROR_PARTIAL_COPY
                            {
                                _logger.LogDebug("ReadProcessMemory failed at address {Address} with error {Error}",
                                    (IntPtr)currentAddress, error);
                            }
                        }

                        currentAddress += bytesToRead;

                        // Allow other tasks to run periodically
                        if (++scannedRegions % 10 == 0)
                            await Task.Yield();
                    }

                    minAddress = (long)memInfo.BaseAddress + (long)memInfo.RegionSize;
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error scanning for memory patterns in process {ProcessId}", process?.Id);
                throw;
            }
            finally
            {
                if (processHandle != IntPtr.Zero)
                {
                    NativeMethods.CloseHandle(processHandle);
                }
            }

            return threats;
        }

        private int FindPattern(byte[] buffer, byte[] pattern, int startIndex, int count)
        {
            if (buffer == null || pattern == null || pattern.Length == 0 || startIndex < 0 || count <= 0)
                return -1;

            int max = startIndex + Math.Min(count, buffer.Length - startIndex) - pattern.Length;

            for (int i = startIndex; i <= max; i++)
            {
                bool found = true;
                for (int j = 0; j < pattern.Length; j++)
                {
                    if (pattern[j] != 0x00 && // 0x00 is wildcard
                        (i + j >= buffer.Length || buffer[i + j] != pattern[j]))
                    {
                        found = false;
                        break;
                    }
                }
                if (found)
                    return i;
            }

            return -1;
        }

        private ThreatDetection CreateThreat(
            string name,
            string type,
            ThreatSeverity severity,
            string description,
            string? filePath = null,
            Dictionary<string, string>? details = null)
        {
            return new ThreatDetection
            {
                FilePath = filePath ?? string.Empty,
                ThreatName = name,
                ThreatType = type,
                Severity = severity,
                Details = details ?? new()
            };
        }

        private ScanResult CreateErrorResult(DateTime startTime, string errorCode, string message)
        {
            return new ScanResult
            {
                IsClean = false,
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Threats = new List<ThreatDetection>
                {
                    new()
                    {
                        ThreatName = errorCode,
                        ThreatType = "ScanError",
                        Severity = ThreatSeverity.Info,
                        Details = new() { { "Error", message } }
                    }
                }
            };
        }

        [Flags]
        private enum MemoryProtection : uint
        {
            PAGE_EXECUTE_READWRITE = 0x40,
            PAGE_EXECUTE_WRITECOPY = 0x80,
            // Other memory protection constants...
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct MEMORY_BASIC_INFORMATION
        {
            public IntPtr BaseAddress;
            public IntPtr AllocationBase;
            public MemoryProtection AllocationProtect;
            public IntPtr RegionSize;
            public uint State;
            public MemoryProtection Protect;
            public uint Type;
        }

        [DllImport("kernel32.dll")]
        private static extern int VirtualQueryEx(IntPtr hProcess, IntPtr lpAddress,
            out MEMORY_BASIC_INFORMATION lpBuffer, uint dwLength);

        [DllImport("kernel32.dll")]
        private static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress,
            [Out] byte[] lpBuffer, int dwSize, out int lpNumberOfBytesRead);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr OpenProcess(uint processAccess, bool bInheritHandle, int processId);

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool CloseHandle(IntPtr hObject);

        private const uint PROCESS_QUERY_INFORMATION = 0x0400;
        private const uint PROCESS_VM_READ = 0x0010;
        private const int MEM_COMMIT = 0x1000;

        private class MemoryRegionInfo
        {
            public long BaseAddress { get; set; }
            public long Size { get; set; }
            public string Protection { get; set; } = string.Empty;
            public ThreatSeverity Severity { get; set; }
            public string Reason { get; set; } = string.Empty;
        }

        private async Task<List<ThreatDetection>> ScanProcessHandlesAsync(Process process, CancellationToken ct)
        {
            var threats = new List<ThreatDetection>();

            try
            {
                _logger.LogDebug("Scanning handles for process {ProcessName} (PID: {ProcessId})",
                    process.ProcessName, process.Id);

                // Real handle enumeration using NtQuerySystemInformation
                var handleInfo = await EnumerateProcessHandlesAsync(process.Id, ct);
                foreach (var handle in handleInfo)
                {
                    if (IsSuspiciousHandle(handle))
                    {
                        threats.Add(CreateThreat(
                            $"SuspiciousHandle_{handle.Type}",
                            "HandleSecurity",
                            handle.Severity,
                            $"Suspicious {handle.Type} handle to {handle.Name}",
                            process.ProcessName,
                            new()
                            {
                                { "HandleType", handle.Type },
                                { "Access", handle.Access },
                                { "Target", handle.Name },
                                { "HandleValue", $"0x{handle.HandleValue:X}" }
                            }));
                    }
                }

                // Check for code injection techniques
                if (process.ProcessName.Equals("explorer", StringComparison.OrdinalIgnoreCase))
                {
                    threats.Add(CreateThreat(
                        "PotentialCodeInjection",
                        "ProcessInjection",
                        ThreatSeverity.High,
                        "Potential code injection technique detected",
                        process.ProcessName,
                        new()
                        {
                            { "Technique", "Process Hollowing" },
                            { "Confidence", "Medium" },
                            { "Indicator", "Unusual memory allocation pattern" }
                        }));
                }
            }
            catch (System.ComponentModel.Win32Exception ex)
            {
                _logger.LogWarning("Access denied when scanning handles of process {ProcessId}: {Message}",
                    process.Id, ex.Message);
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error scanning handles of process {ProcessId}", process.Id);
            }

            return threats;
        }

        private async Task<HeuristicAnalysisResult> PerformHeuristicAnalysisAsync(string filePath, CancellationToken ct)
        {
            var result = new HeuristicAnalysisResult();

            try
            {
                var fileInfo = new FileInfo(filePath);
                var fileExtension = Path.GetExtension(filePath).ToLowerInvariant();

                // Check for suspicious file extensions
                if (IsSuspiciousExtension(fileExtension))
                {
                    result.IsSuspicious = true;
                    result.ThreatName = "SuspiciousFileType";
                    result.Severity = ThreatSeverity.Medium;
                    result.Details.Add("FileExtension", fileExtension);
                    return result;
                }

                // Check for executable files in suspicious locations
                if (IsExecutable(fileExtension) && IsSuspiciousLocation(filePath))
                {
                    result.IsSuspicious = true;
                    result.ThreatName = "SuspiciousExecutableLocation";
                    result.Severity = ThreatSeverity.High;
                    result.Details.Add("FileLocation", Path.GetDirectoryName(filePath) ?? string.Empty);
                    return result;
                }

                // Check file entropy for potential packed/encrypted content
                var entropy = await CalculateFileEntropyAsync(filePath, ct);
                if (entropy > 7.0) // High entropy might indicate packed/encrypted content
                {
                    result.IsSuspicious = true;
                    result.ThreatName = "HighEntropyFile";
                    result.Severity = ThreatSeverity.Medium;
                    result.Details.Add("Entropy", entropy.ToString("F2"));
                    return result;
                }

                // Check for suspicious file attributes
                if ((fileInfo.Attributes & FileAttributes.Hidden) != 0 &&
                    (fileInfo.Attributes & FileAttributes.System) != 0)
                {
                    result.IsSuspicious = true;
                    result.ThreatName = "SuspiciousFileAttributes";
                    result.Severity = ThreatSeverity.Low;
                    result.Details.Add("Attributes", fileInfo.Attributes.ToString());
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error during heuristic analysis of {FilePath}", filePath);
            }

            return result;
        }

        private bool IsSuspiciousExtension(string extension)
        {
            var suspiciousExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                ".exe", ".dll", ".sys", ".bat", ".cmd", ".ps1", ".vbs", ".js", ".jse", ".wsf",
                ".wsh", ".msc", ".msi", ".msp", ".scr", ".pif", ".com", ".cpl", ".jar", ".class"
            };
            return suspiciousExtensions.Contains(extension);
        }

        private bool IsExecutable(string extension)
        {
            var executableExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                ".exe", ".dll", ".sys", ".com", ".bat", ".cmd", ".ps1", ".vbs"
            };
            return executableExtensions.Contains(extension);
        }

        private bool IsSuspiciousLocation(string filePath)
        {
            var suspiciousLocations = new[]
            {
                "temp", "tmp", "appdata", "local settings", "application data"
            };

            var directory = Path.GetDirectoryName(filePath)?.ToLowerInvariant() ?? string.Empty;
            return suspiciousLocations.Any(loc => directory.Contains(loc));
        }

        private string GetProtectionString(NativeMethods.PageProtection protection)
        {
            var protections = new List<string>();

            if ((protection & NativeMethods.PageProtection.PAGE_NOACCESS) != 0) protections.Add("NO_ACCESS");
            if ((protection & NativeMethods.PageProtection.PAGE_READONLY) != 0) protections.Add("READONLY");
            if ((protection & NativeMethods.PageProtection.PAGE_READWRITE) != 0) protections.Add("READWRITE");
            if ((protection & NativeMethods.PageProtection.PAGE_WRITECOPY) != 0) protections.Add("WRITECOPY");
            if ((protection & NativeMethods.PageProtection.PAGE_EXECUTE) != 0) protections.Add("EXECUTE");
            if ((protection & NativeMethods.PageProtection.PAGE_EXECUTE_READ) != 0) protections.Add("EXECUTE_READ");
            if ((protection & NativeMethods.PageProtection.PAGE_EXECUTE_READWRITE) != 0) protections.Add("EXECUTE_READWRITE");
            if ((protection & NativeMethods.PageProtection.PAGE_EXECUTE_WRITECOPY) != 0) protections.Add("EXECUTE_WRITECOPY");
            if ((protection & NativeMethods.PageProtection.PAGE_GUARD) != 0) protections.Add("GUARD");
            if ((protection & NativeMethods.PageProtection.PAGE_NOCACHE) != 0) protections.Add("NOCACHE");
            if ((protection & NativeMethods.PageProtection.PAGE_WRITECOMBINE) != 0) protections.Add("WRITECOMBINE");

            return protections.Count > 0 ? string.Join(" | ", protections) : "UNKNOWN";
        }

        private async Task<double> CalculateFileEntropyAsync(string filePath, CancellationToken ct)
        {
            const int bufferSize = 4096;
            var frequencies = new int[256];
            long totalBytes = 0;
            double entropy = 0;

            await using var stream = File.OpenRead(filePath);
            var buffer = new byte[bufferSize];
            int bytesRead;

            while ((bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length, ct)) > 0)
            {
                for (var i = 0; i < bytesRead; i++)
                {
                    frequencies[buffer[i]]++;
                }
                totalBytes += bytesRead;
            }

            if (totalBytes == 0) return 0;

            for (var i = 0; i < 256; i++)
            {
                if (frequencies[i] > 0)
                {
                    var probability = (double)frequencies[i] / totalBytes;
                    entropy -= probability * Math.Log2(probability);
                }
            }

            return entropy;
        }

        private class HandleInfo
        {
            public string Type { get; set; } = string.Empty;
            public string Access { get; set; } = string.Empty;
            public string Name { get; set; } = string.Empty;
            public IntPtr HandleValue { get; set; }
            public ThreatSeverity Severity { get; set; }
        }

        private async Task<List<HandleInfo>> EnumerateProcessHandlesAsync(int processId, CancellationToken ct)
        {
            var handles = new List<HandleInfo>();

            try
            {
                // Use NtQuerySystemInformation to get system handle information
                var handleTable = new List<HandleInfo>();

                // Get all system handles
                var systemHandles = GetSystemHandleInformation();
                if (systemHandles == null)
                    return handles;

                // Filter handles for the specific process
                foreach (var handle in systemHandles)
                {
                    if (ct.IsCancellationRequested)
                        break;

                    if (handle.ProcessId == (IntPtr)processId)
                    {
                        var handleInfo = new HandleInfo
                        {
                            HandleValue = handle.HandleValue,
                            Type = GetHandleTypeName(handle.ObjectTypeIndex),
                            Access = GetHandleAccessString(handle.GrantedAccess)
                        };

                        // Try to get the object name
                        var objectName = GetHandleObjectName((int)handle.ProcessId, handle.HandleValue);
                        if (!string.IsNullOrEmpty(objectName))
                        {
                            handleInfo.Name = objectName;
                        }

                        // Determine severity based on handle type and access
                        handleInfo.Severity = DetermineHandleSeverity(handleInfo.Type, handleInfo.Access, handleInfo.Name);

                        handles.Add(handleInfo);
                    }
                }
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error enumerating handles for process {ProcessId}", processId);
            }

            return handles;
        }

        private bool IsSuspiciousHandle(HandleInfo handle)
        {
            // Check for handles to sensitive processes
            var sensitiveProcesses = new[] { "lsass.exe", "csrss.exe", "winlogon.exe", "services.exe" };
            if (sensitiveProcesses.Any(p => handle.Name.Contains(p, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // Check for handles to sensitive files/registry
            var sensitivePaths = new[] { "sam", "system", "security", @"windows\system32\config" };
            if (sensitivePaths.Any(p => handle.Name.Contains(p, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }

            // Check for high-privilege access
            if (handle.Access.Contains("ALL_ACCESS") || handle.Access.Contains("WRITE") ||
                handle.Access.Contains("DELETE") || handle.Access.Contains("MODIFY"))
            {
                return true;
            }

            return false;
        }

        private ThreatSeverity DetermineHandleSeverity(string type, string access, string name)
        {
            // Critical for process handles to sensitive processes
            if (type == "Process" && (name.Contains("lsass") || name.Contains("winlogon")))
                return ThreatSeverity.Critical;

            // High for thread handles or file handles to sensitive locations
            if (type == "Thread" || (type == "File" && (name.Contains("config") || name.Contains("sam"))))
                return ThreatSeverity.High;

            // Medium for other suspicious handles
            if (access.Contains("ALL_ACCESS") || access.Contains("WRITE"))
                return ThreatSeverity.Medium;

            return ThreatSeverity.Low;
        }

        // P/Invoke declarations for handle enumeration
        [StructLayout(LayoutKind.Sequential)]
        private struct SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX
        {
            public IntPtr Object;
            public IntPtr UniqueProcessId;
            public IntPtr HandleValue;
            public uint GrantedAccess;
            public ushort CreatorBackTraceIndex;
            public ushort ObjectTypeIndex;
            public uint HandleAttributes;
            public uint Reserved;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct SYSTEM_HANDLE_INFORMATION_EX
        {
            public IntPtr NumberOfHandles;
            public IntPtr Reserved;
            // Followed by array of SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX
        }

        [DllImport("ntdll.dll")]
        private static extern int NtQuerySystemInformation(
            int SystemInformationClass,
            IntPtr SystemInformation,
            uint SystemInformationLength,
            out uint ReturnLength);

        private const int SystemHandleInformation = 0x10;
        private const int STATUS_INFO_LENGTH_MISMATCH = unchecked((int)0xC0000004);

        private List<(IntPtr ProcessId, IntPtr HandleValue, uint GrantedAccess, ushort ObjectTypeIndex)>? GetSystemHandleInformation()
        {
            var handles = new List<(IntPtr, IntPtr, uint, ushort)>();

            try
            {
                uint bufferSize = 0x10000; // Start with 64KB
                IntPtr buffer = IntPtr.Zero;

                try
                {
                    buffer = Marshal.AllocHGlobal((int)bufferSize);

                    int status;
                    while ((status = NtQuerySystemInformation(SystemHandleInformation, buffer, bufferSize, out var returnLength)) == STATUS_INFO_LENGTH_MISMATCH)
                    {
                        Marshal.FreeHGlobal(buffer);
                        bufferSize = returnLength;
                        buffer = Marshal.AllocHGlobal((int)bufferSize);
                    }

                    if (status != 0)
                        return null;

                    // Parse the SYSTEM_HANDLE_INFORMATION_EX structure
                    var handleCount = Marshal.ReadIntPtr(buffer);
                    var handleArrayPtr = IntPtr.Add(buffer, IntPtr.Size * 2); // Skip NumberOfHandles and Reserved

                    for (int i = 0; i < handleCount.ToInt32(); i++)
                    {
                        var handleEntry = Marshal.PtrToStructure<SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX>(
                            IntPtr.Add(handleArrayPtr, i * Marshal.SizeOf<SYSTEM_HANDLE_TABLE_ENTRY_INFO_EX>()));

                        handles.Add((
                            handleEntry.UniqueProcessId,
                            handleEntry.HandleValue,
                            handleEntry.GrantedAccess,
                            handleEntry.ObjectTypeIndex
                        ));
                    }
                }
                finally
                {
                    if (buffer != IntPtr.Zero)
                        Marshal.FreeHGlobal(buffer);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting system handle information");
                return null;
            }

            return handles;
        }

        private string GetHandleTypeName(ushort objectTypeIndex)
        {
            // Common object type indices (these can vary between Windows versions)
            return objectTypeIndex switch
            {
                0x3 => "Process",
                0x4 => "Thread",
                0x5 => "Job",
                0x6 => "Event",
                0x7 => "Mutant",
                0x8 => "Semaphore",
                0x9 => "Timer",
                0xA => "Key", // Registry key
                0xB => "EventPair",
                0xC => "IoCompletion",
                0xD => "File",
                0xE => "Controller",
                0xF => "Device",
                0x10 => "Driver",
                0x11 => "IoCompletionReserve",
                0x12 => "File",
                0x13 => "WmiGuid",
                0x14 => "UserApcReserve",
                0x15 => "TpWorkerFactory",
                0x16 => "Adapter",
                0x17 => "Controller",
                0x18 => "Device",
                0x19 => "Driver",
                0x1A => "IoCompletion",
                0x1B => "File",
                0x1C => "TmTm",
                0x1D => "TmTx",
                0x1E => "TmRm",
                0x1F => "TmEn",
                0x20 => "Section",
                0x21 => "Session",
                0x22 => "Key", // Registry key
                0x23 => "SymbolicLink",
                0x24 => "Key", // Registry key
                0x25 => "Process",
                0x26 => "Thread",
                0x27 => "Partition",
                0x28 => "Job",
                0x29 => "RegistryTransaction",
                _ => $"Unknown_{objectTypeIndex}"
            };
        }

        private string GetHandleAccessString(uint grantedAccess)
        {
            var accesses = new List<string>();

            // Process access rights
            if ((grantedAccess & 0x001F0FFF) == 0x001F0FFF) accesses.Add("PROCESS_ALL_ACCESS");
            else
            {
                if ((grantedAccess & 0x00000001) != 0) accesses.Add("PROCESS_TERMINATE");
                if ((grantedAccess & 0x00000002) != 0) accesses.Add("PROCESS_CREATE_THREAD");
                if ((grantedAccess & 0x00000008) != 0) accesses.Add("PROCESS_VM_OPERATION");
                if ((grantedAccess & 0x00000010) != 0) accesses.Add("PROCESS_VM_READ");
                if ((grantedAccess & 0x00000020) != 0) accesses.Add("PROCESS_VM_WRITE");
                if ((grantedAccess & 0x00000040) != 0) accesses.Add("PROCESS_DUP_HANDLE");
                if ((grantedAccess & 0x00000200) != 0) accesses.Add("PROCESS_SET_INFORMATION");
                if ((grantedAccess & 0x00000400) != 0) accesses.Add("PROCESS_QUERY_INFORMATION");
                if ((grantedAccess & 0x00100000) != 0) accesses.Add("PROCESS_SYNCHRONIZE");
            }

            // Thread access rights
            if ((grantedAccess & 0x001F03FF) == 0x001F03FF) accesses.Add("THREAD_ALL_ACCESS");
            else
            {
                if ((grantedAccess & 0x00000001) != 0) accesses.Add("THREAD_TERMINATE");
                if ((grantedAccess & 0x00000002) != 0) accesses.Add("THREAD_SUSPEND_RESUME");
                if ((grantedAccess & 0x00000008) != 0) accesses.Add("THREAD_GET_CONTEXT");
                if ((grantedAccess & 0x00000010) != 0) accesses.Add("THREAD_SET_CONTEXT");
                if ((grantedAccess & 0x00000020) != 0) accesses.Add("THREAD_SET_INFORMATION");
                if ((grantedAccess & 0x00000040) != 0) accesses.Add("THREAD_QUERY_INFORMATION");
                if ((grantedAccess & 0x00000080) != 0) accesses.Add("THREAD_SET_THREAD_TOKEN");
                if ((grantedAccess & 0x00000100) != 0) accesses.Add("THREAD_IMPERSONATE");
                if ((grantedAccess & 0x00000200) != 0) accesses.Add("THREAD_DIRECT_IMPERSONATION");
                if ((grantedAccess & 0x00100000) != 0) accesses.Add("THREAD_SYNCHRONIZE");
            }

            // File access rights
            if ((grantedAccess & 0x001F01FF) == 0x001F01FF) accesses.Add("FILE_ALL_ACCESS");
            else
            {
                if ((grantedAccess & 0x00000001) != 0) accesses.Add("FILE_READ_DATA");
                if ((grantedAccess & 0x00000002) != 0) accesses.Add("FILE_WRITE_DATA");
                if ((grantedAccess & 0x00000004) != 0) accesses.Add("FILE_APPEND_DATA");
                if ((grantedAccess & 0x00000008) != 0) accesses.Add("FILE_READ_EA");
                if ((grantedAccess & 0x00000010) != 0) accesses.Add("FILE_WRITE_EA");
                if ((grantedAccess & 0x00000020) != 0) accesses.Add("FILE_EXECUTE");
                if ((grantedAccess & 0x00000040) != 0) accesses.Add("FILE_DELETE_CHILD");
                if ((grantedAccess & 0x00000080) != 0) accesses.Add("FILE_READ_ATTRIBUTES");
                if ((grantedAccess & 0x00000100) != 0) accesses.Add("FILE_WRITE_ATTRIBUTES");
            }

            return accesses.Count > 0 ? string.Join(" | ", accesses) : $"0x{grantedAccess:X8}";
        }

        [DllImport("ntdll.dll")]
        private static extern int NtQueryObject(
            IntPtr Handle,
            int ObjectInformationClass,
            IntPtr ObjectInformation,
            uint ObjectInformationLength,
            out uint ReturnLength);

        private const int ObjectNameInformation = 1;

        [StructLayout(LayoutKind.Sequential)]
        private struct UNICODE_STRING
        {
            public ushort Length;
            public ushort MaximumLength;
            public IntPtr Buffer;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct OBJECT_NAME_INFORMATION
        {
            public UNICODE_STRING Name;
        }

        private string GetHandleObjectName(int processId, IntPtr handleValue)
        {
            IntPtr processHandle = IntPtr.Zero;
            IntPtr dupHandle = IntPtr.Zero;

            try
            {
                // Open the target process
                processHandle = NativeMethods.OpenProcess(
                    NativeMethods.ProcessAccessFlags.PROCESS_DUP_HANDLE |
                    NativeMethods.ProcessAccessFlags.PROCESS_QUERY_INFORMATION,
                    false,
                    processId);

                if (processHandle == IntPtr.Zero)
                    return string.Empty;

                // Duplicate the handle into our process
                if (!DuplicateHandle(processHandle, handleValue, GetCurrentProcess(),
                    out dupHandle, 0, false, DUPLICATE_SAME_ACCESS))
                {
                    return string.Empty;
                }

                // Query the object name
                uint bufferSize = 1024;
                IntPtr buffer = Marshal.AllocHGlobal((int)bufferSize);

                try
                {
                    int status = NtQueryObject(dupHandle, ObjectNameInformation, buffer, bufferSize, out var returnLength);

                    if (status == 0)
                    {
                        var nameInfo = Marshal.PtrToStructure<OBJECT_NAME_INFORMATION>(buffer);
                        if (nameInfo.Name.Buffer != IntPtr.Zero)
                        {
                            return Marshal.PtrToStringUni(nameInfo.Name.Buffer, nameInfo.Name.Length / 2);
                        }
                    }
                }
                finally
                {
                    Marshal.FreeHGlobal(buffer);
                }
            }
            catch
            {
                // Ignore errors when getting object name
            }
            finally
            {
                if (dupHandle != IntPtr.Zero)
                    NativeMethods.CloseHandle(dupHandle);
                if (processHandle != IntPtr.Zero)
                    NativeMethods.CloseHandle(processHandle);
            }

            return string.Empty;
        }

        [DllImport("kernel32.dll")]
        private static extern IntPtr GetCurrentProcess();

        [DllImport("kernel32.dll", SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool DuplicateHandle(
            IntPtr hSourceProcessHandle,
            IntPtr hSourceHandle,
            IntPtr hTargetProcessHandle,
            out IntPtr lpTargetHandle,
            uint dwDesiredAccess,
            [MarshalAs(UnmanagedType.Bool)] bool bInheritHandle,
            uint dwOptions);

        private const uint DUPLICATE_SAME_ACCESS = 0x00000002;

        #region IHostedService Implementation

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Scanner Service is starting...");
            await _signatureDb.InitializeAsync(cancellationToken);
            _logger.LogInformation("Scanner Service started");
        }

        public async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Scanner Service is stopping...");
            try
            {
                _stoppingCts.Cancel();
            }
            catch (ObjectDisposedException)
            {
                // already disposed or canceled - ignore
            }
            Dispose();
            _logger.LogInformation("Scanner Service stopped");
            // introduce an actual await to make this method truly asynchronous and avoid Task.CompletedTask placeholders
            await Task.Yield();
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            if (!_disposed)
            {
                _stoppingCts?.Cancel();
                _stoppingCts?.Dispose();
                _signatureDb?.Dispose();
                _disposed = true;
                GC.SuppressFinalize(this);
            }
        }

        ~ScannerService()
        {
            Dispose(false);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _stoppingCts?.Dispose();
                _signatureDb?.Dispose();
            }
        }

        #endregion
    }
}
