# Simple test for PcFutureShield Blocking System
Write-Host "=== PcFutureShield Blocking Test ===" -ForegroundColor Cyan
Write-Host ""

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "WARNING: Not running as Administrator!" -ForegroundColor Yellow
    Write-Host "Some features may not work properly without admin privileges." -ForegroundColor Yellow
    Write-Host ""
}

# Test domains
$testDomains = @{
    "Adult Content" = @("pornhub.com", "xvideos.com")
    "Gambling" = @("bet365.com", "pokerstars.com")
    "Safe Sites" = @("google.com", "microsoft.com")
}

Write-Host "Testing DNS Resolution..." -ForegroundColor Cyan
Write-Host ""

foreach ($category in $testDomains.Keys) {
    Write-Host "Category: $category" -ForegroundColor Yellow
    
    foreach ($domain in $testDomains[$category]) {
        Write-Host "  Testing $domain..." -NoNewline
        
        try {
            $result = Resolve-DnsName -Name $domain -ErrorAction Stop
            if ($result) {
                Write-Host " ACCESSIBLE" -ForegroundColor Green
            }
        }
        catch {
            Write-Host " BLOCKED" -ForegroundColor Red
        }
        
        Start-Sleep -Milliseconds 500
    }
    Write-Host ""
}

# Check hosts file
Write-Host "Checking hosts file..." -ForegroundColor Cyan
$hostsPath = "$env:SystemRoot\System32\drivers\etc\hosts"
$pcfsEntries = Get-Content $hostsPath | Select-String "PcFutureShield"

if ($pcfsEntries) {
    Write-Host "Found $($pcfsEntries.Count) PcFutureShield entries in hosts file" -ForegroundColor Yellow
} else {
    Write-Host "No PcFutureShield entries found in hosts file" -ForegroundColor Green
}

Write-Host ""
Write-Host "Test completed! Press any key to exit..." -ForegroundColor Green
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
