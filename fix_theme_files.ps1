# PowerShell script to fix theme files

$themeFiles = @(
    "PcFutureShield.UI\Themes\GlossyPurple.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyRed.xaml.cs",
    "PcFutureShield.UI\Themes\GlossyGreen.xaml.cs",
    "PcFutureShield.UI\Styles\SharedStyles.xaml.cs"
)

foreach ($file in $themeFiles) {
    if (Test-Path $file) {
        Write-Host "Fixing theme file $file..."
        
        $themeName = [System.IO.Path]::GetFileNameWithoutExtension($file) -replace '\.xaml$', ''
        
        $content = @"
using System;
using System.Windows;

namespace PcFutureShield.UI.Themes
{
    public partial class $themeName : ResourceDictionary
    {
        public $themeName()
        {
            InitializeComponent();
        }
    }
}
"@
        
        Set-Content $file $content -Encoding UTF8
        Write-Host "Fixed theme file $file"
    }
}

# Fix SharedStyles separately
if (Test-Path "PcFutureShield.UI\Styles\SharedStyles.xaml.cs") {
    $content = @"
using System;
using System.Windows;

namespace PcFutureShield.UI.Styles
{
    public partial class SharedStyles : ResourceDictionary
    {
        public SharedStyles()
        {
            InitializeComponent();
        }
    }
}
"@
    
    Set-Content "PcFutureShield.UI\Styles\SharedStyles.xaml.cs" $content -Encoding UTF8
    Write-Host "Fixed SharedStyles.xaml.cs"
}

Write-Host "All theme files fixed!"
