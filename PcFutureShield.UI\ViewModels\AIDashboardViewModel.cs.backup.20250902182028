using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Threading.Tasks;
using System.Linq;
using System.IO;
using System.Diagnostics;
using System.Security.Cryptography.X509Certificates;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class AIDashboardViewModel : BaseViewModel
    {
        private string _aiStatus = "Initializing...";
        private string _lastAnalysis = "Never";
        private int _threatsDetected = 0;
        private double _systemRiskScore = 0.0;
        private string _aiModelStatus = "Loading...";

        private readonly AdvancedAIDetectionService _aiService;

        public AIDashboardViewModel()
        {
            _aiService = ServiceLocator.Get<AdvancedAIDetectionService>();
            AIStatus = "Ready";
            LastAnalysis = "Never";
            AIModelStatus = "Active";
            AnalyzeCommand = new RelayCommand(async () => await AnalyzeSystem());
            QuickScanCommand = new RelayCommand(async () => await PerformQuickScan());
        }

        public string AIStatus
        {
            get => _aiStatus;
            set => SetProperty(ref _aiStatus, value);
        }

        public string LastAnalysis
        {
            get => _lastAnalysis;
            set => SetProperty(ref _lastAnalysis, value);
        }

        public int ThreatsDetected
        {
            get => _threatsDetected;
            set => SetProperty(ref _threatsDetected, value);
        }

        public double SystemRiskScore
        {
            get => _systemRiskScore;
            set => SetProperty(ref _systemRiskScore, value);
        }

        public string AIModelStatus
        {
            get => _aiModelStatus;
            set => SetProperty(ref _aiModelStatus, value);
        }

        public ICommand AnalyzeCommand { get; }
        public ICommand QuickScanCommand { get; }

        private async Task AnalyzeSystem()
        {
            try
            {
                AIStatus = "AI Analysis in progress...";
                var result = await _aiService.AnalyzeSystemAsync();
                ThreatsDetected = result.AIThreatsDetected + result.TraditionalThreatsDetected;

                // Calculate risk score based on threats and analysis
                double riskScore = 0.0;
                if (result.AIThreatsDetected > 0) riskScore += result.AIThreatsDetected * 0.2;
                if (result.TraditionalThreatsDetected > 0) riskScore += result.TraditionalThreatsDetected * 0.1;
                if (result.ProcessFindings.Count > 10) riskScore += 0.1;
                if (result.AutorunFindings.Count > 5) riskScore += 0.1;
                SystemRiskScore = Math.Min(riskScore, 1.0);

                LastAnalysis = DateTime.Now.ToString("g");
                AIStatus = "Analysis Complete";
            }
            catch (Exception ex)
            {
                AIStatus = "Analysis Failed";
                LastAnalysis = $"Error: {ex.Message}";
            }
        }

        private async Task PerformQuickScan()
        {
            try
            {
                AIStatus = "Quick AI Scan...";

                // Avoid fake Task.Run: yield once and run a bounded quick scan synchronously (limited to 50 processes)
                await Task.Yield();
                var processes = Process.GetProcesses();
                int threats = 0;
                double riskScore = 0.0;

                foreach (var proc in processes.Take(50)) // Limit to first 50 for quick scan
                {
                    try
                    {
                        var path = proc.MainModule?.FileName;
                        if (!string.IsNullOrEmpty(path) && File.Exists(path))
                        {
                            // Quick checks
                            var fileInfo = new FileInfo(path);
                            if (fileInfo.Length > 10_000_000) riskScore += 0.01; // Large files

                            var isSigned = IsFileSigned(path);
                            if (!isSigned) riskScore += 0.005;

                            // Check for suspicious names
                            var processName = proc.ProcessName.ToLowerInvariant();
                            if (processName.Contains("hack") || processName.Contains("exploit") || processName.Contains("malware"))
                                threats++;
                        }
                    }
                    catch { }
                }

                // Update UI-bound properties
                ThreatsDetected = threats;
                SystemRiskScore = Math.Min(riskScore, 1.0);
                LastAnalysis = DateTime.Now.ToString("g");
                AIStatus = "Quick Scan Complete";
            }
            catch (Exception ex)
            {
                AIStatus = "Scan Failed";
                LastAnalysis = $"Error: {ex.Message}";
            }
        }

        private static bool IsFileSigned(string filePath)
        {
            try
            {
                var cert = new X509Certificate2(filePath);
                return true;
            }
            catch { return false; }
        }
    }
}
