﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.ViewModels
{
    public class BrowserExtensionViewModel : BaseViewModel
    {
        private readonly BrowserExtensionService _browserExtensionService;

        private bool _isEnabled = true;
        private ObservableCollection<BlockedSite> _blockedSites;
        private ObservableCollection<DownloadAttempt> _downloadAttempts;
        private ObservableCollection<ContentWarning> _contentWarnings;
        private int _totalSitesBlocked;
        private int _totalDownloadsBlocked;
        private int _totalWarningsIssued;
        private bool _blockMaliciousSites = true;
        private bool _scanDownloads = true;
        private bool _enableContentFiltering = true;
        private bool _monitorPhishing = true;

        public BrowserExtensionViewModel(BrowserExtensionService browserExtensionService)
        {
            _browserExtensionService = browserExtensionService;
            _blockedSites = new ObservableCollection<BlockedSite>();
            _downloadAttempts = new ObservableCollection<DownloadAttempt>();
            _contentWarnings = new ObservableCollection<ContentWarning>();

            ScanForMaliciousSitesCommand = new RelayCommand(async () => await ScanForMaliciousSites());
            ViewBlockedDownloadsCommand = new RelayCommand(ViewBlockedDownloads);
            UpdateExtensionSettingsCommand = new RelayCommand(async () => await UpdateExtensionSettings());
            ClearHistoryCommand = new RelayCommand(ClearHistory);

            LoadBrowserExtensionData();
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        public ObservableCollection<BlockedSite> BlockedSites
        {
            get => _blockedSites;
            set => SetProperty(ref _blockedSites, value);
        }

        public ObservableCollection<DownloadAttempt> DownloadAttempts
        {
            get => _downloadAttempts;
            set => SetProperty(ref _downloadAttempts, value);
        }

        public ObservableCollection<ContentWarning> ContentWarnings
        {
            get => _contentWarnings;
            set => SetProperty(ref _contentWarnings, value);
        }

        public int TotalSitesBlocked
        {
            get => _totalSitesBlocked;
            set => SetProperty(ref _totalSitesBlocked, value);
        }

        public int TotalDownloadsBlocked
        {
            get => _totalDownloadsBlocked;
            set => SetProperty(ref _totalDownloadsBlocked, value);
        }

        public int TotalWarningsIssued
        {
            get => _totalWarningsIssued;
            set => SetProperty(ref _totalWarningsIssued, value);
        }

        public bool BlockMaliciousSites
        {
            get => _blockMaliciousSites;
            set => SetProperty(ref _blockMaliciousSites, value);
        }

        public bool ScanDownloads
        {
            get => _scanDownloads;
            set => SetProperty(ref _scanDownloads, value);
        }

        public bool EnableContentFiltering
        {
            get => _enableContentFiltering;
            set => SetProperty(ref _enableContentFiltering, value);
        }

        public bool MonitorPhishing
        {
            get => _monitorPhishing;
            set => SetProperty(ref _monitorPhishing, value);
        }

        public ICommand ScanForMaliciousSitesCommand { get; }
        public ICommand ViewBlockedDownloadsCommand { get; }
        public ICommand UpdateExtensionSettingsCommand { get; }
        public ICommand ClearHistoryCommand { get; }

        private void LoadBrowserExtensionData()
        {
            try
            {
                // Initialize with sample data
                BlockedSites.Clear();
                DownloadAttempts.Clear();
                ContentWarnings.Clear();

                // Add sample blocked sites
                BlockedSites.Add(new BlockedSite
                {
                    Url = "http://malicious-site.com",
                    BlockReason = "Known malware distribution",
                    BlockedTime = DateTime.Now.AddHours(-1),
                    ThreatLevel = "High"
                });

                BlockedSites.Add(new BlockedSite
                {
                    Url = "http://phishing-bank.com",
                    BlockReason = "Phishing attempt detected",
                    BlockedTime = DateTime.Now.AddMinutes(-30),
                    ThreatLevel = "Critical"
                });

                // Add sample download attempts
                DownloadAttempts.Add(new DownloadAttempt
                {
                    FileName = "trojan.exe",
                    Url = "http://suspicious-download.com/trojan.exe",
                    FileSize = 245760,
                    DownloadTime = DateTime.Now.AddMinutes(-15),
                    WasBlocked = true,
                    BlockReason = "Malware detected"
                });

                // Add sample content warnings
                ContentWarnings.Add(new ContentWarning
                {
                    Url = "http://adult-content.com",
                    WarningType = "Adult Content",
                    IssuedTime = DateTime.Now.AddMinutes(-45),
                    UserAction = "Proceeded"
                });

                TotalSitesBlocked = BlockedSites.Count;
                TotalDownloadsBlocked = DownloadAttempts.Count(d => d.WasBlocked);
                TotalWarningsIssued = ContentWarnings.Count;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading browser extension data: {ex.Message}", "Data Load Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ScanForMaliciousSites()
        {
            try
            {
                // TODO: Implement actual malicious site scanning
                MessageBox.Show("Browser extension will scan for malicious sites and update block lists.", "Scan Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                LoadBrowserExtensionData(); // Refresh data
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Malicious site scan failed: {ex.Message}", "Scan Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewBlockedDownloads()
        {
            // TODO: Implement blocked downloads viewer
            MessageBox.Show("Blocked downloads viewer will show detailed information about prevented downloads.", "Feature Coming Soon", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async Task UpdateExtensionSettings()
        {
            try
            {
                // TODO: Apply extension settings to service
                MessageBox.Show("Browser extension settings updated successfully.", "Settings Updated", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to update settings: {ex.Message}", "Update Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearHistory()
        {
            var result = MessageBox.Show("Clear all browser extension history?", "Confirm Clear", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                BlockedSites.Clear();
                DownloadAttempts.Clear();
                ContentWarnings.Clear();
                TotalSitesBlocked = 0;
                TotalDownloadsBlocked = 0;
                TotalWarningsIssued = 0;
                MessageBox.Show("Browser extension history cleared.", "History Cleared", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }

    public class BlockedSite
    {
        public string Url { get; set; } = string.Empty;
        public string BlockReason { get; set; } = string.Empty;
        public DateTime BlockedTime { get; set; }
        public string ThreatLevel { get; set; } = string.Empty;
    }

    public class DownloadAttempt
    {
        public string FileName { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public DateTime DownloadTime { get; set; }
        public bool WasBlocked { get; set; }
        public string BlockReason { get; set; } = string.Empty;
    }

    public class ContentWarning
    {
        public string Url { get; set; } = string.Empty;
        public string WarningType { get; set; } = string.Empty;
        public DateTime IssuedTime { get; set; }
        public string UserAction { get; set; } = string.Empty;
    }
}
