using System;
using System.IO;
using System.Security.Cryptography;

namespace PcFutureShield.Common.Services
{
    public static class HashingService
    {
        private const int BufferSize = 1024 * 1024; // 1 MB

        public static string ComputeSHA256(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath)) throw new ArgumentNullException(nameof(filePath));
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var sha = SHA256.Create();
            var buffer = new byte[BufferSize];
            int read;
            while ((read = stream.Read(buffer, 0, buffer.Length)) > 0)
                sha.TransformBlock(buffer, 0, read, null, 0);
            sha.TransformFinalBlock(Array.Empty<byte>(), 0, 0);
            return Convert.ToHexString(sha.Hash!);
        }

        public static string ComputeSHA512(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath)) throw new ArgumentNullException(nameof(filePath));
            using var stream = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            using var sha = SHA512.Create();
            var buffer = new byte[BufferSize];
            int read;
            while ((read = stream.Read(buffer, 0, buffer.Length)) > 0)
                sha.TransformBlock(buffer, 0, read, null, 0);
            sha.TransformFinalBlock(Array.Empty<byte>(), 0, 0);
            return Convert.ToHexString(sha.Hash!);
        }

        /// <summary>Shannon entropy (0..8). High entropy (>7.2) often indicates packed/obfuscated payloads.</summary>
        public static double ComputeFileEntropy(string filePath)
        {
            using var fs = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            var counts = new long[256];
            var buffer = new byte[BufferSize];
            long total = 0;
            int read;
            while ((read = fs.Read(buffer, 0, buffer.Length)) > 0)
            {
                total += read;
                for (int i = 0; i < read; i++) counts[buffer[i]]++;
            }
            if (total == 0) return 0.0;
            double entropy = 0.0;
            for (int i = 0; i < 256; i++)
            {
                if (counts[i] == 0) continue;
                double p = counts[i] / (double)total;
                entropy -= p * Math.Log(p, 2);
            }
            return entropy;
        }

        /// <summary>Basic PE check: "MZ" header.</summary>
        public static bool LooksLikePortableExecutable(string filePath)
        {
            Span<byte> hdr = stackalloc byte[2];
            using var fs = File.Open(filePath, FileMode.Open, FileAccess.Read, FileShare.Read);
            if (fs.Length < 2) return false;
            fs.Read(hdr);
            return hdr[0] == (byte)'M' && hdr[1] == (byte)'Z';
        }
    }
}
