using System;
using System.ComponentModel;

namespace PcFutureShield.UI.ViewModels
{
    public class OverrideRequest : INotifyPropertyChanged
    {
        private string _requestId = string.Empty;
        private string _user = string.Empty;
        private string _reason = string.Empty;
        private DateTime _requestTime;
        private string _status = string.Empty;
        private TimeSpan _duration;

        public string RequestId
        {
            get => _requestId;
            set
            {
                if (_requestId != value)
                {
                    _requestId = value;
                    OnPropertyChanged(nameof(RequestId));
                }
            }
        }

        public string User
        {
            get => _user;
            set
            {
                if (_user != value)
                {
                    _user = value;
                    OnPropertyChanged(nameof(User));
                }
            }
        }

        public string RequestedBy
        {
            get => _user;
            set
            {
                if (_user != value)
                {
                    _user = value;
                    OnPropertyChanged(nameof(RequestedBy));
                }
            }
        }

        public TimeSpan RequestedDuration
        {
            get => _duration;
            set
            {
                if (_duration != value)
                {
                    _duration = value;
                    OnPropertyChanged(nameof(RequestedDuration));
                }
            }
        }

        public string Reason
        {
            get => _reason;
            set
            {
                if (_reason != value)
                {
                    _reason = value;
                    OnPropertyChanged(nameof(Reason));
                }
            }
        }

        public DateTime RequestTime
        {
            get => _requestTime;
            set
            {
                if (_requestTime != value)
                {
                    _requestTime = value;
                    OnPropertyChanged(nameof(RequestTime));
                }
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        public TimeSpan Duration
        {
            get => _duration;
            set
            {
                if (_duration != value)
                {
                    _duration = value;
                    OnPropertyChanged(nameof(Duration));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }

    public class AdminLogEntry : INotifyPropertyChanged
    {
        private string _logId = string.Empty;
        private DateTime _timestamp;
        private string _user = string.Empty;
        private string _action = string.Empty;
        private string _details = string.Empty;
        private string _result = string.Empty;

        public string LogId
        {
            get => _logId;
            set
            {
                if (_logId != value)
                {
                    _logId = value;
                    OnPropertyChanged(nameof(LogId));
                }
            }
        }

        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged(nameof(Timestamp));
                }
            }
        }

        public string User
        {
            get => _user;
            set
            {
                if (_user != value)
                {
                    _user = value;
                    OnPropertyChanged(nameof(User));
                }
            }
        }

        public string Action
        {
            get => _action;
            set
            {
                if (_action != value)
                {
                    _action = value;
                    OnPropertyChanged(nameof(Action));
                }
            }
        }

        public string Details
        {
            get => _details;
            set
            {
                if (_details != value)
                {
                    _details = value;
                    OnPropertyChanged(nameof(Details));
                }
            }
        }

        public string Result
        {
            get => _result;
            set
            {
                if (_result != value)
                {
                    _result = value;
                    OnPropertyChanged(nameof(Result));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string name) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
    }
}
