using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using PcFutureShield.UI.Views;
using PcFutureShield.Engine.VirusScanner;
using PcFutureShield.Common.Services;
using PcFutureShield.UI.Services;

namespace PcFutureShield.UI.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly AntivirusOrchestrator _antivirusOrchestrator;
        private readonly ThreatIntelligenceService _threatIntelligenceService;
        private readonly ParentalControlService _parentalControlService;
        private readonly GamingProtectionService _gamingProtectionService;
        private readonly PcOptimizationService _pcOptimizationService;
        private readonly AdminOverrideService _adminOverrideService;
        private readonly BrowserExtensionService _browserExtensionService;
        private readonly LoggingService _logger = LoggingService.Instance;

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public event Action<string>? RequestThemeChange;

        public ICommand DashboardCommand { get; }
        public ICommand VirusScanCommand { get; }
        public ICommand EnhancedScannerCommand { get; }
        public ICommand RealTimeProtectionCommand { get; }
        public ICommand ParentalControlCommand { get; }
        public ICommand QuarantineCommand { get; }
        public ICommand AIDashboardCommand { get; }
        public ICommand GamingProtectionCommand { get; }
        public ICommand AdminOverrideCommand { get; }
        public ICommand SmartRepairCommand { get; }
        public ICommand SettingsCommand { get; }
        public ICommand LicenseManagerCommand { get; }
        public ICommand UpdatesCommand { get; }
        public ICommand ThemeSelectorCommand { get; }

        // Added commands to match XAML bindings
        public ICommand PcOptimizationCommand { get; }
        public ICommand BrowserExtensionCommand { get; }

        private object? _currentView;
        public object? CurrentView
        {
            get
            {
                _logger.LogDebug("MainViewModel", $"CurrentView getter called, returning: {_currentView?.GetType().Name ?? "null"}");
                return _currentView;
            }
            set
            {
                _logger.LogDebug("MainViewModel", $"CurrentView setter called with: {value?.GetType().Name ?? "null"}");
                _currentView = value;
                OnPropertyChanged();
                _logger.LogDebug("MainViewModel", $"CurrentView set and PropertyChanged raised");
            }
        }

        private int _themeIndex = 0;
        private readonly string[] _themes = new[] { "GlossyBlue", "GlossyGreen", "GlossyMidnightBlue", "GlossyPurple", "GlossyRed" };

        public MainViewModel()
        {
            _antivirusOrchestrator = PcFutureShield.UI.Services.ServiceLocator.Get<AntivirusOrchestrator>();
            _threatIntelligenceService = PcFutureShield.UI.Services.ServiceLocator.Get<ThreatIntelligenceService>();
            _parentalControlService = PcFutureShield.UI.Services.ServiceLocator.Get<ParentalControlService>();
            _gamingProtectionService = PcFutureShield.UI.Services.ServiceLocator.Get<GamingProtectionService>();
            _pcOptimizationService = PcFutureShield.UI.Services.ServiceLocator.Get<PcOptimizationService>();
            _adminOverrideService = PcFutureShield.UI.Services.ServiceLocator.Get<AdminOverrideService>();
            _browserExtensionService = PcFutureShield.UI.Services.ServiceLocator.Get<BrowserExtensionService>();

            DashboardCommand = new RelayCommand(() => Navigate("Dashboard"));
            VirusScanCommand = new RelayCommand(() => Navigate("VirusScan"));
            EnhancedScannerCommand = new RelayCommand(() => Navigate("EnhancedScanner"));
            RealTimeProtectionCommand = new RelayCommand(() => Navigate("RealTimeProtection"));
            ParentalControlCommand = new RelayCommand(() => Navigate("ParentalControl"));
            QuarantineCommand = new RelayCommand(() => Navigate("Quarantine"));
            AIDashboardCommand = new RelayCommand(() => Navigate("AIDashboard"));
            GamingProtectionCommand = new RelayCommand(() => Navigate("GamingProtection"));
            AdminOverrideCommand = new RelayCommand(() => Navigate("AdminOverride"));
            SmartRepairCommand = new RelayCommand(() => Navigate("SmartRepair"));
            SettingsCommand = new RelayCommand(() => Navigate("Settings"));
            LicenseManagerCommand = new RelayCommand(() => Navigate("LicenseManager"));
            UpdatesCommand = new RelayCommand(() => Navigate("Updates"));
            ThemeSelectorCommand = new RelayCommand(ExecuteThemeSelector);

            // initialize new commands
            PcOptimizationCommand = new RelayCommand(() => Navigate("PcOptimization"));
            BrowserExtensionCommand = new RelayCommand(() => Navigate("BrowserExtension"));

            Navigate("Dashboard");
        }

        private void Navigate(string viewName)
        {
            _logger.LogInfo("MainViewModel", $"Navigate called with viewName: {viewName}");
            try
            {
                switch (viewName)
                {
                    case "Dashboard":
                        _logger.LogInfo("MainViewModel", "Navigating to Dashboard (ViewModel)");
                        try
                        {
                            var dashboardVm = new DashboardViewModel(_antivirusOrchestrator, _threatIntelligenceService);
                            CurrentView = dashboardVm; // DataTemplate will render DashboardView
                            _logger.LogDebug("MainViewModel", "CurrentView set to DashboardViewModel");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"Dashboard navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("Dashboard", ex.Message);
                        }
                        break;
                    case "VirusScan":
                        try
                        {
                            var virusScanView = new VirusScanView();
                            var virusScannerAdapter = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.ViewModels.IVirusScannerService>();
                            virusScanView.DataContext = new PcFutureShield.UI.ViewModels.VirusScanViewModel(virusScannerAdapter);
                            CurrentView = virusScanView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Virus Scanner navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var virusScanView = new VirusScanView();
                            virusScanView.DataContext = new FallbackViewModel("Virus Scanner", ex.Message);
                            CurrentView = virusScanView;
                        }
                        break;
                    case "Scanner":
                        try
                        {
                            var scannerView = new ScannerView();
                            scannerView.DataContext = new PcFutureShield.UI.ViewModels.ScannerViewModel();
                            CurrentView = scannerView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Scanner navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var scannerView = new ScannerView();
                            scannerView.DataContext = new FallbackViewModel("Scanner", ex.Message);
                            CurrentView = scannerView;
                        }
                        break;
                    case "EnhancedScanner":
                        try
                        {
                            var enhancedScanView = new EnhancedScanView();
                            enhancedScanView.DataContext = new PcFutureShield.UI.ViewModels.EnhancedScanViewModel(_antivirusOrchestrator);
                            CurrentView = enhancedScanView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Enhanced Scanner navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var enhancedScanView = new EnhancedScanView();
                            enhancedScanView.DataContext = new FallbackViewModel("Enhanced Scanner", ex.Message);
                            CurrentView = enhancedScanView;
                        }
                        break;
                    case "RealTimeProtection":
                        try
                        {
                            var realtimeProtectionView = new RealtimeProtectionView();
                            realtimeProtectionView.DataContext = new PcFutureShield.UI.ViewModels.RealtimeProtectionViewModel();
                            CurrentView = realtimeProtectionView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Real-time Protection navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var realtimeProtectionView = new RealtimeProtectionView();
                            realtimeProtectionView.DataContext = new FallbackViewModel("Real-time Protection", ex.Message);
                            CurrentView = realtimeProtectionView;
                        }
                        break;
                    case "Quarantine":
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"Creating QuarantineView...");
                            var quarantineView = new QuarantineView();
                            System.Diagnostics.Debug.WriteLine($"QuarantineView created successfully");
                            quarantineView.DataContext = new PcFutureShield.UI.ViewModels.QuarantineViewModel();
                            System.Diagnostics.Debug.WriteLine($"QuarantineViewModel assigned to DataContext");
                            CurrentView = quarantineView;
                            System.Diagnostics.Debug.WriteLine($"CurrentView set to QuarantineView");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Quarantine navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var quarantineView = new QuarantineView();
                            quarantineView.DataContext = new FallbackViewModel("Quarantine", ex.Message);
                            CurrentView = quarantineView;
                        }
                        break;
                    case "AIDashboard":
                        try
                        {
                            var aiView = new PcFutureShield.UI.Views.AIDashboardView();
                            aiView.DataContext = new AIDashboardViewModel();
                            CurrentView = aiView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"AI Dashboard navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("AI Dashboard", ex.Message);
                        }
                        break;
                    case "GamingProtection":
                        try
                        {
                            var gpView = new PcFutureShield.UI.Views.GamingProtectionView();
                            gpView.DataContext = new GamingProtectionViewModel(_gamingProtectionService);
                            CurrentView = gpView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"Gaming Protection navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("Gaming Protection", ex.Message);
                        }
                        break;
                    case "AdminOverride":
                        try
                        {
                            var adminView = new PcFutureShield.UI.Views.AdminOverrideView();
                            adminView.DataContext = new AdminOverrideViewModel(_adminOverrideService);
                            CurrentView = adminView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"Admin Override navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("Admin Override", ex.Message);
                        }
                        break;
                    case "SmartRepair":
                        try
                        {
                            var smartView = new PcFutureShield.UI.Views.SmartRepairView();
                            smartView.DataContext = new SmartRepairViewModel(_pcOptimizationService);
                            CurrentView = smartView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"Smart Repair navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("Smart Repair", ex.Message);
                        }
                        break;
                    case "Settings":
                        _logger.LogInfo("MainViewModel", "Navigating to Settings (View)");
                        try
                        {
                            var settingsView = new PcFutureShield.UI.Views.SettingsView();
                            settingsView.DataContext = new SettingsViewModel();
                            CurrentView = settingsView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"Settings navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("Settings", ex.Message);
                        }
                        break;
                    case "LicenseManager":
                        try
                        {
                            var licenseView = new PcFutureShield.UI.Views.LicenseManagerView();
                            var licenseManager = ServiceLocator.Get<ILicenseManager>();
                            licenseView.DataContext = new LicenseManagerViewModel(licenseManager);
                            CurrentView = licenseView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"License Manager navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("License Manager", ex.Message);
                        }
                        break;
                    case "Updates":
                        try
                        {
                            var updatesView = new PcFutureShield.UI.Views.UpdatesView();
                            updatesView.DataContext = new UpdatesViewModel();
                            CurrentView = updatesView;
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("MainViewModel", $"Updates navigation failed: {ex.Message}", ex);
                            CurrentView = new FallbackViewModel("Updates", ex.Message);
                        }
                        break;
                    case "ParentalControl":
                        try
                        {
                            var parentalControlView = new ParentalControlView();
                            parentalControlView.DataContext = new PcFutureShield.UI.ViewModels.ParentalControlViewModel(_parentalControlService);
                            CurrentView = parentalControlView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Parental Control navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var parentalControlView = new ParentalControlView();
                            parentalControlView.DataContext = new FallbackViewModel("Parental Control", ex.Message);
                            CurrentView = parentalControlView;
                        }
                        break;
                    case "PcOptimization":
                        try
                        {
                            var pcOptView = new PcOptimizationView();
                            pcOptView.DataContext = new PcFutureShield.UI.ViewModels.PcOptimizationViewModel(_pcOptimizationService);
                            CurrentView = pcOptView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"PC Optimization navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var pcOptView = new PcOptimizationView();
                            pcOptView.DataContext = new FallbackViewModel("PC Optimization", ex.Message);
                            CurrentView = pcOptView;
                        }
                        break;
                    case "BrowserExtension":
                        try
                        {
                            var browserView = new BrowserExtensionView();
                            browserView.DataContext = new PcFutureShield.UI.ViewModels.BrowserExtensionViewModel(_browserExtensionService);
                            CurrentView = browserView;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Browser Extension navigation failed: {ex.Message}");
                            // Create view with fallback ViewModel
                            var browserView = new BrowserExtensionView();
                            browserView.DataContext = new FallbackViewModel("Browser Extension", ex.Message);
                            CurrentView = browserView;
                        }
                        break;
                    default:
                        try
                        {
                            var notifier = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>();
                            notifier.ShowError("Navigation Error", $"Unknown view: {viewName}");
                        }
                        catch
                        {
                            System.Windows.MessageBox.Show($"Unknown view: {viewName}", "Navigation Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                        }
                        break;
                }
            }
            catch (Exception ex)
            {
                try
                {
                    var notifier = PcFutureShield.UI.Services.ServiceLocator.Get<PcFutureShield.UI.Services.INotificationService>();
                    notifier.ShowError("Navigation Error", $"Failed to navigate to {viewName}: {ex.Message}");
                }
                catch
                {
                    System.Windows.MessageBox.Show($"Failed to navigate to {viewName}: {ex.Message}", "Navigation Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
        }

        private void ExecuteThemeSelector()
        {
            _themeIndex = (_themeIndex + 1) % _themes.Length;
            RequestThemeChange?.Invoke(_themes[_themeIndex]);
        }
    }

    // Fallback ViewModel for when ViewModel construction fails
    public class FallbackViewModel : INotifyPropertyChanged
    {
        private string _title;
        private string _errorMessage;

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged();
            }
        }

        public string ErrorMessage
        {
            get => _errorMessage;
            set
            {
                _errorMessage = value;
                OnPropertyChanged();
            }
        }

        public FallbackViewModel(string title, string errorMessage)
        {
            Title = title;
            ErrorMessage = errorMessage;
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
