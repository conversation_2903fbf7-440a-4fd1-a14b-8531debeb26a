using System.Windows;
using PcFutureShield.UI.ViewModels;

namespace PcFutureShield.UI.Views
{
    public partial class EventLogWindow : Window
    {
        public EventLogWindow()
        {
            InitializeComponent();
            DataContext = new EventLogViewModel();
        }
    }

		// Auto-generated event handler stub by AutoValidationTool
		private void Time(object sender, System.Windows.RoutedEventArgs e)
		{
			// TODO: implement handler logic. Keep lightweight and call into services or ViewModel.
		}
}
