using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace PcFutureShield.UI.Services
{
    public class SettingsService
    {
        private readonly string _settingsFilePath;
        private Dictionary<string, object> _settings;

        public SettingsService(string? settingsFilePath = null)
        {
            _settingsFilePath = settingsFilePath ?? Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "PcFutureShield", "settings.json");
            _settings = new Dictionary<string, object>();
            Load();
        }

        public T Get<T>(string key, T defaultValue = default!)
        {
            if (_settings.TryGetValue(key, out var value) && value is JsonElement elem)
            {
                return JsonSerializer.Deserialize<T>(elem.GetRawText())!;
            }
            if (_settings.TryGetValue(key, out value) && value is T tval)
                return tval;
            return defaultValue;
        }

        public void Set<T>(string key, T value)
        {
            _settings[key] = value!;
            Save();
        }

        public void Load()
        {
            try
            {
                if (File.Exists(_settingsFilePath))
                {
                    var json = File.ReadAllText(_settingsFilePath);
                    _settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json) ?? new();
                }
            }
            catch { _settings = new(); }
        }

        public void Save()
        {
            try
            {
                var dir = Path.GetDirectoryName(_settingsFilePath);
                if (!Directory.Exists(dir)) Directory.CreateDirectory(dir!);
                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_settingsFilePath, json);
            }
            catch { }
        }
    }
}
