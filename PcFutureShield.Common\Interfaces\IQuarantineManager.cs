using System;
using System.Collections.Generic;

namespace PcFutureShield.Common.Interfaces
{
    public interface IQuarantineManager
    {
        event EventHandler QuarantineChanged;
        int GetQuarantinedCount();
        IReadOnlyCollection<QuarantineItem> GetQuarantinedItems();
        QuarantineResult RestoreItem(string itemId);
        QuarantineResult DeleteItem(string itemId);
    }

    public class QuarantineResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
