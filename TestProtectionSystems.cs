using System;
using System.Linq;
using System.Threading.Tasks;
using PcFutureShield.Common.Services;

namespace PcFutureShield.Testing
{
    /// <summary>
    /// Console application to test all protection systems
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("PcFutureShield Protection System Tester");
            Console.WriteLine("=======================================");
            Console.WriteLine();

            // Check if running as administrator
            if (!IsRunningAsAdministrator())
            {
                Console.WriteLine("⚠️  WARNING: Not running as administrator!");
                Console.WriteLine("Some tests may fail without administrator privileges.");
                Console.WriteLine("Please run as administrator for complete testing.");
                Console.WriteLine();
            }

            try
            {
                using var tester = new ProtectionSystemTester();
                var results = await tester.RunAllTestsAsync();

                Console.WriteLine();
                Console.WriteLine("=== DETAILED RESULTS ===");

                if (results.GetPassedTests() == results.GetTotalTests())
                {
                    Console.WriteLine("🎉 ALL PROTECTION SYSTEMS ARE WORKING PERFECTLY!");
                    Console.WriteLine("Your system is fully protected against:");
                    Console.WriteLine("• Adult content and inappropriate websites");
                    Console.WriteLine("• Gambling and betting sites");
                    Console.WriteLine("• Malicious gaming applications");
                    Console.WriteLine("• Network-level threats");
                }
                else
                {
                    Console.WriteLine("⚠️  SOME PROTECTION SYSTEMS NEED ATTENTION!");
                    Console.WriteLine();
                    Console.WriteLine("Issues found:");

                    PrintFailedTests("DNS Filtering", results.DnsFilteringResults);
                    PrintFailedTests("Hosts File Blocking", results.HostsFileResults);
                    PrintFailedTests("Network Blocking", results.NetworkBlockingResults);
                    PrintFailedTests("Gaming Protection", results.GamingProtectionResults);
                    PrintFailedTests("Website Blocking", results.WebsiteBlockingResults);

                    Console.WriteLine();
                    Console.WriteLine("Recommendations:");
                    Console.WriteLine("1. Run this application as Administrator");
                    Console.WriteLine("2. Ensure PcFutureShield service is running");
                    Console.WriteLine("3. Check Windows Firewall settings");
                    Console.WriteLine("4. Verify network adapter permissions");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ CRITICAL ERROR: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("Stack trace:");
                Console.WriteLine(ex.StackTrace);
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static void PrintFailedTests(string category, TestResult result)
        {
            var failedTests = result.Tests.Where(t => !t.Passed).ToList();
            if (failedTests.Any())
            {
                Console.WriteLine($"• {category}:");
                foreach (var test in failedTests)
                {
                    Console.WriteLine($"  - {test.Name}: {test.Details}");
                }
            }
        }

        private static bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
